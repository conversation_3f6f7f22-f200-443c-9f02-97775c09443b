<template>
    <div id="app">
        <router-view />
    </div>
</template>
<script>
// 引入水印样式
import Watermark from '@/assets/js/watermark';
export default {
    name: "App",
    data() {
        // 环境变量检查，提供默认值
        const showWatermark = process.env.VUE_APP_Watermark || false;
        return {
            showsWatermark:showWatermark,
        };
    },
    created(){
       document.documentElement.style.setProperty("--el-color-primary", "rgba(192,0,0,1)");
    },
    // 页面更新的时候判断添加水印（默认所有页面添加水印
    updated(){
      if(this.$store.getters.user.truename && this.showsWatermark ==='1'){
        console.log('jr');
        Watermark.set(`${this.$store.getters.user.truename+this.$store.getters.user.preferredMobile}`)
      }else{
        Watermark.set('')
      }
    }
};
</script>
<style>
::-webkit-scrollbar {
    background: #f7f7f9;
    width: 3px; /* 纵向滚动条滑块宽度 */
    height: 15px; /* 横向滚动条滑块宽度 */
}
::-webkit-scrollbar-track-piece {
    /*滚动条背景颜色*/
    background: #f7f7f9;
}
::-webkit-scrollbar-thumb {
    /* 滑块 */
    border-radius: 1.5px;
    background: #ccc;
}
/* 也可以单独设置横向滚动条和纵向滚动条的背景颜色 */
::-webkit-scrollbar-thumb:vertical {
    /* 纵向滑块 */
    background: #ccc;
}
::-webkit-scrollbar-thumb:horizontal {
    /* 横向滑块 */
    background: #ccc;
}
* {
    scrollbar-color: #ccc #f7f7f9; /* 滑块颜色  滚动条背景颜色 */
    scrollbar-width: thin; /* 滚动条宽度有三种：thin、auto、none */
}
</style>
