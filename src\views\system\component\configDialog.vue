<template>
  <el-dialog
      :title="item.title" 
      v-dialogDrag
      :visible.sync="item.dialogVisible"
      width="50%"
      append-to-body
      :center="false"
    >
      <el-container class="contwarp">
        <el-main class="warpLeft">
            <div class="card-header" v-if="item.type==='RY'">
              <el-input v-model="searchName" placeholder="请输入姓名进行搜索" size="small" style="width:200px" clearable/>
              <el-button class="button" type="primary" style="margin-left:10px" size="small" @click="searchNameUser()">搜索</el-button>
            </div>
            <el-tree v-if="!trueBtn" class="tree1" :key="clickKey" style="height: 56vh;overflow: auto;padding-bottom: 32px;" :props="item.defaultProps || defaultProps" ref="chooseOrgTree" :default-expanded-keys="treeExpandData" :load="loadNode" lazy :node-key="item.nodeKey || 'id'"  :check-on-click-node="true">
              <!-- :expand-on-click-node="false" -->
              <template #default="{ node, data }">
                <div @click="onTreeNodeClick(node, data)" style="width:100%;height: 100%;line-height: 26px;">{{ data.orgName || data.name }}</div>
              </template>
            </el-tree>
             <el-tree class="tree2" v-if="trueBtn" :key="(clickKey + 1)" style="height: 56vh;overflow: auto;padding-bottom: 32px;" :props="item.defaultProps || defaultProps" ref="chooseOrgTree" :data="filteredTreeData" default-expand-all :highlight-current="true" :node-key="item.nodeKey || 'id'"  :check-on-click-node="true">
              <template #default="{ node, data }">
                <div @click="onTreeNodeClick(node, data)" style="width:100%;height: 100%;line-height: 26px;">{{ data.name }}</div>
              </template>
            </el-tree>
        </el-main>
        <el-aside width="320px" class="asideR">
          <!-- <h5 class="fbold">已选人员</h5> -->
          <div class="choose-department-checked">
            <div class="choose-department-item" v-for="(citem, index) in multipleSelection" :key="citem[item.nodeKey || 'id']">
              <div class="choose-department-item-text ellipsis-line-1">
                {{ citem[item.defaultProps.label || "name"] }}
              </div>
              <span class="choose-department-item-close" @click="delChoose(citem)">x</span>
            </div>
          </div>
        </el-aside>
      </el-container>
      <span slot="footer" class="dialog-footer">
        <el-button @click="item.dialogVisible = false" size="small">取消</el-button>
        <el-button type="primary" @click="handleConfirm" size="small"
          >确定</el-button
        >
      </span>
    </el-dialog>
</template> 
<script>
// import { findOneStep } from "@/api/senior";
import { findRootAndNextRoot,findSonByParentOrgId,findAllToTree } from '@/api/system/yhgl.js';
import { findOneStep, findDimUserTree } from '@/api/system/jsgl.js';

export default {
  name: "SbChooseOrg",
  props: {
    item: {
      type: Object,
      required: true
    },
    onOk: {
      type: Function
    },
  },
  created() {
      
  },
  data() {
    return {
    //   dialogVisible: false,
      defaultProps: {
        children: "children",
        label: "name",
        isLeaf: 'leaf',
      },
      treeData: [],
      multipleSelection: [],
      defaultCheckedKs: [],
      reqs: [],
      treeExpandData: [],
      searchName:"",
      trueBtn:false,
      filteredTreeData:[],
      clickKey: 0,
    };
  },
  methods: {
    //查询职务树
    searchNames(){
        this.trueBtn = true
        this.$nextTick(()=>{
          this.clickKey++;
          findAllToTree(this.searchName.trim()).then(({ data }) => {
            let newData = this.util.toTreeData(
                data,
                'id',
                'parentId',
                'id,name,parentId'
            );
            this.filteredTreeData = newData
          })
        })
    },
    //搜索人员信息
    searchNameUser(){
        if(this.searchName.trim()){
          this.trueBtn = true
          this.$nextTick(()=>{
            this.clickKey++;
            var params = {
              truename: this.searchName.trim()
            }
            findDimUserTree(params).then(({ data }) => {
              let newData = this.util.toTreeData(
                  data,
                  'id',
                  'parentId',
                  'id,parentId,name,treeLevel,treeType,orgDisplayName'
              );
              this.filteredTreeData = newData
            })
          })
          
        }else{
          this.trueBtn = false
          this.$nextTick(()=>{
            this.clickKey++;
          })
        }
    },
    loadNode(node, resolve) {
      if(this.item.type === 'RY') {
        if (node.level === 0) {
            this.treeExpandData = ['9999000100000000000']
            resolve([{
              "id": "9999000100000000000",
              "name": "北京晟壁",
            }])
        } else {
          findOneStep(node.data.id).then(({ data }) => {
            data.forEach((target) => {
              target.treeType == 'user' && Reflect.set(target, 'leaf', true)
            })
            data&&data.length > 0?resolve(data):resolve([])
          })
        }
      } else if (this.item.type == "zuzhi" || this.item.type == "ZZ") {
        if (node.level === 0) {
				  this.treeExpandData = ["1"]
          resolve([{
            "id": "1",
            "orgCode": "9999000100000000000",
					  "orgName": "北京晟壁",
          }])
        } else {
          var params = {
            orgCode: node.data.orgCode
          }
          findSonByParentOrgId(params).then(({ data }) => {
          data.forEach((target) => {
            target.treeType == 'user' && Reflect.set(target, 'leaf', true)
          })
          resolve(data)
          })
        }
      }
        
    },
    onTreeNodeClick(node, data) {
      let flag = false;
      if(this.item.type === 'RY') {
        if(data.treeType == 'user') {
          flag = true
        }
      } else if (data.treeLevel !== 0) {
        flag = true
      }
      if (flag) {
        if(this.trueBtn){
          data.orgDisplayName = node.parent.data.orgDisplayName
        }
        if (
            (!this.item.mulitple && this.item.mulitple !== false) ||
            this.item.mulitple === true
        ) {
            //多选
            this.writeNodeToLocalChecked(data);
        }else{
            this.multipleSelection = [data]
        }
      }
    },
    writeNodeToLocalChecked(node) {
      if (!this.arrayIsIncludeTheObject(this.multipleSelection, node)) {
        this.multipleSelection.push({ ...node })
      }
    },
    arrayIsIncludeTheObject(arr, obj, key = 'id') {
      return arr.some((every) => Object.is(every[key], obj[key]))
    },


    openDialog(e) {
      // this.trueBtn = false
      this.multipleSelection = [];
      this.item.dialogVisible = true;
    },
    handleConfirm() {
      this.item.dialogVisible = false;
      this.$emit("chooseData", this.multipleSelection,this.item.type);
      if (this.onOk && this.item.handleUser) {
        this.onOk(this.item,"handleUser",this.multipleSelection);
      }
    },
    delChoose(row) {
      if (
        (!this.item.mulitple && this.item.mulitple !== false) ||
        this.item.mulitple === true
      ) {
        //多选
        let arry = JSON.parse(JSON.stringify(this.multipleSelection));
        for (let i in arry) {
          if (arry[i][this.item.nodeKey || "id"] === row[this.item.nodeKey || "id"])arry.splice(i, 1);
        }
        this.multipleSelection = arry;
      } else {
        //单选
        this.multipleSelection = [];
        this.$refs.chooseOrgTree.setCheckedKeys([]);
      }
    }
  },
  created() {
    // this.getTreeData();
  }
};
</script>
<style scoped>
.icon {
  margin: 0;
}
.el-dialog__body {
  padding: 0px 20px 30px;
}
.el-main {
  padding: 0px;
  border-left: 0px solid #e0e0e0;
}
.asideR {
  border-left: 1px solid #e0e0e0;
  padding:0px 15px 0px;
}
.chooseD a {
  display: block;
  padding: 5px 0;
}

.choose-department-checked {
  width: 100%;
  box-sizing: border-box;
  padding: 10px;
  height: 500px;
  overflow-y: auto;
  border: 1px solid #e6ebf5;
}

.choose-department-item {
  min-height: 30px;
  -js-display: flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.choose-department-item:hover {
  background-color:rgb(183, 217, 245)
}

.choose-department-item-text {
  width: 80%;
  user-select: none;
}

.choose-department-item-close {
  cursor: pointer;
  font-size: 16px;
}
.contwarp{
  height: 58vh;
  overflow: hidden;
}
.warpLeft{
  height: 100%;
  overflow: hidden;
}

.warpLeft ::v-deep .el-input__validateIcon{
    display: none;
}
</style>
