<template>
  <div class="app-container" style="padding-bottom: 80px;">
    <sb-el-table :table="table" @getList="getList" @updateTableData="updateTableData" :on-ok="handleDoFun" @handleAdd="handleAdd" @editHandle="editHandle"
                 @editHandleGetRow="editHandleGetRow" @lookHandle="lookHandle" @removeHandle="removeHandle" @uploadFileList="uploadFileList">
      <template v-slot:file="{obj}">
        {{obj.row.file?.[0]?.fileName}}
      </template>
      <template v-slot:state="{obj}">
        {{obj.row.state == 0 ? '月度通报' : '活动安排'}}
      </template>
    </sb-el-table>
  </div>
</template>

<script>
import {
  monthreportadd,
  monthreportlist,
  monthreportremove,
  monthreportupdate
} from "@/api/process";
import {uploadProcessFiles} from "@/api/public";

export default {
  data() {
    return {
      title: "",
      showDialog: false,
      curComponent: "",
      pageLabel: "taskInfo",
      table: {
        modulName: "月度通报管理-月度通报", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: false, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: true, // 是否有查询条件
        queryForm: {
          inline: true,
          labelPosition: 'right',
          labelWidth: "120px",
          formItemList: [
            {label: "名称", key: "title", type: "input", class: 'c4'},
            {label: "类型", key: "state", type: "select", class: 'c4', options: [{name: '月度通报', value: '0'},{name: '活动安排', value: '1'}], value: 0},
          ],
        },
        tr: [
          {id: "title", label: "名称", prop: 'title', width: '300'},
          {id: "state", label: "类型", prop: 'state', show: 'template', template: 'state', width: '120'},
          {id: "file", label: "相关附件", prop: 'file', show: 'template', template: 'file'},
        ],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "1000px",
          labelWidth: "150px",
          inline: true,
          formItemList: [
            {class: "c12", label: "名称", key: "title", type: "input", rule: { required: true }},
            {class: "c12", label: "类型", key: "state", type: "select", options: [{name: '月度通报', value: '0'},{name: '活动安排', value: '1'}], rule: { required: true }},
            {
              class: "c12",
              label: "相关附件",
              key: "file",
              type: "sbUpload",
              btnText: "上传",
              fun: "uploadFileList",
              listType: "text",
              multiple: false,
              rule: {required: true},
              show: true,
              isfile: true,
              // accept: '.doc, .docx, .pdf'
            }
          ],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: "200",
          fixed: "right",
          data: [
            // {id: "read", name: "【查看】", fun: "lookHandle"},
            {id: "update", name: "【编辑】", fun: "editHandle"},
            {id: "delete", name: "【删除】", fun: "removeHandle"},
            {id: "add", type: "danger", name: "新增", fun: "handleAdd"},
          ],
        },
        hasOtherQueryBtn: false, //是否有其他操作
        otherQueryBtn: {
          data: [
            // {id: "export", type: "success", name: "导出", fun: "handleExport"}
          ]
        },
        hasPagination: true,
        listQuery: {page: 1, size: 10, state: '0'},
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
      optionsList: []
    }
  },
  created() {
    this.getList()
  },
  methods: {
    uploadFileList(obj) {
      uploadProcessFiles(obj.formData).then(async res => {
        obj.content.onSuccess(res, obj.content.file, []);
      }).catch(error => {
        obj.content.onError();
      });
    },
    lookHandle(row) {
      Object.assign(this.table.listFormModul, row)
    },
    editHandleGetRow(row) {
      Object.assign(this.table.listFormModul, {
        id: row.id,
        title: row.title,
        file: row.file,
        state: row.state
      })
    },
    async removeHandle(row, index) {
      await monthreportremove(row.id)
      // this.$message.success('删除成功')
      this.table.data.splice(index, 1)
    },
    async editHandle() {
      await monthreportupdate(this.table.listFormModul)
      // this.$message.success('修改管理员成功')
      this.table.dialogVisible = false
      this.getList()
    },
    async handleAdd() {
      await monthreportadd(this.table.listFormModul)
      // this.$message.success('新增管理员成功')
      this.table.dialogVisible = false
      this.getList()
    },
    // 查询列表
    getList(listQuery) {
      this.table.loading = true;
      monthreportlist(listQuery || this.table.listQuery, listQuery || this.table.listQuery).then((res) => {
        this.table.loading = false;
        this.table.data = res.data.content;
        this.table.total = res.data.totalElements;
      }).catch((err) => {
        this.table.loading = false;
      });
    },
    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    },
  }
}
</script>

<style scoped>

::v-deep .inlineB .el-form-item__content {
  flex: 1;
}

.title {
  width: 99%;
  height: 30px;
  line-height: 30px;
  padding-left: 10px;
  border-left: #39aef5 4px solid;
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.titleName {
  font-size: 15px;
  color: #333;
  font-weight: 700;
}

::v-deep .table-container {
  /* 去除项目经历左右边距 */
  padding: 0;
}
</style>