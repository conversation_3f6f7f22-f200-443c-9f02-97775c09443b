<template>
  <div class="app-container">
    <sb-el-table
      :table="table"
      @getList="getList"
      @deleteTableData="deleteTableData"
      @addTableData="addTableData"
      :on-ok="handleDoFun"
    >
      <template v-slot:BUSINESS_TITLE="{ obj }">
        <span class="toDetail" @click="handleTodo(obj)">{{
          obj.row.BUSINESS_TITLE
        }}</span>
      </template>
      <template v-slot:CREATED_TIME="{ obj }">
        <span>{{
          util.getTimeDate(obj.row.CREATED_TIME, "yyyy-MM-dd HH:mm:ss")
        }}</span>
      </template>
    </sb-el-table>

    <!-- 工单详情 -->
    <el-dialog
      title="新增"
      :visible.sync="viewD"
      v-dialogDrag
      :close-on-click-modal="false"
      append-to-body
      width="50%"
    >
      <addAdmin
        :key="cKey"
        @event="getEvent"
        @dialogClose="dialogClose"
      ></addAdmin>
    </el-dialog>
  </div>
</template>
<script>
import addAdmin from "@/components/myCom/addAdmin";
import { getWorkQuery, exportParameter } from "@/api/apply/application";
import {
  getUserAll,
  findPOrgAndCityOrg,
  addUser,
  deleteUserById,
} from "@/api/user/user";
import util from "@/assets/js/public";
import { constrainPoint } from "@fullcalendar/core/internal";
export default {
  name: "user",
  components: { addAdmin },
  data() {
    return {
      viewD: false,

      gps: {
        type: "join",
        location: "",
        pmInsType: "",
      },

      cKey: 0,
      table: {
        modulName: "applicationQuery-工单查询", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: true, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "90px",
          formItemList: [
            {
              class: "c4",
              label: "所在单位",
              key: "a",
              type: "select",
              props: {
                label: "orgName",
                value: "orgCode",
              },
            },
          ],
        },
        tr: [
          {
            id: "belongCompanyName",
            label: "所在单位",
            prop: "belongCompanyName",
          },
          {
            id: "belongDepartmentName",
            label: "所在部门",
            prop: "belongDepartmentName",
          },

          {
            id: "truename",
            label: "姓名",
            prop: "truename",
          },
          {
            id: "code",
            label: "管理员类型",
            prop: "code",
          },
        ],
        // hasSetup:true,
        // setup:[],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "400px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: "100",
          fixed: "right",
          data: [
            { id: "deleteTableData", name: "删除", fun: "deleteTableData" },
          ],
        },
        hasOtherQueryBtn: true, //是否有其他操作
        otherQueryBtn: {
          data: [
            {
              id: "export",
              type: "primary",
              name: "添加",
              fun: "addTableData",
            },
          ],
        },
        hasPagination: true,
        listQuery: { size: 10, page: 1 },
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
    };
  },
  created() {
    this.getList();
    this.findPOrgAndCityOrgs();
  },
  methods: {
    getEvent(value) {
      addUser({
        username: value.username,
        code: value.code,
      }).then((res) => {
        this.getList();
      });
    },
    // 查询列表
    getList(listQuery) {
      console.log(listQuery);
      this.table.loading = true;
      getUserAll(listQuery || this.table.listQuery)
        .then((res) => {
          this.table.loading = false;
          this.table.data = res.data.content;
          console.log(this.table.data);
          this.table.total = res.data.totalElements;
        })
        .catch((err) => {
          this.table.loading = false;
        });
    },
    findPOrgAndCityOrgs() {
      findPOrgAndCityOrg().then((res) => {
        const company = res.data.filter((item) => item.styleDictValue === "02");
        this.table.queryForm.formItemList[0].options = company;
        this.table = JSON.parse(JSON.stringify(this.table));
      });
    },

    // 查看
    handleTodo(obj) {
      // 参数
      this.gps = {
        type: "join",
        location: `${process.env.VUE_APP_APPCODE}.`,
        pmInsId: obj.row.PM_INS_ID,
        pmInsType: obj.row.PM_INS_TYPE,
        processInstId: obj.row.PROCESS_INST_ID,
      };
      this.cKey++;
      this.viewD = true;
    },
    //新增
    addTableData() {
      this.cKey++;
      this.viewD = true;
    },
    //删除
    deleteTableData(value) {
      deleteUserById(value.row.id).then((res) => {});
    },
    // 导出
    handleExport() {
      exportParameter(this.table.listQuery).then((res) => {
        if (res.data) {
          this.util.blobDownload(res.data, res.filename);
        } else {
          this.$message({
            message: "导出失败",
            type: "warning",
            duration: 1500,
          });
        }
      });
    },

    // 关闭弹框
    dialogClose() {
      this.viewD = false;
      this.getList();
    },

    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },

    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    },
  },
};
</script>
<style scoped>
/* 修改公共样式弹框样式 */
::v-deep .el-dialog__header {
  text-align: center !important;
  background: white !important;
  color: black;
  font-size: 14px;
  font-weight: bold;
  border-bottom: 1px solid #f2f2f2 !important;
}
::v-deep .el-dialog__title {
  color: black !important;
  font-size: 15.5px;
}
::v-deep .el-dialog__headerbtn .el-dialog__close {
  color: black;
}
</style>