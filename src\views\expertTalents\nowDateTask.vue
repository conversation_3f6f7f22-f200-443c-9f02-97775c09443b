<template>
  <div :class="'p10'">
    <div class="flex" style="margin-top: 8px;align-items: flex-start">
      <sb-el-form
          style="width: 700px;margin-right: 10px"
          :form="queryForm"
          v-model="listQuery"
          :from="true"
      ></sb-el-form>
      <el-button type="primary" size="small" @click="getList()" style="margin-right: 10px;">查询</el-button>
      <div v-if="isflag">
        <el-button v-if="yuqiNum === 0" type="primary" size="small" @click="handleCuiban()">一键催办</el-button>
        <el-button type="primary" size="small" @click="handleDaochu()">导出</el-button>
        <el-button type="primary" size="small" @click="handleAI()">AI周报</el-button>
      </div>
    </div>
    <el-table
        v-loading.fullscreen.lock="tableLoading"
        element-loading-text="请稍后，正在查询..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255, 255, 255, 0.5)" class="tableCustom" :class="{'nodata': !dataList.length}" :data="dataList" style="width: 100%;"
        border :span-method="arraySpanMethod" :cell-style="{background: '#ffffff'}">
      <el-table-column prop="lineType" label="条线" width="60" align="center">
      </el-table-column>
      <el-table-column prop="targetIntorAndInfo" label="目标" width="120">
        <template v-slot:default="scope">
          <span style="font-weight: bold" v-html="scope.row.targetIntor"></span>
          <br/>
          <span v-html="scope.row.targetInfo"></span>
        </template>
      </el-table-column>
      <el-table-column label="时间要求" width="100">
        <template v-slot:default="scope">
          <span v-html="scope.row.finishDataStr"></span>
        </template>
      </el-table-column>
      <el-table-column prop="actionInfo" label="举措" width="120">
        <template v-slot:default="scope">
          <span v-html="scope.row.actionInfo"></span>
        </template>
      </el-table-column>
      <el-table-column prop="taskInfo" label="任务" min-width="500">
        <template v-slot:default="scope">
          <span v-html="scope.row.taskInfo"></span>
        </template>
      </el-table-column>
      <el-table-column prop="taskHostUnitName" label="责任部门" width="120" align="center">
      </el-table-column>
      <el-table-column prop="taskHostUnitTrueName" label="部门接口人" width="90" align="center">
      </el-table-column>
      <el-table-column prop="feedbackPersonTrueName" label="任务负责人" width="90" align="center">
      </el-table-column>
      <el-table-column label="本周反馈状态" width="100" align="center">
        <template v-slot="scope">
          {{ scope.row.state == 0 ? '未反馈' : scope.row.state == 1 ? '已反馈' : scope.row.state == 2 ? '超时未反馈' : '已结项' }}
        </template>
      </el-table-column>
      <el-table-column label="本周进展情况" width="300" align="center">
        <template v-slot:default="scope">
          <div class="flex column w100 f-4" style="align-items: flex-start;padding: 5px 0;" v-if="scope.row.state == 1 || scope.row.state == 3">
            <span class="txtl">
              【{{ scope.row.isExtension == '是' ? '有延期风险' : '无延期风险' }}-{{'完成率' + scope.row.completionRate + '%'
              }}】 {{ scope.row.progress }}
            </span>
            <div v-if="scope.row.files" class="w100">
              <span v-for="file in scope.row.files" :key="file.id" class="f-3" @click="filePreview(file.id)">
                {{ file.fileName }}
              </span>
            </div>
          </div>
          <div class="inlineC" v-if="scope.row.state == 0 && scope.row.feedbackPersonUserName != $store.getters.user.username">
            <el-button size="mini" type="primary" @click.stop="handleCuiban([scope.row])">【催办】</el-button>
          </div>
        </template>
      </el-table-column>
<!--      <el-table-column prop="branchCompanySituation" label="地市分公司情况" width="300" align="center">-->
<!--        <template v-slot:default="scope">-->
<!--          {{scope.row.state == 1 || scope.row.state == 3 ? scope.row.branchCompanySituation : ''}}-->
<!--        </template>-->
<!--      </el-table-column>-->
    </el-table>
  </div>
</template>
<script>
import {
  AIdate,
  cuiban, daochu, feedexport, getfeeddate, queryCollection
} from "@/api/process";

export default {
  name: "application",
  props: {
    href: {
      type: Object,
      default() {
        return {};
      }
    },
    showBtn: {
      type: Object,
      // default(){
      // 	return {};
      // }
    },
    // 流程跟踪
    doFlowTrack: {
      type: Function
    },
    // 查看意见
    doViewComments: {
      type: Function
    },
    // 关闭
    dialogClose: {
      type: Function
    },
    types: {
      type: String,
      default: ''
    },
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      nowTime: this.util.getNow("yyyy-MM-dd hh:mm:ss"),

      dataList: [],
      spanArr: [],//二维数组，用于存放单元格合并规则
      position: 0,//用于存储相同项的开始index

      queryForm: {
        inline: true,
        labelWidth: "90px",
        labelPosition: 'right',
        formItemList: [
          {label: "反馈周期", key: "date", type: "select", class: 'c7', options: [], disabled: true},
          {label: "反馈状态", key: "type", type: "select", class: 'c5', options: [{name: '已反馈', value: '1'}, {name: '未反馈', value: '0'}, {name: '已结项', value: '3'}], clearable: true},
        ],
      },
      listQuery: {date: '', type: null},
      isflag: false,
      exportId: '',
      tableLoading: false
    }
  },
  computed: {
    yuqiNum() {
      return this.dataList?.filter(a => a.state == 2)?.length
    }
  },
  created() {
    setTimeout(() => {
      let height = window.innerHeight - 210
      let dom = document.querySelector('.tableCustom .el-table__body-wrapper')
      dom.setAttribute('style', `max-height: ${height}px;overflow: auto;`)
    }, 0)
    this.getFeedDateList()
  },
  methods: {
    async handleAI() {
      let res = await AIdate(this.exportId)

      if (res.data) {
        this.util.blobDownload(res.data, res.filename);
      } else {
        this.$message({
          message: '导出失败',
          type: 'warning',
          duration: 1500
        });
      }
    },
    async handleDaochu() {
      let res = await feedexport(this.exportId, this.listQuery.type || null)

      if (res.data) {
        this.util.blobDownload(res.data, res.filename);
      } else {
        this.$message({
          message: '导出失败',
          type: 'warning',
          duration: 1500
        });
      }
    },
    async handleCuiban(list) {
      this.$confirm(list ? `确认对当前任务负责人【${list[0].feedbackPersonTrueName}】进行催办吗？` : '请确认是否一键催办所有未反馈的任务负责人？', '提示').then(async () => {
        await cuiban(list || this.dataList.filter(a => a.state == 0 && a.feedbackPersonUserName != this.$store.getters.user.username))
      })
    },
    filePreview(id) {
      this.util.fileOpen(id)
    },
    rowspan(idx, prop) {
      this.spanArr[idx] = [];
      this.position = 0;
      this.dataList.forEach((item, index) => {
        if (index === 0) {
          this.spanArr[idx].push(1);
          this.position = 0;
        } else {
          if (this.dataList[index][prop] === this.dataList[index - 1][prop]) {
            this.spanArr[idx][this.position] += 1;//有相同项
            this.spanArr[idx].push(0); // 名称相同后往数组里面加一项0
          } else {
            this.spanArr[idx].push(1);//同列的前后两行单元格不相同
            this.position = index;
          }
        }
      })
    },
    // 合并行
    arraySpanMethod({row, column, rowIndex, columnIndex}) {
      for (let i = 0; i < 7; i++) {
        if (columnIndex === i) {
          const _row = this.spanArr[i][rowIndex];
          const _col = _row > 0 ? 1 : 0;
          // console.log('第'+rowIndex+'行','第'+i+'列','rowspan:'+_row,'colspan:'+_col)
          return {
            rowspan: _row,
            colspan: _col
          }
        }
      }
    },
    dateToText(date) {
      if (date) {
        let arr = date.split('-')
        return arr[0] + '年' + arr[1] + '月' + arr[2] + '日'
      }
      return ''
    },
    async getFeedDateList() {
      let res = await getfeeddate(0)
      this.queryForm.formItemList[0].options = res.data.map(item => {
        return {
          name: `第${item.cycle}期（${this.dateToText(item.startDate)} - ${this.dateToText(item.endDate)}）`,
          value: item.id
        }
      })
      if (res.data.length) {
        this.listQuery.date = res.data[0].id
        await this.getList()
      }
    },
    async getList() {
      this.tableLoading = true
      let list = await queryCollection(this.listQuery.date || '', this.listQuery.type || '')
      this.exportId = this.listQuery.date
      this.dataList = list?.data?.map(item => {
        item.targetIntor = this.util.htmlDecode(item.targetIntor)
        item.targetInfo = this.util.htmlDecode(item.targetInfo)
        item.actionInfo = this.util.htmlDecode(item.actionInfo)
        item.taskInfo = this.util.htmlDecode(item.taskInfo)
        return {
          intorAndInfoAndDate: item.targetIntor + item.targetInfo + item.finishDataStr,
          targetIntorAndInfo: item.targetIntor + item.targetInfo,
          ...item
        }
      });

      this.rowspan(0, 'lineType');
      this.rowspan(1, 'targetIntorAndInfo');
      this.rowspan(2, 'intorAndInfoAndDate');
      this.rowspan(3, 'actionInfo');
      this.rowspan(4, 'taskInfo')
      this.rowspan(5, 'taskHostUnitName')
      this.rowspan(6, 'taskHostUnitTrueName')
      if (list.data.length) {
        this.isflag = true
      }
      this.tableLoading = false
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }

};
</script>
<style scoped>
::v-deep .tableCustom:before {
  height: 0;
}

::v-deep .nodata:before {
  height: 1px;
}

::v-deep .el-table__header-wrapper .el-table__cell.gutter {
  width: 20px !important;
  display: block !important;
}


::v-deep .el-input.is-disabled .el-input__inner {
  background-color: #FFFFFF;
  color: #000;
}

::v-deep .el-input .el-input__suffix {
  line-height: 28px;
}

::v-deep .el-form-item__content {
  flex: 1;
}

.f-1 {
  position: relative;
}

.f-2 {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 99;
  font-size: 12px;
  line-height: 26px;
  background: #fff;
  width: 100%;
  text-align: right;
}

.f-3 {
  text-decoration: underline;
  cursor: pointer;
}

::v-deep .tableCustom .f-4 span {
  white-space: normal;
  overflow: visible;
  text-overflow: inherit;
  width: 100%;
}

::v-deep .el-input__count {
  background: rgba(247, 234, 233, 0);
}

.red {
  color: rgba(192, 0, 0, 1);
  cursor: pointer;
}

::v-deep .tableCustom .el-textarea__inner {
  padding: 0;
  border: none;
  min-height: 80px !important;
  padding-bottom: 26px;
}

::v-deep .tableCustom .cell {
  white-space: normal;
  overflow: visible;
  text-overflow: inherit;
}

::v-deep .el-table .cell > .inlineC > .el-button {
  background: transparent;
  border: none;
  margin: 0;
  font-size: 14px;
  color: rgba(192, 0, 0, 1);
  padding: 7px 0px;
  white-space: normal;
}

::v-deep .el-input-group__append {
  background-color: #fff3f3;
  border-color: #fff3f3;
}

::v-deep .upload_D {
  min-width: 100%;
}

::v-deep .upload_Btn .uploadB {
  right: 0;
  left: auto;
}

.m-title {
  width: 100%;
  padding-left: 10px;
  border-left: 4px solid rgba(192, 0, 0, 1);
  padding: 10px;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  border-bottom: 1px solid #ebebeb;
  border-right: 1px solid #ebebeb;
}

.m-4 {
  height: 32px;
  display: flex;
  align-items: center;
  padding: 0;
  border-right: 1px solid #ebebeb;
  border-bottom: 1px solid #ebebeb;
}

.m-4:first-child {
  border-top: 1px solid #ebebeb;
}

::v-deep .m-4 .el-input__inner {
  height: 30px;
  line-height: 30px;
  border: none;
}

::v-deep .m-4 .el-input__icon {
  line-height: 30px;
}

.m-2 {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100px;
  height: 100%;
  background: #DDF1FE;
  padding: 0 10px;
  font-size: 14px;
  color: #606266;
  font-weight: bold;
}

.m-5 {
  flex: 1;
}
</style>