<template>
  <div :class="'p10'">
    <div class="flex" style="margin-top: 8px;align-items: flex-start">
      <sb-el-form
          style="width: 800px;margin-right: 10px"
          :form="queryForm"
          v-model="listQuery"
          :from="true"
      ></sb-el-form>
      <el-button type="primary" size="small" @click="getList()">查询</el-button>
    </div>
    <el-table
        v-loading.fullscreen.lock="tableLoading"
        element-loading-text="请稍后，正在查询..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255, 255, 255, 0.5)" class="tableCustom" :class="{'nodata': !dataList.length}" :data="dataList"
        style="width: 100%;" border :span-method="arraySpanMethod" :cell-style="{background: '#ffffff'}">
      <el-table-column prop="lineType" label="条线" width="60" align="center">
      </el-table-column>
      <el-table-column prop="targetIntorAndInfo" label="目标" width="120">
        <template v-slot:default="scope">
          <span style="font-weight: bold" v-html="scope.row.targetIntor"></span>
          <br/>
          <span v-html="scope.row.targetInfo"></span>
        </template>
      </el-table-column>
      <el-table-column label="时间要求" width="100">
        <template v-slot:default="scope">
          <span v-html="scope.row.finishDataStr"></span>
        </template>
      </el-table-column>
      <el-table-column prop="actionInfo" label="举措" width="120">
        <template v-slot:default="scope">
          <span v-html="scope.row.actionInfo"></span>
        </template>
      </el-table-column>
      <el-table-column prop="taskInfo" label="任务" min-width="300">
        <template v-slot:default="scope">
          <span v-html="scope.row.taskInfo"></span>
        </template>
      </el-table-column>
      <el-table-column prop="taskHostUnitName" label="责任部门" width="120" align="center">
      </el-table-column>
      <el-table-column prop="taskHostUnitTrueName" label="部门接口人" width="90" align="center">
      </el-table-column>
      <el-table-column prop="feedbackPersonTrueName" label="任务负责人" width="90" align="center">
      </el-table-column>
      <el-table-column label="本周反馈状态" width="100" align="center">
        <template v-slot="scope">
          {{ scope.row.state == 0 ? '未反馈' : scope.row.state == 1 ? '已反馈' : scope.row.state == 2 ? '超时未反馈' : '已结项' }}
        </template>
      </el-table-column>
      <el-table-column label="本周进展情况" width="300" align="center">
        <template v-slot:default="scope">
          <div class="flex column w100 f-4" style="align-items: flex-start;padding: 5px 0;" v-if="scope.row.state == 1 || scope.row.state == 3">
            <span class="txtl">
              【{{ scope.row.isExtension == '是' ? '有延期风险' : '无延期风险' }}-{{'完成率' + scope.row.completionRate + '%'
              }}】 {{ scope.row.progress }}
            </span>
            <div v-if="scope.row.files" class="w100">
              <span v-for="file in scope.row.files" :key="file.id" class="f-3" @click="filePreview(file.id)">
                {{ file.fileName }}
              </span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="polishContent" label="AI润色后内容" width="300" align="center">
        <template v-slot:default="scope">
          <el-input :ref="'textareaRef'+scope.$index" type="textarea" v-model="scope.row.polishContent"
                    :autosize="{ minRows: 2, maxRows: 999}" @blur="contentBlur(scope.row, scope.$index)"></el-input>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import {
  AIdate,
  cuiban, feedexport, getfeeddate, mzlRequest, queryAIReport, queryCollection, sendexport
} from "@/api/process";

export default {
  name: "application",
  props: {
    href: {
      type: Object,
      default() {
        return {};
      }
    },
    showBtn: {
      type: Object,
      // default(){
      // 	return {};
      // }
    },
    // 流程跟踪
    doFlowTrack: {
      type: Function
    },
    // 查看意见
    doViewComments: {
      type: Function
    },
    // 关闭
    dialogClose: {
      type: Function
    },
    types: {
      type: String,
      default: ''
    },
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      nowTime: this.util.getNow("yyyy-MM-dd hh:mm:ss"),

      dataList: [],
      spanArr: [],//二维数组，用于存放单元格合并规则
      position: 0,//用于存储相同项的开始index

      queryForm: {
        inline: true,
        labelPosition: 'right',
        labelWidth: "90px",
        formItemList: [
          {label: "反馈周期", key: "date", type: "select", class: 'c6', options: []},
          {label: "任务名称", key: "taskInfo", type: "input", class: 'c6'},
        ],
      },
      listQuery: {date: '', taskInfo: null},
      exportId: '',
      tableLoading: false
    }
  },
  created() {
    setTimeout(() => {
      let height = window.innerHeight - 210
      let dom = document.querySelector('.tableCustom .el-table__body-wrapper')
      dom.setAttribute('style', `max-height: ${height}px;overflow: auto;`)
    }, 0)
    this.getFeedDateList()
  },
  methods: {
    async contentBlur(row, index) {
      if (!row.polishContent) return;
      await mzlRequest({
        url: `/action/collection/updateAi`,
        data: {
          polishContent: row.polishContent,
          applicationInfoId: row.applicationSubInfoId,
          latitudeId: this.listQuery.date
        }
      })
    },
    filePreview(id) {
      this.util.fileOpen(id)
    },
    rowspan(idx, prop) {
      this.spanArr[idx] = [];
      this.position = 0;
      this.dataList.forEach((item, index) => {
        if (index === 0) {
          this.spanArr[idx].push(1);
          this.position = 0;
        } else {
          if (this.dataList[index][prop] === this.dataList[index - 1][prop]) {
            this.spanArr[idx][this.position] += 1;//有相同项
            this.spanArr[idx].push(0); // 名称相同后往数组里面加一项0
          } else {
            this.spanArr[idx].push(1);//同列的前后两行单元格不相同
            this.position = index;
          }
        }
      })
    },
    // 合并行
    arraySpanMethod({row, column, rowIndex, columnIndex}) {
      for (let i = 0; i < 7; i++) {
        if (columnIndex === i) {
          const _row = this.spanArr[i][rowIndex];
          const _col = _row > 0 ? 1 : 0;
          // console.log('第'+rowIndex+'行','第'+i+'列','rowspan:'+_row,'colspan:'+_col)
          return {
            rowspan: _row,
            colspan: _col
          }
        }
      }
    },
    dateToText(date) {
      if (date) {
        let arr = date.split('-')
        return arr[0] + '年' + arr[1] + '月' + arr[2] + '日'
      }
      return ''
    },
    async getList() {
      if (!this.listQuery.date) {
        this.$message.warning('请先选择反馈周期')
        return
      }
      this.tableLoading = true
      let list = await queryAIReport(this.listQuery.date || '', this.listQuery.taskInfo || '')
      this.exportId = this.listQuery.date
      this.dataList = list.data.map(item => {
        item.targetIntor = this.util.htmlDecode(item.targetIntor)
        item.targetInfo = this.util.htmlDecode(item.targetInfo)
        item.actionInfo = this.util.htmlDecode(item.actionInfo)
        item.taskInfo = this.util.htmlDecode(item.taskInfo)
        return {
          intorAndInfoAndDate: item.targetIntor + item.targetInfo + item.finishDataStr,
          targetIntorAndInfo: item.targetIntor + item.targetInfo,
          ...item
        }
      });

      this.rowspan(0, 'lineType');
      this.rowspan(1, 'targetIntorAndInfo');
      this.rowspan(2, 'intorAndInfoAndDate');
      this.rowspan(3, 'actionInfo');
      this.rowspan(4, 'taskInfo')
      this.rowspan(5, 'taskHostUnitName')
      this.rowspan(6, 'taskHostUnitTrueName')
      this.tableLoading = false
      setTimeout(() => {
        this.dataList.forEach((item, index) => {
          this.$refs['textareaRef' + index].resizeTextarea()
        })
      }, 1000)
    },
    async getFeedDateList() {
      let res = await getfeeddate(1)
      this.queryForm.formItemList[0].options = res.data.map(item => {
        return {
          name: `第${item.cycle}期（${this.dateToText(item.startDate)} - ${this.dateToText(item.endDate)}）`,
          value: item.id
        }
      })
      if (res.data.length) {
        this.listQuery.date = res.data[0].id
        await this.getList()
      }
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }

};
</script>
<style scoped>
::v-deep .tableCustom:before {
  height: 0;
}

::v-deep .nodata:before {
  height: 1px;
}

::v-deep .el-table__header-wrapper .el-table__cell.gutter {
  width: 20px !important;
  display: block !important;
}

::v-deep .el-input.is-disabled .el-input__inner {
  background-color: #FFFFFF;
  color: #000;
}

::v-deep .el-input .el-input__suffix {
  line-height: 28px;
}

::v-deep .el-form-item__content {
  flex: 1;
}

.f-1 {
  position: relative;
}

.f-2 {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 99;
  font-size: 12px;
  line-height: 26px;
  background: #fff;
  width: 100%;
  text-align: right;
}

.f-3 {
  text-decoration: underline;
  cursor: pointer;
}

::v-deep .tableCustom .f-4 span {
  white-space: normal;
  overflow: visible;
  text-overflow: inherit;
  width: 100%;
}

::v-deep .el-input__count {
  background: rgba(247, 234, 233, 0);
}

.red {
  color: rgba(192, 0, 0, 1);
  cursor: pointer;
}

::v-deep .tableCustom .el-textarea__inner {
  padding: 10px 0;
  border: none;
  min-height: 80px !important;
}

::v-deep .tableCustom .cell {
  white-space: normal;
  overflow: visible;
  text-overflow: inherit;
}

::v-deep .el-table .cell > .inlineC > .el-button {
  background: transparent;
  border: none;
  margin: 0;
  font-size: 14px;
  color: rgba(192, 0, 0, 1);
  padding: 7px 0px;
  white-space: normal;
}

::v-deep .el-input-group__append {
  background-color: #fff3f3;
  border-color: #fff3f3;
}

::v-deep .upload_D {
  min-width: 100%;
}

::v-deep .upload_Btn .uploadB {
  right: 0;
  left: auto;
}

</style>