<template>
  <div class="party-screen-container">
    <!-- 顶部标题栏 -->
    <div class="header-section">
      <div class="header-left">
        <div class="date-time">
          {{ currentDate }} {{ currentWeek }} {{ currentTime }}
        </div>
      </div>
      <div class="header-center">
        <img
          src="@/assets/images/djptScreen/logo.png"
          alt="河南移动党建信息平台"
          class="logo-img"
        />
        <div class="search-box">
          <el-input
            v-model="searchText"
            placeholder=""
            suffix-icon="el-icon-search"
            size="mini"
          >
          </el-input>
        </div>
      </div>
      <div class="header-right">
        <div class="user-info">
          <i class="el-icon-user-solid"></i>
          <span>累计访问人数: {{ userCount }}</span>
        </div>
      </div>
    </div>

    <!-- 主体内容区域 -->
    <div class="main-content">
      <!-- 左栏 - 基础党建 -->
      <div class="left-panel">
        <div class="panel-title">
          <img src="@/assets/images/djptScreen/jcdj-text.png" alt="基础党建" />
        </div>

        <!-- 年度数据统计 -->
        <div class="stats-section">
          <div class="section-title">
            <div class="title-container">
              <img
                src="@/assets/images/djptScreen/arrow.png"
                alt=""
                class="arrow"
              />
              <img
                src="@/assets/images/djptScreen/ndsjtj.png"
                class="text-title"
                alt="年度数据统计"
              />
            </div>
            <div class="more-link-container">
              <span class="more-link">查看更多 ></span>
            </div>
          </div>
          <div class="stats-cards">
            <div class="stat-card red">
              <div class="stat-content">
                <div class="stat-number">521</div>
                <div class="stat-label">支部近期学习重点数</div>
              </div>
            </div>
            <div class="stat-card orange">
              <div class="stat-content">
                <div class="stat-number">234</div>
                <div class="stat-label">省公司第一议题数量</div>
              </div>
            </div>
            <div class="stat-card blue">
              <div class="stat-content">
                <div class="stat-number">457</div>
                <div class="stat-label">分公司第一议题数量</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 党建资源科学 -->
        <div class="stats-section">
          <div class="section-title">
            <div class="title-container">
              <img
                src="@/assets/images/djptScreen/arrow.png"
                alt=""
                class="arrow"
              />
              <img
                src="@/assets/images/djptScreen/djzygx.png"
                class="text-title"
                alt="党建资源共享"
              />
            </div>
            <div class="more-link-container">
              <span class="more-link">查看更多 ></span>
            </div>
          </div>
          <div class="resource-grid">
            <div
              class="resource-item"
              v-for="item in resourceItems"
              :key="item.id"
            >
              <div class="resource-icon">
                <img
                  :src="require('@/assets/images/djptScreen/' + item.icon)"
                  alt=""
                />
              </div>
              <div class="resource-label">{{ item.label }}</div>
            </div>
          </div>
        </div>

        <!-- 党员突击队 -->
        <div class="stats-section">
          <div class="section-title">
            <div class="title-container">
              <img
                src="@/assets/images/djptScreen/arrow.png"
                alt=""
                class="arrow"
              />
              <img
                src="@/assets/images/djptScreen/dytjd.png"
                class="text-title"
                alt="党员突击队"
              />
            </div>
            <div class="more-link-container">
              <span class="more-link">查看更多 ></span>
            </div>
          </div>
          <div class="party-team-content">
            <div class="team-carousel">
              <div class="swiper-container" ref="teamSwiper">
                <div class="swiper-wrapper">
                  <div
                    class="swiper-slide"
                    v-for="(item, index) in partyTeamData"
                    :key="index"
                  >
                    <img :src="item.image" :alt="item.title" class="team-img" />
                  </div>
                </div>
                <!-- 分页器 -->
                <div class="swiper-pagination"></div>
              </div>
            </div>
            <div class="team-info">
              <div class="team-title">{{ currentTeamInfo.description }}</div>
              <div class="team-meta">
                <span class="team-author">
                  <i class="el-icon-user-solid"></i>
                  {{ currentTeamInfo.author }}
                </span>
                <span class="team-date">
                  <i class="el-icon-time"></i>
                  {{ currentTeamInfo.date }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- AI+基础党建专区 -->
        <div class="stats-section ai-section">
          <div class="section-title">
            <div class="title-container ai-info">
              <img
                src="@/assets/images/djptScreen/qizi.png"
                alt=""
                class="arrow"
              />
              <img
                src="@/assets/images/djptScreen/aidjzq.png"
                class="text-title"
                alt="AI+基础党建专区"
              />
            </div>
          </div>
          <div class="ai-grid">
            <div class="ai-card">
              <img
                src="@/assets/images/djptScreen/ckzuzs.png"
                alt="查重工作助手"
              />
            </div>
            <div class="ai-card">
              <img
                src="@/assets/images/djptScreen/hyjlzs.png"
                alt="会议记录助手"
              />
            </div>
            <div class="ai-card">
              <img
                src="@/assets/images/djptScreen/sxhbzs.png"
                alt="思想汇报稽核助手"
              />
            </div>
            <div class="ai-card">
              <img
                src="@/assets/images/djptScreen/dtzdzs.png"
                alt="党团阵地校验助手"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 中栏 - 党建强链 -->
      <div class="center-panel">
        <div class="panel-title">
          <img src="@/assets/images/djptScreen/djql-text.png" alt="党建强链" />
        </div>

        <!-- 豫起奋发 -->
        <div class="stats-section">
          <div class="section-title">
            <div class="title-container">
              <img
                src="@/assets/images/djptScreen/arrow.png"
                alt=""
                class="arrow"
              />
              <img
                src="@/assets/images/djptScreen/yqff.png"
                class="text-title"
                alt="豫起奋发"
              />
            </div>
            <div class="more-link-container">
              <span class="more-link">查看更多 ></span>
            </div>
          </div>
          <div class="yqff-carousel">
            <div class="swiper-container" ref="yqffSwiper">
              <div class="swiper-wrapper">
                <div
                  class="swiper-slide"
                  v-for="(chunk, chunkIndex) in getYqffChunks"
                  :key="chunkIndex"
                >
                  <div class="message-images">
                    <div
                      class="message-item"
                      v-for="item in chunk"
                      :key="item.id"
                      :class="{ large: item.isLarge }"
                    >
                      <img :src="item.image" :alt="item.title" />
                      <div class="message-desc">
                        {{ item.description }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 分页器 -->
              <!-- <div class="swiper-pagination yqff-pagination"></div> -->
            </div>
          </div>
        </div>

        <!-- 豫起协同 -->
        <div class="stats-section">
          <div class="section-title cooperation-flex">
            <div class="title-container">
              <img
                src="@/assets/images/djptScreen/arrow.png"
                alt=""
                class="arrow"
              />
              <img
                src="@/assets/images/djptScreen/yqxt.png"
                class="text-title"
                alt="豫起协同"
              />
            </div>
            <div class="cooperation-nav">
                <div @click="handleCooperationClick(index)" :class="{'active': currentCooperationIndex === index}" class="cooperation-nav-item" v-for="(item, index) in cooperationNav" :key="index">
                    <img :src="require('@/assets/images/djptScreen/' + item.icon)" :alt="item.title" />
                </div>
            </div>
           
          </div>
          <div class="cooperation-container">
            <!-- 左侧轮播图 -->
            <div class="cooperation-carousel">
              <div class="swiper-container" ref="cooperationSwiper">
                <div class="swiper-wrapper">
                  <div
                    class="swiper-slide"
                    v-for="(item, index) in cooperationData"
                    :key="index"
                  >
                    <img
                      :src="item.image"
                      :alt="item.title"
                      class="cooperation-image"
                    />
                  </div>
                </div>
                <!-- 分页器 -->
                <div class="swiper-pagination cooperation-pagination"></div>
              </div>
            </div>

            <!-- 右侧信息展示 -->
            <div class="cooperation-info">
              <div class="cooperation-news-list">
                 <div class="more-link-container" style="width: 100%;text-align: right;">
                  <span class="more-link" style="text-align: right;font-size: 12px;">查看更多 ></span>
                </div>
                <div
                  class="cooperation-news-item"
                  v-for="(news, index) in currentCooperationInfo.news"
                  :key="news.id"
                >
                  <div class="news-content">
                    {{ index + 1 }}. {{ news.content }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 豫起赋能 -->
        <div class="stats-section">
          <div class="section-title">
            <div class="title-container">
              <img
                src="@/assets/images/djptScreen/arrow.png"
                alt=""
                class="arrow"
              />
              <img
                src="@/assets/images/djptScreen/yqfn.png"
                class="text-title"
                alt="豫起赋能"
              />
            </div>
            <div class="more-link-container">
              <span class="more-link">查看更多 ></span>
            </div>
          </div>
          <div class="interpretation-carousel">
            <div class="swiper-container" ref="interpretationSwiper">
              <div class="swiper-wrapper">
                <div
                  class="swiper-slide"
                  v-for="(chunk, chunkIndex) in getInterpretationChunks"
                  :key="chunkIndex"
                >
                  <div class="interpretation-grid">
                    <div
                      class="interpretation-item"
                      v-for="item in chunk"
                      :key="item.id"
                    >
                      <img :src="item.image" :alt="item.title" />
                      <div class="interpretation-desc">
                        {{ item.description }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 分页器 -->
            </div>
          </div>
        </div>

        <!-- AI+党建强链专区 -->
        <div class="stats-section ai-section">
          <div class="section-title">
            <div class="title-container ai-info">
              <img
                src="@/assets/images/djptScreen/qizi.png"
                alt=""
                class="arrow"
              />
              <img
                src="@/assets/images/djptScreen/aiqlzq.png"
                class="text-title"
                alt="AI+党建强链专区"
              />
            </div>
          </div>
          <div class="ai-grid">
            <div class="ai-card">
              <img
                src="@/assets/images/djptScreen/djywhx.png"
                alt="党建业务考核画像"
              />
            </div>
            <div class="ai-card">
              <img
                src="@/assets/images/djptScreen/djzdy.png"
                alt="党建指导员<br>问题解决稽核"
              />
            </div>
            <div class="ai-card">
              <img
                src="@/assets/images/djptScreen/ddzwzb.png"
                alt="担当作为周报"
              />
            </div>
            <div class="ai-card">
              <img
                src="@/assets/images/djptScreen/gjcczb.png"
                alt="更加出彩周报"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 右栏 - 宣传文化 -->
      <div class="right-panel">
        <div class="panel-title">
          <img src="@/assets/images/djptScreen/xcwh-text.png" alt="宣传文化" />
        </div>

        <!-- 荣誉时刻 -->
        <div class="stats-section">
          <div class="section-title">
            <div class="title-container">
              <img
                src="@/assets/images/djptScreen/arrow.png"
                alt=""
                class="arrow"
              />
              <img
                src="@/assets/images/djptScreen/rysk.png"
                class="text-title"
                alt="荣誉时刻"
              />
            </div>
            <div class="more-link-container">
              <span class="more-link">查看更多 ></span>
            </div>
          </div>
          <div class="honor-carousel">
            <div class="swiper-container" ref="honorSwiper">
              <div class="swiper-wrapper">
                <div
                  class="swiper-slide"
                  v-for="(chunk, chunkIndex) in getHonorChunks"
                  :key="chunkIndex"
                >
                  <div class="honor-grid">
                    <div
                      class="honor-item"
                      v-for="item in chunk"
                      :key="item.id"
                    >
                      <img :src="item.image" :alt="item.title" />
                      <div class="honor-info">
                        <div class="honor-title">{{ item.shortTitle }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 分页器 -->
              <!-- <div class="swiper-pagination honor-pagination"></div> -->
            </div>
          </div>
        </div>

        <!-- 党建视频 -->
        <div class="stats-section">
          <div class="section-title">
            <div class="title-container">
              <img
                src="@/assets/images/djptScreen/arrow.png"
                alt=""
                class="arrow"
              />
              <img
                src="@/assets/images/djptScreen/djsp.png"
                class="text-title"
                alt="党建视频"
              />
            </div>
            <div class="more-link-container">
              <span class="more-link">查看更多 ></span>
            </div>
          </div>
          <div class="video-carousel">
            <div class="swiper-container" ref="videoSwiper">
              <div class="swiper-wrapper">
                <div
                  class="swiper-slide"
                  v-for="(chunk, chunkIndex) in getVideoChunks"
                  :key="chunkIndex"
                >
                  <div class="video-grid">
                    <div
                      class="video-item"
                      v-for="item in chunk"
                      :key="item.id"
                    >
                      <video
                        :src="item.videoUrl"
                        :poster="item.poster"
                        controls
                        preload="metadata"
                        class="party-video"
                      >
                        您的浏览器不支持视频播放
                      </video>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 分页器 -->
            </div>
          </div>
        </div>

        <!-- “豫见”宣讲-->
        <div class="stats-section">
          <div class="section-title">
            <div class="title-container">
              <img
                src="@/assets/images/djptScreen/arrow.png"
                alt=""
                class="arrow"
              />
              <img
                src="@/assets/images/djptScreen/yjxj.png"
                class="text-title"
                alt="“豫见”宣讲"
              />
            </div>
            <div class="more-link-container">
              <span class="more-link">查看更多 ></span>
            </div>
          </div>
          <div class="member-news-content">
            <div class="item-text">
              <div class="cycle"></div>
              <div
                class="text-info"
                title="以系统观念推进国企党建质量提升的思考与探索——基于中国移动陕西公以系统观念推进国企党建质量提升的思考与探索——基于中国移动陕西公司实践司实践"
              >
                以系统观念推进国企党建质量提升的思考与探索——基于中国移动陕西公以系统观念推进国企党建质量提升的思考与探索——基于中国移动陕西公司实践司实践
              </div>
            </div>
            <div class="item-text">
              <div class="cycle"></div>
              <div
                class="text-info"
                title="以系统观念推进国企党建质量提升的思考与探索——基于中国移动陕西公以系统观念推进国企党建质量提升的思考与探索——基于中国移动陕西公司实践司实践"
              >
                以系统观念推进国企党建质量提升的思考与探索——基于中国移动陕西公以系统观念推进国企党建质量提升的思考与探索——基于中国移动陕西公司实践司实践
              </div>
            </div>
            <div class="item-text">
              <div class="cycle"></div>
              <div
                class="text-info"
                title="以系统观念推进国企党建质量提升的思考与探索——基于中国移动陕西公以系统观念推进国企党建质量提升的思考与探索——基于中国移动陕西公司实践司实践"
              >
                以系统观念推进国企党建质量提升的思考与探索——基于中国移动陕西公以系统观念推进国企党建质量提升的思考与探索——基于中国移动陕西公司实践司实践
              </div>
            </div>
          </div>
        </div>

        <!-- AI+宣传文化专区 -->
        <div class="stats-section ai-section">
          <div class="section-title">
            <div class="title-container ai-info">
              <img
                src="@/assets/images/djptScreen/qizi.png"
                alt=""
                class="arrow"
              />
              <img
                src="@/assets/images/djptScreen/aiwhzq.png"
                class="text-title"
                alt="AI+宣传文化专区"
              />
            </div>
            <div class="more-link-container">
              <span class="more-link">查看更多 ></span>
            </div>
          </div>
          <div class="ai-grid">
            <div class="ai-card">
              <img
                src="@/assets/images/djptScreen/xcwhch.png"
                alt="宣传文案策划"
              />
            </div>
            <div class="ai-card">
              <img
                src="@/assets/images/djptScreen/wzznjd.png"
                alt="文字智能校对"
              />
            </div>
            <div class="ai-card">
              <img
                src="@/assets/images/djptScreen/xcwgrs.png"
                alt="宣传文稿润色"
              />
            </div>
            <div class="ai-card">
              <img
                src="@/assets/images/djptScreen/xwxxxz.png"
                alt="新闻信息写作"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右下角AI助手 -->
    <div class="ai-assistant">
      <img src="@/assets/images/screen/ai.gif" alt="AI助手" @click="openAi" />
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import "@/assets/js/rem.js";
import Swiper, { Navigation, Pagination, Autoplay, EffectFade } from "swiper";
import "swiper/swiper-bundle.min.css";
Swiper.use([Navigation, Pagination, Autoplay, EffectFade]);

export default {
  name: "PartyScreen",
  data() {
    return {
      // 时间相关
      currentDate: "",
      currentWeek: "",
      currentTime: "",
      timeTimer: null,

      // 搜索和用户信息
      searchText: "",
      userCount: 1125,

      // 党建资源
      resourceItems: [
        { id: 1, label: "党章党规", icon: "dzdg.png", color: "#ff6b6b" },
        { id: 2, label: "巡视巡察", icon: "xsxc.png", color: "#4ecdc4" },
        { id: 3, label: "制度文件", icon: "zdwj.png", color: "#45b7d1" },
        { id: 4, label: "热点问题", icon: "rdwt.png", color: "#f9ca24" },
        { id: 5, label: "集团优秀实践", icon: "jtxy.png", color: "#6c5ce7" },
        { id: 6, label: "红色教育基地", icon: "hsjy.png", color: "#fd79a8" },
        { id: 7, label: "团青统战", icon: "tqtz.png", color: "#fdcb6e" },
        { id: 8, label: "培训资料", icon: "pxzl.png", color: "#74b9ff" },
      ],

      // 党员突击队数据
      partyTeamData: [
        {
          id: 1,
          image: require("@/assets/images/screen/tu1.jpg"),
          title: "项目简介：深化场景运营和加快产品拓展",
          description:
            "提升商客价值套餐贡献，落地举措：1、对1023个商客场景区域全力包提升商客价值套餐贡献，落地举措：1、对1023个商客场景区域全力包，其中沿街由57个高销售销，其中沿街由57个高销售销...",
          author: "刘少萍",
          date: "2025-07-01 15:15:18",
        },
        {
          id: 2,
          image: require("@/assets/images/screen/tu1.jpg"),
          title: "党员先锋模范作用发挥专项行动",
          description:
            "提升商客价值套餐贡献，落地举措：1、对1023个商客场景区域全力包提升商客价值套餐贡献，落地举措：1、对1023个商客场景区域全力包，其中沿街由57个高销售销，其中沿街由57个高销售销...",
          author: "张明华",
          date: "2025-07-02 10:30:25",
        },
        {
          id: 3,
          image: require("@/assets/images/screen/tu1.jpg"),
          title: "基层党组织建设创新实践",
          description:
            "提升商客价值套餐贡献，落地举措：1、对1023个商客场景区域全力包提升商客价值套餐贡献，落地举措：1、对1023个商客场景区域全力包，其中沿街由57个高销售销，其中沿街由57个高销售销...",
          author: "李文静",
          date: "2025-07-03 14:20:12",
        },
      ],

      // 当前显示的团队信息
      currentTeamInfo: {
        title: "项目简介：深化场景运营和加快产品拓展",
        description:
          "提升商客价值套餐贡献，落地举措：1、对1023个商客场景区域全力包提升商客价值套餐贡献，落地举措：1、对1023个商客场景区域全力包，其中沿街由57个高销售销，其中沿街由57个高销售销...",
        author: "刘少萍",
        date: "2025-07-01 15:15:18",
      },

      // Swiper实例
      teamSwiper: null,
      yqffSwiper: null,
      honorSwiper: null,
      interpretationSwiper: null,
      videoSwiper: null,
      cooperationSwiper: null,

      // 豫起奋发数据
      yqffData: [
        {
          id: 1,
          image: require("@/assets/images/screen/tu1.jpg"),
          title: "公司党委深入学习贯彻党的二十大精神 实现高质量发展",
          description: "公司党委深入学习贯彻党的二十大精神 实现高质量发展",
          date: "2024-12-01",
          isLarge: true,
        },
        {
          id: 2,
          image: require("@/assets/images/screen/tu1.jpg"),
          title: "公司召开基层党委书记述职评议大会 全面提升党建质量",
          description: "公司召开基层党委书记述职评议大会 全面提升党建质量",
          date: "2024-11-28",
          isLarge: false,
        },
        {
          id: 3,
          image: require("@/assets/images/screen/tu1.jpg"),
          title: "党建引领促发展 凝心聚力谱新篇",
          description: "党建引领促发展 凝心聚力谱新篇",
          date: "2024-11-25",
          isLarge: true,
        },
        {
          id: 4,
          image: require("@/assets/images/screen/tu1.jpg"),
          title: "深化党建工作创新 推动企业转型升级",
          description: "深化党建工作创新 推动企业转型升级",
          date: "2024-11-20",
          isLarge: false,
        },
      ],

      // 荣誉时刻数据
      honorData: [
        {
          id: 1,
          image: require("@/assets/images/screen/tu1.jpg"),
          title: '2024年度获评"先进基层党组织"荣誉称号',
          shortTitle: "先进基层党组织",
          date: "2024年度",
        },
        {
          id: 2,
          image: require("@/assets/images/screen/tu1.jpg"),
          title: '2024年度获评"优秀共产党员"荣誉称号',
          shortTitle: "优秀共产党员",
          date: "2024年度",
        },
        {
          id: 3,
          image: require("@/assets/images/screen/tu1.jpg"),
          title: '2024年度获评"党建工作先进单位"荣誉称号',
          shortTitle: "党建工作先进单位",
          date: "2024年度",
        },
        {
          id: 4,
          image: require("@/assets/images/screen/tu1.jpg"),
          title: '2024年度获评"红旗党支部"荣誉称号',
          shortTitle: "红旗党支部",
          date: "2024年度",
        },
      ],

      // 豫起赋能数据
      interpretationData: [
        {
          id: 1,
          image: require("@/assets/images/screen/tu1.jpg"),
          title: "党的二十大精神学习解读",
          description:
            "深入学习贯彻党的二十大精神，全面理解新时代新征程的使命任务",
          date: "2024-12-01",
        },
        {
          id: 2,
          image: require("@/assets/images/screen/tu1.jpg"),
          title: "习近平新时代中国特色社会主义思想",
          description: "系统学习习近平新时代中国特色社会主义思想的核心要义",
          date: "2024-11-28",
        },
        {
          id: 3,
          image: require("@/assets/images/screen/tu1.jpg"),
          title: "党史学习教育常态化长效化",
          description: "推动党史学习教育常态化长效化，传承红色基因",
          date: "2024-11-25",
        },
        {
          id: 4,
          image: require("@/assets/images/screen/tu1.jpg"),
          title: "新时代党的建设总要求",
          description: "全面从严治党，推进新时代党的建设新的伟大工程",
          date: "2024-11-20",
        },
        {
          id: 5,
          image: require("@/assets/images/screen/tu1.jpg"),
          title: "党风廉政建设和反腐败斗争",
          description: "持之以恒正风肃纪，一体推进不敢腐不能腐不想腐",
          date: "2024-11-15",
        },
        {
          id: 6,
          image: require("@/assets/images/screen/tu1.jpg"),
          title: "基层党组织建设标准化",
          description: "推进基层党组织建设标准化规范化，提升组织力",
          date: "2024-11-10",
        },
      ],

      // 党建视频数据
      videoData: [
        {
          id: 1,
          videoUrl: "https://www.w3schools.com/html/mov_bbb.mp4",
          poster: require("@/assets/images/screen/tu1.jpg"),
          title: "党的二十大精神宣讲视频",
        },
        {
          id: 2,
          videoUrl: "https://www.w3schools.com/html/mov_bbb.mp4",
          poster: require("@/assets/images/screen/tu1.jpg"),
          title: "习近平总书记重要讲话精神学习",
        },
        {
          id: 3,
          videoUrl: "https://www.w3schools.com/html/mov_bbb.mp4",
          poster: require("@/assets/images/screen/tu1.jpg"),
          title: "党史学习教育专题片",
        },
        {
          id: 4,
          videoUrl: "https://www.w3schools.com/html/mov_bbb.mp4",
          poster: require("@/assets/images/screen/tu1.jpg"),
          title: "新时代党的建设纪录片",
        },
      ],

      // 豫起协同数据
      cooperationData: [
        {
          id: 1,
          image: require("@/assets/images/screen/tu1.jpg"),
          title: "联合党建共建活动",
          news: [
            {
              id: 1,
              title: "19月25日，联合铁通公司党人力资源部党支部开展共建活动",
              content:
                "2河南公司联合铁通河南分公司党支部开展共建活动，3河南公司联合铁通河南分公司党支部开展共建活动。",
              date: "2024-12-01",
            },
            {
              id: 2,
              title: "党建共建促发展，携手共进谱新篇",
              content:
                "深化党建共建合作，推动双方在党建工作、业务发展等方面的深度融合。",
              date: "2024-11-28",
            },
            {
              id: 3,
              title: "基层党组织结对共建签约仪式成功举办",
              content: "进一步加强基层党组织建设，促进党建工作创新发展。",
              date: "2024-11-25",
            },
          ],
        },
        {
          id: 2,
          image: require("@/assets/images/screen/tu1.jpg"),
          title: "党建联盟合作交流",
          news: [
            {
              id: 1,
              title: "党建联盟工作座谈会顺利召开",
              content:
                "各成员单位就党建工作经验进行深入交流，共同探讨党建创新路径。",
              date: "2024-12-02",
            },
            {
              id: 2,
              title: "联盟党建品牌创建工作推进会",
              content: "统一部署联盟党建品牌创建工作，提升党建工作整体水平。",
              date: "2024-11-30",
            },
            {
              id: 3,
              title: "党建联盟志愿服务活动启动",
              content: "组织开展联合志愿服务活动，发挥党员先锋模范作用。",
              date: "2024-11-27",
            },
          ],
        },
        {
          id: 3,
          image: require("@/assets/images/screen/tu1.jpg"),
          title: "区域党建协作发展",
          news: [
            {
              id: 1,
              title: "区域党建协作机制建设研讨会",
              content: "探索建立区域党建协作长效机制，推动党建工作协同发展。",
              date: "2024-12-03",
            },
            {
              id: 2,
              title: "党建资源共享平台正式上线",
              content: "整合区域党建资源，实现党建工作信息化、数字化管理。",
              date: "2024-12-01",
            },
            {
              id: 3,
              title: "区域党建工作联席会议召开",
              content: "统筹协调区域党建工作，形成党建工作合力。",
              date: "2024-11-29",
            },
          ],
        },
      ],
      currentCooperationIndex: 0,
      cooperationNav: [
        {
          title: "理论同学",
          icon: "lltx.png",
        },
        {
          title: "组织同建",
          icon: "zztz.png",
        },
        {
          title: "品牌同筑",
          icon: "pptz.png",
        },
         {
          title: "服务同行",
          icon: "fwtx.png",
        },
         {
          title: "成效同享",
          icon: "cxtx.png",
        },
      ],

      // 当前豫起协同信息
      currentCooperationInfo: {
        title: "联合党建共建活动",
        news: [
          {
            id: 1,
            title: "19月25日，联合铁通公司党人力资源部党支部开展共建活动",
            content:
              "河南公司联合铁通河南分公司党支部开展共建活河南公司联合铁通河南分公司党支部开展共建活动，3河南公司联合铁通河南分公司党支部开展共建活动。动，3河南公司联合铁通河南分公司党支部开展共建活动。",
            date: "2024-12-01",
          },
          {
            id: 2,
            title: "党建共建促发展，携手共进谱新篇",
            content:
              "河南公司联合铁通河南分公司党支部开展共建活动，3河南公司联合铁通河南分公司党支部开展共建活动。",
            date: "2024-11-28",
          },
          {
            id: 3,
            title: "基层党组织结对共建签约仪式成功举办",
            content:
              "进一步加强进一步加强基层党组织建设，促进党建工作创新发展。基层党组织建设，促进党建工作创新发展。",
            date: "2024-11-25",
          },
        ],
      },
    };
  },

  computed: {
    ...mapState(["user"]),

    // 将豫起奋发数据分组，每组2个
    getYqffChunks() {
      const chunks = [];
      for (let i = 0; i < this.yqffData.length; i += 2) {
        chunks.push(this.yqffData.slice(i, i + 2));
      }
      return chunks;
    },

    // 将荣誉时刻数据分组，每组2个
    getHonorChunks() {
      const chunks = [];
      for (let i = 0; i < this.honorData.length; i += 2) {
        chunks.push(this.honorData.slice(i, i + 2));
      }
      return chunks;
    },

    // 将豫起赋能数据分组，每组3个
    getInterpretationChunks() {
      const chunks = [];
      for (let i = 0; i < this.interpretationData.length; i += 3) {
        chunks.push(this.interpretationData.slice(i, i + 3));
      }
      return chunks;
    },

    // 将党建视频数据分组，每组2个
    getVideoChunks() {
      const chunks = [];
      for (let i = 0; i < this.videoData.length; i += 2) {
        chunks.push(this.videoData.slice(i, i + 2));
      }
      return chunks;
    },
  },

  mounted() {
    document.title = "河南移动党建信息平台";
    this.initTime();
    this.$nextTick(() => {
      this.initTeamSwiper();
      this.initYqffSwiper();
      this.initHonorSwiper();
      this.initInterpretationSwiper();
      this.initVideoSwiper();
      this.initCooperationSwiper();
    });
  },

  beforeDestroy() {
    if (this.timeTimer) {
      clearInterval(this.timeTimer);
    }
    if (this.teamSwiper) {
      this.teamSwiper.destroy(true, true);
    }
    if (this.yqffSwiper) {
      this.yqffSwiper.destroy(true, true);
    }
    if (this.honorSwiper) {
      this.honorSwiper.destroy(true, true);
    }
    if (this.interpretationSwiper) {
      this.interpretationSwiper.destroy(true, true);
    }
    if (this.videoSwiper) {
      this.videoSwiper.destroy(true, true);
    }
    if (this.cooperationSwiper) {
      this.cooperationSwiper.destroy(true, true);
    }
  },

  methods: {
    // 初始化时间显示
    initTime() {
      this.updateTime();
      this.timeTimer = setInterval(() => {
        this.updateTime();
      }, 1000);
    },

    // 更新时间
    updateTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, "0");
      const day = String(now.getDate()).padStart(2, "0");
      const hours = String(now.getHours()).padStart(2, "0");
      const minutes = String(now.getMinutes()).padStart(2, "0");

      const weekDays = [
        "星期日",
        "星期一",
        "星期二",
        "星期三",
        "星期四",
        "星期五",
        "星期六",
      ];
      const weekDay = weekDays[now.getDay()];

      this.currentDate = `${year}年${month}月${day}日`;
      this.currentWeek = weekDay;
      this.currentTime = `${hours}:${minutes}`;
    },

    // 打开AI助手
    openAi() {
      console.log("打开AI助手");
      // 这里可以添加AI助手的打开逻辑
    },

    // 初始化团队轮播
    initTeamSwiper() {
      if (this.$refs.teamSwiper) {
        this.teamSwiper = new Swiper(this.$refs.teamSwiper, {
          spaceBetween: 0,
          centeredSlides: true,
          loop: true,
          autoplay: {
            delay: 4000,
            disableOnInteraction: false,
          },
          effect: "fade",
          fadeEffect: {
            crossFade: true,
          },
          pagination: {
            el: ".swiper-pagination",
            type: "bullets",
            clickable: true,
            dynamicBullets: false,
          },
          observer: true,
          observeParents: true,
          on: {
            slideChange: (swiper) => {
              // 使用传入的swiper参数，避免this.teamSwiper为null的问题
              const realIndex = swiper.realIndex;
              this.updateTeamInfo(realIndex);
            },
          },
        });

        // 初始化时设置第一个团队信息
        this.updateTeamInfo(0);
      }
    },

    // 更新团队信息
    updateTeamInfo(index) {
      if (
        this.partyTeamData &&
        this.partyTeamData.length > 0 &&
        this.partyTeamData[index]
      ) {
        this.currentTeamInfo = {
          title: this.partyTeamData[index].title,
          description: this.partyTeamData[index].description,
          author: this.partyTeamData[index].author,
          date: this.partyTeamData[index].date,
        };
      }
    },

    // 初始化豫起奋发轮播
    initYqffSwiper() {
      if (this.$refs.yqffSwiper) {
        this.yqffSwiper = new Swiper(this.$refs.yqffSwiper, {
          slidesPerView: 1,
          spaceBetween: 10,
          centeredSlides: false,
          loop: true,
          //   autoplay: {
          //     delay: 5000,
          //     disableOnInteraction: false,
          //   },
          pagination: {
            el: ".yqff-pagination",
            type: "bullets",
            clickable: true,
            dynamicBullets: false,
          },
          observer: true,
          observeParents: true,
        });
      }
    },

    // 初始化荣誉时刻轮播
    initHonorSwiper() {
      if (this.$refs.honorSwiper) {
        this.honorSwiper = new Swiper(this.$refs.honorSwiper, {
          slidesPerView: 1,
          spaceBetween: 10,
          centeredSlides: false,
          loop: true,
          autoplay: {
            delay: 6000,
            disableOnInteraction: false,
          },
          pagination: {
            el: ".honor-pagination",
            type: "bullets",
            clickable: true,
            dynamicBullets: false,
          },
          observer: true,
          observeParents: true,
        });
      }
    },

    // 初始化豫起赋能轮播
    initInterpretationSwiper() {
      if (this.$refs.interpretationSwiper) {
        this.interpretationSwiper = new Swiper(
          this.$refs.interpretationSwiper,
          {
            slidesPerView: 1,
            spaceBetween: 10,
            centeredSlides: false,
            loop: true,
            // autoplay: {
            //   delay: 7000,
            //   disableOnInteraction: false,
            // },
            pagination: {
              el: ".interpretation-pagination",
              type: "bullets",
              clickable: true,
              dynamicBullets: false,
            },
            observer: true,
            observeParents: true,
          }
        );
      }
    },

    // 初始化党建视频轮播
    initVideoSwiper() {
      if (this.$refs.videoSwiper) {
        this.videoSwiper = new Swiper(this.$refs.videoSwiper, {
          slidesPerView: 1,
          spaceBetween: 10,
          centeredSlides: false,
          loop: true,
          autoplay: {
            delay: 8000,
            disableOnInteraction: false,
          },
          pagination: {
            el: ".video-pagination",
            type: "bullets",
            clickable: true,
            dynamicBullets: false,
          },
          observer: true,
          observeParents: true,
        });
      }
    },

    // 初始化豫起协同轮播
    initCooperationSwiper() {
      if (this.$refs.cooperationSwiper) {
        this.cooperationSwiper = new Swiper(this.$refs.cooperationSwiper, {
          slidesPerView: 1,
          spaceBetween: 0,
          centeredSlides: true,
          loop: true,
          autoplay: {
            delay: 6000,
            disableOnInteraction: false,
          },
          effect: "fade",
          fadeEffect: {
            crossFade: true,
          },
          pagination: {
            el: ".cooperation-pagination",
            type: "bullets",
            clickable: true,
            dynamicBullets: false,
          },
          observer: true,
          observeParents: true,
          on: {
            slideChange: (swiper) => {
              const realIndex = swiper.realIndex;
              this.updateCooperationInfo(realIndex);
            },
          },
        });

        // 初始化时设置第一个协同信息
        this.updateCooperationInfo(0);
      }
    },

    // 更新豫起协同信息
    updateCooperationInfo(index) {
      if (
        this.cooperationData &&
        this.cooperationData.length > 0 &&
        this.cooperationData[index]
      ) {
        this.currentCooperationInfo = {
          title: this.cooperationData[index].title,
          news: this.cooperationData[index].news,
        };
      }
    },
    // 处理协同导航点击
    handleCooperationClick(index) {
      this.currentCooperationIndex = index;
      this.cooperationSwiper.slideTo(index);
    },
  },
};
</script>

<style scoped>
.party-screen-container {
  height: 100vh;
  background: #fff;
  overflow: hidden;
  font-family: "Microsoft YaHei", sans-serif;
  position: relative;
}

/* 全局图片响应式处理 - 防止浏览器放大时图片过大 */
.party-screen-container img {
  max-width: 100% !important;
  max-height: 100% !important;
  object-fit: cover;
  display: block;
  box-sizing: border-box;
}

/* 强制限制所有容器内的图片尺寸 */
.party-screen-container * {
  box-sizing: border-box;
}

/* 防止轮播图片溢出 */
.swiper-slide img {
  max-width: 100% !important;
  max-height: 100% !important;
  object-fit: cover;
  scale: 0.9;

}

/* 顶部标题栏 */
.header-section {
  width: 95%;
  height: 60px;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  background: #fff;
  padding: 0 30px;
  margin: 0 auto;
}

.header-left {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 20px;
}

.date-time {
  color: #d31805;
  font-size: 16px;
  font-weight: 400;
}

.header-center {
  text-align: center;
  position: relative;
}
.logo-img {
  display: block;
  width: 100%;
  height: 49px;
  scale: 0.9;
}

.header-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 20px;
}

.search-box {
  position: absolute;
  bottom: 0;
  left: -100px;
  width: 180px;
}
::v-deep .search-box .el-input__inner {
  border: 1px solid #d31805;
}
::v-deep .search-box .el-input__suffix {
  color: #d31805;
}

.user-info {
  color: #d31805;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 主体内容区域 */
.main-content {
  height: calc(100vh - 60px);
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 15px;
  padding: 15px 15px;
  background: #fff;
  overflow: auto;
}

/* 面板通用样式 */
.left-panel,
.center-panel,
.right-panel {
  overflow: hidden;
  padding: 15px;
  background: linear-gradient(to left, #d31805, #d31805) left top no-repeat,
    linear-gradient(to bottom, #d31805, #d31805) left top no-repeat,
    linear-gradient(to left, #d31805, #d31805) right top no-repeat,
    linear-gradient(to bottom, #d31805, #d31805) right top no-repeat,
    linear-gradient(to left, #d31805, #d31805) left bottom no-repeat,
    linear-gradient(to bottom, #d31805, #d31805) left bottom no-repeat,
    linear-gradient(to left, #d31805, #d31805) right bottom no-repeat,
    linear-gradient(to left, #d31805, #d31805) right bottom no-repeat;
  background-size: 2px 12px, 12px 2px, 2px 12px, 12px 2px;
  background-color: #fff4f2;
  width: 100%;
  box-sizing: border-box;
}

/* 面板标题 */
.panel-title {
  text-align: center;
  padding: 0px 12px 15px 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.panel-title img {
  display: block;
  width: 129px;
  scale: 0.9;
}
.stats-section {
  background: linear-gradient(to left, #fbe3e1, #fbe3e1) left top no-repeat,
    linear-gradient(to bottom, #fbe3e1, #fbe3e1) left top no-repeat,
    linear-gradient(to left, #fbe3e1, #fbe3e1) right top no-repeat,
    linear-gradient(to bottom, #fbe3e1, #fbe3e1) right top no-repeat,
    linear-gradient(to left, #fbe3e1, #fbe3e1) left bottom no-repeat,
    linear-gradient(to bottom, #fbe3e1, #fbe3e1) left bottom no-repeat,
    linear-gradient(to left, #fbe3e1, #fbe3e1) right bottom no-repeat,
    linear-gradient(to left, #fbe3e1, #fbe3e1) right bottom no-repeat;
  background-size: 2px 10px, 10px 2px, 2px 10px, 10px 2px;
  background-color: #ffffff;
  padding: 10px 20px;
  margin-bottom: 10px;
}

/* 章节标题样式 */
.section-title {
  color: #333;
  font-size: 14px;
  font-weight: bold;
  margin: 0 0 10px 0;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}
.title-container {
  display: flex;
  align-items: center;
}

.arrow {
  display: block;
  height: 18px;
  color: #e53e3e;
  font-size: 10px;
  margin-right: 8px;
  scale: 0.9;
}
.text-title {
  /* 文本标题样式 */
  display: block;
  height: 22px;
  scale: 0.9;
}

.more-link {
  color: #000000;
  font-size: 14px;
  cursor: pointer;
}

.more-link:hover {
  color: #e53e3e;
}
.ai-section {
  padding: 10px 20px;
}
.ai-info {
  position: relative;
}
.ai-info .arrow {
  height: 44px;
  margin-right: 0;
  scale: 0.9;

}
.ai-info::after {
  content: "";
  position: absolute;
  left: 17px;
  bottom: 1px;
  width: calc(100% - 17px);
  height: 3px;
  background-color: #d0000b;
}
.ai-info .text-title {
  height: 21px;
  scale: 0.9;

}

/* 统计卡片 */
.stats-cards {
  display: flex;
  gap: 15px;
}

.stat-card {
  width: 162px;
  height: 62px;
  flex: 1;
  color: white;
  text-align: center;

  background: radial-gradient(
    ellipse,
    white 55%,
    rgba(251, 224, 224, 0.6) 80%,
    rgba(251, 224, 224, 0.8) 95%,
    rgba(251, 224, 224, 1) 100%
  );
}
.stat-content {
  background: linear-gradient(to left, #d31805, #d31805) left top no-repeat,
    linear-gradient(to bottom, #d31805, #d31805) left top no-repeat,
    linear-gradient(to left, #d31805, #d31805) right top no-repeat,
    linear-gradient(to bottom, #d31805, #d31805) right top no-repeat,
    linear-gradient(to left, #d31805, #d31805) left bottom no-repeat,
    linear-gradient(to bottom, #d31805, #d31805) left bottom no-repeat,
    linear-gradient(to left, #d31805, #d31805) right bottom no-repeat,
    linear-gradient(to left, #d31805, #d31805) right bottom no-repeat;
  background-size: 2px 10px, 10px 2px, 2px 10px, 10px 2px;
}

.stat-number {
  font-size: 30px;
  font-weight: bold;
  color: #e02020;
  font-family: YouSheBiaoTiHei;
}

.stat-label {
  font-size: 12px;
  color: #000000;
}

/* 资源网格 */
.resource-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
}

.resource-item {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}
.resource-item:nth-child(-n + 4) {
  margin-bottom: 8px;
}

.resource-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 5px;
}
.resource-icon img {
  display: flex;
  height: 62px;
  scale: 0.9;
}

.resource-label {
  font-size: 14px;
  color: #af3128;
  line-height: 1.2;
}

/* 党员突击队 */
.party-team-content {
  display: flex;
  gap: 0px;
  align-items: center;
}

.team-carousel {
  width: 50%;
  height: 120px;
  overflow: hidden;
  flex-shrink: 0;
  position: relative;
}

.swiper-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.swiper-slide {
  width: 100% !important;
  height: 100%;
}

.team-img {
  width: 100%;
  height: 100%;
  display: block;
}

.team-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.team-title {
  font-size: 13px;
  color: #303030;
  margin: 0 0 3px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  height: auto;
  max-height: calc(1.3em * 5);
}

.team-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin: 0 0 5px 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  height: auto;
  max-height: calc(1.4em * 3);
}

.team-meta {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.team-author,
.team-date {
  font-size: 12px;
  color: #999;
  display: flex;
  align-items: center;
  gap: 4px;
}

.team-author i,
.team-date i {
  font-size: 14px;
}

/* Swiper 轮播组件样式 */
.swiper-container .swiper-wrapper {
  transition-timing-function: ease-in-out;
}

.swiper-slide img {
  transition: opacity 0.3s ease-in-out;
}

/* Swiper 分页器样式 */
.team-carousel .swiper-pagination {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 6px;
}
::v-deep .team-carousel .swiper-pagination-bullet {
  width: 8px;
  height: 8px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  opacity: 1;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.8);
}

::v-deep .team-carousel .swiper-pagination-bullet-active {
  background: #fff;
  border-color: #fff;
  transform: scale(1.2);
}

::v-deep .team-carousel .swiper-pagination-bullet:hover {
  background: #fff;
  transform: scale(1.1);
}

/* AI功能区 */
.ai-section {
  margin-bottom: 20px;
}

.ai-title {
  color: #333;
  font-size: 14px;
  font-weight: bold;
  margin: 0 0 15px 0;
  display: flex;
  align-items: center;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.ai-icon {
  margin-right: 8px;
  font-size: 16px;
}

.ai-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  place-items: center;
}

.ai-card {
  color: #333;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}
.ai-card img {
  width: 248px;
  height: 76px;
  scale: 0.9;

}

.ai-card:hover {
  transform: translateY(-1px);
}

/* 豫起奋发轮播 */
.yqff-carousel {
  position: relative;
  overflow: hidden;
}

.yqff-carousel .swiper-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.yqff-carousel .message-images {
  display: flex;
  gap: 10px;
  height: 100%;
}

.yqff-carousel .message-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.yqff-carousel .message-item img {
  width: 100%;
  height: 129px;
  flex-shrink: 0;
}

.yqff-carousel .message-desc {
  padding: 0 15px;
  font-size: 12px;
  color: #000000;
  line-height: 1.3;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  height: auto;
  max-height: calc(1.3em * 2);
  text-align: center;
}

/* 荣誉时刻轮播 */
.honor-carousel {
  position: relative;
  overflow: hidden;
}

.honor-carousel .swiper-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.honor-carousel .honor-grid {
  display: flex;
  gap: 10px;
  height: 100%;
}

.honor-carousel .honor-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  cursor: pointer;
}

.honor-carousel .honor-item img {
  width: 100%;
  height: 129px;
  flex-shrink: 0;
}

.honor-carousel .honor-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.honor-carousel .honor-title {
  padding: 0 8px;
  color: #000000;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.3;
  margin-bottom: 4px;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  height: auto;
  max-height: calc(1.3em * 2);
  text-align: center;
  margin-top: 4px;
}

.honor-carousel .honor-date {
  font-size: 10px;
  color: #666;
}

/* 党员音讯 */
.member-news-section {
  margin-bottom: 20px;
}

.member-news-content {
  color: #333;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.item-text {
  display: flex;
  align-items: center;
  margin: 8px 0 0 10px;
  cursor: pointer;
  width: calc(100% - 20px);
  box-sizing: border-box;
  max-width: 100%;
  overflow: hidden;
}

.cycle {
  width: 8px;
  height: 8px;
  border-radius: 100%;
  background-color: #bc0b00;
  margin-right: 5px;
  flex-shrink: 0;
}

.text-info {
  flex: 1;
  font-size: 14px;
  color: #000;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-left: 5px;
  min-width: 0;
  max-width: 100%;
}
.text-info:hover {
  color: #e53e3e;
}

/* AI助手 */
.ai-assistant {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  cursor: pointer;
  z-index: 1000;
}

.ai-assistant img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.ai-assistant:hover img {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb {
  background: #e53e3e;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: #d32f2f;
}

/* Element UI 组件样式覆盖 */
::v-deep .el-input__inner {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 15px;
  font-size: 12px;
}

/* 轮播图 */
.carousel-container {
  margin-bottom: 20px;
}

.carousel-image {
  width: 100%;
  height: 200px;
  max-width: 100%;
  max-height: 200px;
  object-fit: cover;
  border-radius: 8px;
}

.carousel-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
  padding: 20px 15px 15px;
  font-size: 14px;
  font-weight: 500;
}

/* 新闻列表 */
.news-list {
  margin-bottom: 20px;
}

.news-item {
  display: flex;
  gap: 15px;
  padding: 15px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.news-item:hover {
  background: #f8f9fa;
  border-color: #d32f2f;
}

.news-date {
  background: #d32f2f;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  align-self: flex-start;
}

.news-content {
  flex: 1;
}

.news-content h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #333;
  line-height: 1.4;
}

.news-content p {
  margin: 0;
  font-size: 12px;
  color: #666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  height: auto;
  max-height: calc(1.5em * 2);
}

/* 解读网格 */
.interpretation-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.interpretation-item {
  overflow: hidden;
  cursor: pointer;
}

/* 视频区域 */
.video-container {
  margin-bottom: 20px;
}

/* 删除重复的党员音讯样式，已在上面定义 */

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #d32f2f;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #b71c1c;
}

/* Element UI 组件样式覆盖 */
::v-deep .el-carousel__container {
  height: 200px !important;
}

::v-deep .el-carousel__item {
  border-radius: 8px;
  overflow: hidden;
}

/* 豫起赋能轮播 */
.interpretation-carousel {
  position: relative;
  overflow: hidden;
}

.interpretation-carousel .swiper-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.interpretation-carousel .interpretation-grid {
  display: flex;
  gap: 8px;
  height: 100%;
  margin-bottom: 0;
}

.interpretation-carousel .interpretation-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  cursor: pointer;
}

.interpretation-carousel .interpretation-item img {
  width: 100%;
  height: 80px;
  flex-shrink: 0;
}

.interpretation-carousel .interpretation-desc {
  padding: 0 10px;
  font-size: 12px;
  color: #000000;
  line-height: 1.3;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  height: auto;
  max-height: calc(1.3em * 2);
  text-align: center;
  margin-top: 4px;
}

/* 党建视频轮播 */
.video-carousel {
  position: relative;
  height: 130px;
  overflow: hidden;
}

.video-carousel .swiper-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.video-carousel .video-grid {
  display: flex;
  gap: 10px;
  height: 100%;
}

.video-carousel .video-item {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.video-carousel .party-video {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
}

/* 豫起协同模块 */
.cooperation-container {
  display: flex;
  gap: 5px;
  height: 88px;
  align-items: center;
  min-width: 0;
  overflow: hidden;
}
.cooperation-flex{
    justify-content: flex-start;
    align-items: center;
}

.cooperation-carousel {
  flex: 0 0 190px;
  position: relative;
  height: 100%;
  overflow: hidden;
}
.cooperation-nav{
    display: flex;
    align-items: center;
}
.cooperation-nav-item{
    margin-left: 8px;
    cursor: pointer;
}
.cooperation-nav-item.active{
    border-bottom: 2px solid #e02020;
}

.cooperation-nav-item img{
    display: block;
    height: 25px;
}

.cooperation-carousel .swiper-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.cooperation-image {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
}

::v-deep .cooperation-carousel .swiper-pagination {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
}

::v-deep .cooperation-carousel .swiper-pagination-bullet {
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  opacity: 1;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.8);
}

::v-deep .cooperation-carousel .swiper-pagination-bullet-active {
  background: #fff;
  border-color: #fff;
  transform: scale(1.2);
}

.cooperation-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  overflow: hidden;
}

.cooperation-news-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  overflow: hidden;
}

.cooperation-news-item {
  min-width: 0;
  overflow: hidden;
  cursor: pointer;
}

.cooperation-news-item .news-content {
  font-size: 14px;
  color: #000;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
  width: 100%;
  display: block;
  box-sizing: border-box;
}
.news-content:hover {
  color: #e02020;
}

::v-deep .el-input__inner {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 20px;
}
</style>