<template>
  <!-- <div class="app-wrapper" :class="classObj"> -->
  <div class="app-wrapper">
    <!--<span @click="getuser()" style='position:fixed;z-index:1000;'>asdfasdf</span>-->
    <sidebar class="sidebar-container"></sidebar>
    <div class="main-container">
      <navbar></navbar>
      <tab-nav></tab-nav>
      <app-main style="margin-top: 40px"></app-main>
    </div>
  </div>
</template>
<script>
import { Navbar, Sidebar, AppMain, TabNav } from "./components";
import ResizeMixin from "./mixin/ResizeHandler";
export default {
  name: "layout",
  components: {
    Navbar,
    Sidebar,
    AppMain,
    TabNav,
  },
  mixins: [ResizeMixin],
  computed: {
    sidebar() {
      return this.$store.state.app.sidebar;
    },
    device() {
      return this.$store.state.app.device;
    },
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === "mobile",
      };
    },
  },
  methods: {
    getuser() {},
  },
};
</script>
<style scoped>
.app-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
}
.app-wrapper:after {
  content: "";
  display: table;
  clear: both;
}
</style>
