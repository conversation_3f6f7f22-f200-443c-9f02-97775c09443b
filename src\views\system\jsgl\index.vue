<template>
	<div class="app-container" style="display:flex;justify-content: space-between;">
		<div class="container-right">
			<sb-el-table :table="table" @getList="getList" @handleRole="handleRole" @handleAddData="handleAddData" @handleUpDataGetRow="handleUpDataGetRow"
						@handleUpData="handleUpData" @handleDelete="handleDelete" @handleAddBef="handleAddBef">
				<template v-slot:enabled="{ obj }">
					<div>{{ obj.row.enabled==1?'是':'否' }}</div>
				</template>
				<template v-slot:isApplicationRole="{ obj }">
					<div>{{ obj.row.isApplicationRole==1?'是':'否' }}</div>
				</template>
			</sb-el-table>
		</div>
		<el-dialog title="角色配置" :visible.sync="roleAllocationDialog" width="1200px" center>
			<div style="display:flex">
				<sb-el-table :table="roleAllocatTable" @getList="findUserByRole" @handleRoleDelete="handleRoleDelete">
				</sb-el-table>
				<User :item="roleAllocatData" @chooseData="createRoleUsers"/>
			</div>
			<!-- <span slot="footer" class="dialog-footer">
				<el-button @click="roleAllocationDialog = false">取 消</el-button>
				<el-button type="primary" @click="createRoleUsers">确 定</el-button>
			</span> -->
		</el-dialog>
	</div>
</template>
<script>
// import { findAll, deleteById, changeStatus, getTableField, getSyncField, batchUpdateField, create, update, findAllDb, saveGroupDataAdd, saveGroupDataUpdate, saveGroupData, delectGroupData, queryGroupList, addGroupJY, deleteGroupById } from '@/api/dataCollect/dataSet';
// import { ListByDataSetId } from '@/api/dataCollect/datasetForm';
import {createRoleUsers,deleteByIdCustom,findUserByRoleR, getRoleList,updateSysRole,findRootAndNextRoot,findSonByParentOrgId,addRole,updateRoleCustom,deleteRoleCustom,findByIdRole } from '@/api/system/jsgl.js';

import User from "../component/user.vue";
export default {
	name: 'dataSet',
	props: ["tableType", "tableTitle"],
	components: {
		User
	},
	data() {
		return {
			fieldTableDialog: false, //字段管理弹框
			fieldTableData: [], //字段列表表数据
			quotaChecked: false,
			quotaIndeterminate: false,
			datasetId: '', //数据集id
			table: {
				border: true, // 是否带纵向边框
				loading: false, // 加载中动画
				modulName: 'jsgl-角色信息', // 列表中文名称
				stripe: true, // 是否为斑马条样式
				hasSelect: true, // 是否有复选框
				showIndex: true, // 序号
				data: [], // 数据
				addAndUpdateType: 'dialog',
				total: null,
				hasQueryForm: true, // 是否有查询条件
				queryForm: {
					inline: true,
					labelWidth: '110px',
					labelPosition: 'right',
					formItemList: [
						{ label: '角色id', key: 'id', type: 'input', clearable: true },
						{ label: '角色编码', key: 'roleCode', type: 'input' , clearable: true },
						{ label: '角色名称', key: 'roleName', type: 'input', clearable: true },
						{ label: '账号/姓名', key: 'user', type: 'input', clearable: true },
					],
				},
				tr: [
					{ id: 'id', label: '角色id', prop: 'id', width: '300',},
					{ id: 'enabled', label: '启用状态', prop: 'enabled', show: 'template'},
					{ id: 'roleCode', label: '角色编码', prop: 'roleCode',  },
					{ id: 'roleName', label: '角色名称', prop: 'roleName',  },
					{ id: 'isApplicationRole', label: '是否是业务角色', prop: 'isApplicationRole', show: 'template' },
				],
				multipleSelection: [], //多选选中数据存放变量
				dialogVisible: false, //默认对话框关闭
				form: {
					width: '800px',
					labelWidth: '130px',
					inline: true,
					labelPosition: 'right',
					formItemList: [
						{ class: 'c11', label: '角色编码', key: 'roleCode', type: 'input', rule: { required: true } , clearable: true},
						{ class: 'c11', label: '角色名称', key: 'roleName', type: 'input', rule: { required: false }, clearable: true },
						{ class: 'c11', label: '是否是业务角色', key: 'isApplicationRole', type: 'select', options:[{name:'是',value:1},{name:'否',value:0}], clearable: true},
					],
				},
				listFormModul: {},
				hasOperation: true, //是否有操作列表
				operation: {
					width: '250',
					data: [
						{ id: 'add', name: '新增',beforeFun: "handleAddBef", fun: 'handleAddData' },
                        { id: 'read', name: '查看', fun: 'handleUpDataGetRow' },
						{ id: 'update', name: '编辑', fun: 'handleUpData', beforeFun: "handleUpDataGetRow" },
                        { id: 'jspz', name: '角色配置', fun: 'handleRole'},
						{ id: 'deleteY', name: '启用', fun: 'handleDelete',show: "enabled|0"},
						{ id: 'deleteN', name: '弃用', fun: 'handleDelete',show: "enabled|1"},
					],
				},
				hasPagination: true,
				listQuery: { size: 10, page: 1 },
				hasBatchOperate: false, //有无批量操作
				batchOperate: {},
				hasOtherQueryBtn: true,
				otherQueryBtn: {
					data: [
                        {
                            id: 'export',
                            name: '导出 ',
                            fun: 'exportSelect',
                        },
                    ],
				},
				hasGroupTabs: true,
				tabsList: [],
				tabsPosition: '全部',
			},
			dbName:"",
			dbArr: [],
			idS: [], //当前选中分组ids
			selectIndex:null,  //搜索结果,被选中的li index
            data: [],
            defaultProps: {
                children: 'children',
                label: 'label'
            },
			roleAllocationDialog: false,
			roleAllocatData: {
				inputType: 'text',
				appendShow: true,
				rows: 12,
				btnText: '搜索',
				mulitple: true,
				defaultProps: {
					children: "children",
					label: "name",
					isLeaf: 'leaf',
				},
			},
			roleAllocatTable: {
				border: true, // 是否带纵向边框
				loading: false, // 加载中动画
				modulName: 'rolePZ-角色配置', // 列表中文名称
				stripe: true, // 是否为斑马条样式
				hasSelect: true, // 是否有复选框
				showIndex: true, // 序号
				data: [], // 数据
				addAndUpdateType: 'dialog',
				total: null,
				hasQueryForm: true, // 是否有查询条件
				queryForm: {
					inline: true,
					labelWidth: '80px',
					labelPosition: 'right',
					formItemList: [
						{ label: '用户账号', key: 'username', type: 'input', clearable: true },
					],
				},
				tr: [
					{ id: 'USERNAME', label: '用户账号', prop: 'USERNAME', align: 'center',},
					{ id: 'TRUENAME', label: '用户姓名', prop: 'TRUENAME' },
					{ id: 'DISPLAY_NAME', label: '所在组织的全路径', prop: 'DISPLAY_NAME', align: 'center' },
				],
				multipleSelection: [], //多选选中数据存放变量
				dialogVisible: false, //默认对话框关闭
				form: {
					width: '800px',
					labelWidth: '100px',
					inline: true,
					labelPosition: 'right',
					formItemList: [
						
					],
				},
				listFormModul: {},
				hasOperation: true, //是否有操作列表
				operation: {
					width: '250',
					data: [
						{ id: 'delete', name: '删除', fun: 'handleRoleDelete'},
					],
				},
				hasPagination: true,
				listQuery: { size: 10, page: 1 ,roleId: '' },
				hasBatchOperate: false, //有无批量操作
				batchOperate: {},
				hasOtherQueryBtn: false,
				otherQueryBtn: {
					data: [],
				},
				currentRoleId: '',
			}
		};
	},
	activated() {
		
	},
	created(){
		this.getList();
	},
	methods: {
		// 添加用户角色
		createRoleUsers(item) {
			var users = '';
			item.forEach(item=>{
				users = (users + ',' + item.id).slice(1)
			})
			var params = {
				roleId: this.roleAllocatTable.currentRoleId,
				usernames: users,
			}
			createRoleUsers(params).then((res) => {
				if(res.status == 200) this.findUserByRole()
			}).catch((err) => {
				
			});
		},
		// 删除用户角色
		handleRoleDelete(obj) {
			deleteByIdCustom(obj.ID).then((res) => {
				if(res.status == 200) this.findUserByRole()
			}).catch((err) => {
				
			});
		},
		//弹窗内部搜索OA账号
		findUserByRole(obj) {
			this.roleAllocatTable.loading = true;
			findUserByRoleR(this.roleAllocatTable.listQuery).then((res) => {
				this.roleAllocatTable.loading = false;
				this.roleAllocatTable.data = res.data&&res.data.content?res.data.content:[];
				this.roleAllocatTable.total = res.data.totalElements;
			}).catch((err) => {
				this.roleAllocatTable.loading = false;
			});
		},
		//角色配置
		handleRole(obj) {
			this.roleAllocatTable.listQuery.roleId=obj.row.id;
			this.findUserByRole(this.roleAllocatTable.listQuery)
			this.roleAllocationDialog = true;
			this.roleAllocatTable.listQuery.roleId = obj.row.id;
			this.roleAllocatTable.currentRoleId = obj.row.id;
		},
		// 同步API
		handleUpdateApi({row}){
			this.$confirm('请确定该条数据集是否更新到API', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				let params = {
					dataSetId:row.id
				}
				// ListByDataSetId(params).then((res) => {
				// 	if (res.status == 200) this.$message({ message: '更新成功', type: 'success' });
				// });
			})
		},
		openDataSourceFun(data){
			const obj = this.dbArr.find(item=>{
				return item.id == data.id
			})
			if(obj) this.handleDataSet(null,obj)
		},
		onTreeNodeClick(data,index){
			this.selectIndex = index;
			this.table.listQuery.dataSourceId = data.id;
			this.getList();
		},
		getDataDbList(val) {
			let params = {}
			if (val) params = { name: val};
			// findAllDb(params).then((res) => {
			// 	res.data.forEach((target) => {
			// 		Reflect.set(target, 'leaf', true)
			// 	})
			// 	this.dbArr = res.data;
			// });
		},
		// 查询列表
		getList() {
			this.table.loading = true;
			getRoleList(this.table.listQuery).then((res) => {
				this.table.loading = false;
				this.table.data = res.data&&res.data.content?res.data.content:[];
				this.table.total = res.data.totalElements;
			}).catch((err) => {
				this.table.loading = false;
			});
		},
		handleAddBef(){
			this.table.operation.nowBtn = true;
			this.table.form.formItemList.forEach((item)=>{
				item.disabled = false;
			})
			return true
		},
		// 新增
		handleAddData() {
			this.table.listFormModul.id='',
			this.table.listFormModul.displayOrder= '1',
			addRole(this.table.listFormModul).then(res => {
				this.table.dialogVisible = false
				if (res.status == 200){
					this.$message({
						message: '操作成功',
						type: 'success'
					});
					this.getList();
				} 
			})
		},

		// 编辑
		handleUpData() {
			updateRoleCustom(this.table.listFormModul).then(res => {
				this.table.dialogVisible = false
				this.$message({
					message: '操作成功',
					type: 'success'
				});
				this.getList();
			})
		},
		// 启用
		handleDelete(obj) {
			this.$confirm(
              "是否要" + (obj.id=="deleteY"?'启用':'弃用') + "该条数据",
              "提示",
              {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
              }
            )
			.then(() => {
				let params = {
					id: obj.row.id,
					status: obj.row.enabled==1?0:1
				}
				updateSysRole(params).then((res) => {
					// this.$message({
					// 		message: '操作成功',
					// 		type: 'success'
					// 	});
						this.getList();
				});
			})
			.catch(() => {});
		},
		// 根据id查询行数据
		handleUpDataGetRow(row) {
			this.table.form.formItemList.forEach((item)=>{
				item.disabled = row.read;
			})
			this.table.listFormModul = row
		},
		
		handleDoFun(obj, fun, data) {
			let n = this[obj[fun]].call(this, obj, data);
			return n;
		},
		// //选择配置信息回显
		// chooseData(array) {
		// 	let org = ''
		// 	for(var i=0;i<array.length;i++) {
		// 		org = (org + ',' +array[i].name).substring(1);
		// 	}
		// 	this.table.listFormModul.positionIdName = org;
		// },
	},
};
</script>
<style scoped>
.table-name {
    font-weight: bold;
}
.container-left {
    width: 300px;
    height: 100%;
    overflow: auto;
    background-color: var(--el-bg-color-overlay);
    border: 1px solid var(--el-border-color-light);
    border-radius: 4px;
    box-shadow: var(--el-box-shadow-light);
}
.container-right {
    width: calc(100% - 10px);
    height: 100%;
	position: relative;
}
::-webkit-scrollbar {
    display: none;
}
::v-deep .el-card__header{
	padding: 8px 15px;
}
.treeData{
	width:100%;
	display:flex;
	justify-content: space-between;
	font-size: 15px;
	height: 30px;
	line-height: 30px;
	color: #444;
	padding-left: 8px;
}
.treeData .text{
	width: 215px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	/* padding: 5px; */
	cursor: pointer;
}
.treeData .text:hover{
	color: rgba(192,0,0,1);
}
.treeData .execute{
	width: 70px;
	color: rgba(192,0,0,1);
	cursor: pointer;
	text-align: right;
}
.treeData .execute:hover{
	text-decoration: underline;
}
::v-deep .el-card__body{
	padding: 0 15px;
	padding-bottom: 15px;
}
.head-container{
	height: calc(100vh - 180px);
	overflow-y: auto;
    padding: 20px;
}
::v-deep .el-table__body-wrapper{
	max-height: calc(100vh - 320px);
	overflow-y: auto;
}
.acceptanceBox{
    position: relative;
	/* height: 250px; */
}
.footer{
    width: 100%;
    /* position: absolute; */
    left: 0;
    bottom: 0;
    text-align: center;
    margin-top: 20px;
	margin-bottom: 20px;
}
.selectedColor {
	background-color: #eeeeee;
}
</style>
