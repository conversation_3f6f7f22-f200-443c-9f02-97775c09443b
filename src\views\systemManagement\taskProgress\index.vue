<template>
  <div class="app-container">
    <sb-el-table
      :table="table"
      @getList="getList"
    @handleEdit="handleEdit"
      :on-ok="handleDoFun"
    >
      <template v-slot:currentProgress="{obj}">
            <el-input v-if="editClick" type="number" v-model="obj.row.currentProgress"   size="small"  ></el-input>
            <span v-else>{{obj.row.currentProgress}}</span>
        </template>
    </sb-el-table>
  </div>
</template>
<script>
import { uploadProcessFiles } from "@/api/public";
import WorkOrder from "@/components/WorkOrder";
import { selectAllNoPage,batchEdit} from "@/api/apply/application.js";
export default {
  name: "processTask",
  components: { WorkOrder },
  data() {
    return {
      viewD: false,
      dialogTitle: "",

      gps: {
        type: "task",
        location: "",
        pmInsType: "",
      },

      cKey: 0,
      table: {
        modulName: "processTask-待办列表", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: false, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: false, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "90px",
          formItemList: [{ label: "出彩名称", key: "name", type: "input" }],
        },
        tr: [
          { id: "id", label: "序号", prop: "id", width: 80 },
          { id: "lineName", label: "所属条线名称", prop: "lineName", width: 400 },
          { id: "projectName", label: "出彩项目名称", prop: "projectName", width: 400 },
          { id: "currentProgress", label: "当前进度", prop: "currentProgress",show: 'template' },
        ],
        // hasSetup:true,
        // setup:[],
        processType: [],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "600px",
          labelWidth: "100px",
          inline: true,
          formItemList: [
           
          ],
        },
        listFormModul: {},
        hasOperation: false, //是否有操作列表
        operation: {
          width: "120",
          fixed: "right",
          data: [
           
          ],
        },
         hasOtherQueryBtn: true, //是否有其他操作
        otherQueryBtn: {
          data: [
            {id: "edit", type: "primary", name: "修改", fun: "handleEdit"}
          ]
        },
        hasPagination: false,
        listQuery: {  },
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
      tableIndex: 0,
      editClick :false
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 查询列表
    getList(listQuery) {
      this.table.loading = true;
      selectAllNoPage(listQuery || this.table.listQuery)
        .then((res) => {
          this.table.loading = false;
          this.table.data = res.data;
        })
        .catch((err) => {
          this.table.loading = false;
        });
    },
    handleEdit() {
        this.editClick = !this.editClick;
        if(!this.editClick){
            this.table.data.forEach((item) => {
             delete item.modifiedTime;
             delete item.createdTime;
            })
            batchEdit(this.table.data).then((res) => {
                this.$message({
                    message: "修改成功",
                    type: "success",
                  });
                this.getList();
            })
          
        }

      
    },
    

    // 关闭弹框
    dialogClose() {
      this.viewD = false;
      this.getList();
    },

    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },

    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    },
  },
};
</script>
<style scoped>
/* 修改公共样式弹框样式 */
::v-deep .el-dialog__header {
  text-align: center !important;
  background: white !important;
  color: black;
  font-size: 14px;
  font-weight: bold;
  border-bottom: 1px solid #f2f2f2 !important;
}
::v-deep .el-dialog__title {
  color: black !important;
  font-size: 15.5px;
}
::v-deep .el-dialog__headerbtn .el-dialog__close {
  color: black;
}
</style>