<template>
  <div class="w100 inputBtn">
    <el-container>
      <el-main>
        <el-tree :props="item.defaultProps || defaultProps" ref="chooseOrgTree" :node-key="item.nodeKey || 'orgCode'" @node-click="treeClick" :data="treeData"
                 @check="treeCheck" :default-checked-keys="defaultCheckedKs" accordion :expand-on-click-node="false" default-expand-all show-checkbox
                 check-strictly></el-tree>
      </el-main>
      <el-aside width="320px" class="asideR">
        <h5 class="fbold">{{ item.typeInfo == 'org' ? '已选组织' : '已选部门' }}</h5>
        <div class="choose-department-checked">
          <div class="choose-department-item" v-for="(citem, index) in multipleSelection" :key="citem[item.nodeKey || 'orgCode']">
            <div class="choose-department-item-text ellipsis-line-1">
              <!-- {{ citem[item.defaultProps.label || "orgName"] }} -->
              {{ citem.orgName }}
            </div>
            <span class="choose-department-item-close" @click="delChoose(citem)">x</span>
          </div>
        </div>
      </el-aside>
    </el-container>
    <div slot="footer" class="footer">
      <el-button @click="handleUp" size="small">取消</el-button>
      <el-button type="primary" @click="handleUp('yes')" size="small">确定</el-button>
    </div>
  </div>
</template>
<script>
import {findPOrgAndCityOrg, findSonByParentOrgId} from "@/api/public";

export default {
  name: "SbChooseOrg",
  props: {
    item: {
      type: Object,
      required: true
    },
    onOk: {
      type: Function
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    cd: {
      type: Array,
    }
  },
  data() {
    return {
      defaultProps: {
        children: "children",
        label: "orgName"
      },
      treeData: [],
      multipleSelection: [],
      defaultCheckedKs: [],
      reqs: [],
      arrId: []
    };
  },
  methods: {
    // 按钮事件
    handleUp(op) {
      if (op == "yes") {
        this.$emit("flowdata", this.multipleSelection);
      }
      this.$emit("closeshowDialogFun");
    },
    getTreeData() {
      if (this.item.treeDataHttpRequest) {
        const {uid} = new Date().getTime();
        const options = {
          onProgress: e => {
          },
          onSuccess: res => {
            this.treeData = res;
            delete this.reqs[uid];
          },
          onError: err => {
            //this.handleError(err);
            delete this.reqs[uid];
          }
        };
        const req = this.handleHttpRequest(options);
        this.reqs[uid] = req;
        if (req && req.then) {
          req.then(options.onSuccess, options.onError);
        }
      } else {
        findPOrgAndCityOrg().then(res => {
          this.treeData = this.util.toTreeData(
              res.data,
              "orgCode",
              "parentOrgCode",
              "orgCode,orgName,parentOrgCode,displayName"
          );
        });
      }
    },
    handleHttpRequest(content, data) {
      this.$emit("uploadHttpRequest", {
        fun: this.item.treeDataHttpRequest,
        content,
        data
      });
    },
    treeClick(data, node, tree) {
      if (this.item.treeClickHttpRequest) {
        const {uid} = new Date().getTime();
        const options = {
          onProgress: e => {
          },
          onSuccess: res => {
            if (res.length > 0)
              this.$refs.chooseOrgTree.updateKeyChildren(
                  data[this.item.nodeKey || "orgCode"],
                  res
              );
            else
              this.$message({
                message: "该组织无下级组织！",
                type: "warning",
                duration: 3000
              });
            delete this.reqs[uid];
          },
          onError: err => {
            delete this.reqs[uid];
          }
        };
        const req = this.handleHttpRequest(options, data);
        this.reqs[uid] = req;
        if (req && req.then) {
          req.then(options.onSuccess, options.onError);
        }
      } else {
        if (
            (this.item.stepLoad !== undefined && this.item.stepLoad !== false) ||
            this.item.stepLoad === true
        ) {
          findSonByParentOrgId(data[this.item.nodeKey || "orgCode"]).then(
              res => {
                if (res.data.length > 0)
                  this.$refs.chooseOrgTree.updateKeyChildren(
                      data[this.item.nodeKey || "orgCode"],
                      res.data
                  );
                else
                  this.$message({
                    message: "该组织无下级组织！",
                    type: "warning",
                    duration: 3000
                  });
              }
          );
        }
      }
    },
    treeCheck(data, checkedD) {
      if (
          (!this.item.mulitple && this.item.mulitple !== false) ||
          this.item.mulitple === true
      ) {
        //多选
        var index = this.multipleSelection.findIndex(
            datai =>
                datai[this.item.nodeKey || "orgCode"] ===
                data[this.item.nodeKey || "orgCode"]
        );
        if (
            checkedD.checkedKeys.indexOf(data[this.item.nodeKey || "orgCode"]) >
            -1
        ) {
          this.multipleSelection.push(data);
        } else {
          this.multipleSelection.splice(index, 1);
        }
      } else {
        if (checkedD.checkedKeys.length === 0) {
          this.multipleSelection = [];
          this.$refs.chooseOrgTree.setCheckedKeys([]);
        } else {
          this.multipleSelection = [data];
          this.$refs.chooseOrgTree.setCheckedKeys([
            data[this.item.nodeKey || "orgCode"]
          ]);
        }
      }
    },
    delChoose(row) {
      if (
          (!this.item.mulitple && this.item.mulitple !== false) ||
          this.item.mulitple === true
      ) {
        //多选
        let arry = JSON.parse(JSON.stringify(this.multipleSelection));
        for (let i in arry) {
          for (let i in arry) {
            if (arry[i][this.item.nodeKey || "orgCode"] === row[this.item.nodeKey || "orgCode"]) arry.splice(i, 1);
          }
        }
        this.multipleSelection = arry;
        let arrId = []
        for (var i in arry) {
          arrId.push(arry[i].orgCode)
        }
        this.$refs.chooseOrgTree.setCheckedKeys(arrId);
      } else {
        //单选
        this.multipleSelection = [];
        this.$refs.chooseOrgTree.setCheckedKeys([]);
      }
    }
  },
  created() {
    this.getTreeData();
    // 回显数据cd
    if (this.cd.length > 0) {
      for (let i in this.cd) {
        this.multipleSelection.push({orgCode: this.cd[i].code, orgName: this.cd[i].name, sentType: 'org'})
        this.arrId.push(this.cd[i].code)
      }
    }
  },
  mounted() {
    // 选中组织树
    this.$refs.chooseOrgTree.setCheckedKeys(this.arrId);
  }
};
</script>
<style scoped>
.icon {
  margin: 0;
}

.el-main {
  padding: 0px;
  border-left: 0px solid #e0e0e0;
}

.asideR {
  border-left: 1px solid #e0e0e0;
  padding-left: 15px;
}

.chooseD a {
  display: block;
  padding: 5px 0;
}

.choose-department-checked {
  width: 100%;
  box-sizing: border-box;
  padding: 10px;
  height: 500px;
  overflow-y: auto;
  border: 1px solid #e6ebf5;
}

.choose-department-item {
  height: 30px;
  -js-display: flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.choose-department-item-text {
  width: 80%;
  user-select: none;
}

.choose-department-item-close {
  cursor: pointer;
  font-size: 16px;
}

::v-deep .el-container {
  height: 555px !important;
}

.footer {
  display: flex;
  justify-content: flex-end;
  padding: 15px;
}
</style>