<template>
    <div class="menu-wrapper">
        <template v-for="item in menus" v-if="!item.hidden"
            >
            
                <!-- v-if="!item.children && !item.alwaysShow" -->
            <router-link
                :to="item.path"
                :key="item.name"
            >
                <el-menu-item
                    :index="item.path"
                    :class="{ 'submenu-title-noDropdown': !isNest }"
                >
                    <svg-icon
                        v-if="item.meta && item.meta.icon"
                        :icon-class="item.meta.icon"
                    ></svg-icon>
                    <span v-if="item.meta && item.meta.title" slot="title">{{
                        item.meta.title
                    }}</span>
                </el-menu-item>
            </router-link>
            <!-- <router-link
                v-else-if="
                    hasOneShowingChildren(item.children, item) &&
                        !item.alwaysShow
                "
                :to="item.path + '/' + item.children[0].path"
                :key="item.children[0].name"
            >
                <el-menu-item
                    :index="item.path + '/' + item.children[0].path"
                    :class="{ 'submenu-title-noDropdown': !isNest }"
                >
                    <svg-icon
                        v-if="
                            item.children[0].meta && item.children[0].meta.icon
                        "
                        :icon-class="item.children[0].meta.icon"
                    ></svg-icon>
                    <span
                        v-if="
                            item.children[0].meta && item.children[0].meta.title
                        "
                        slot="title"
                        >{{ item.children[0].meta.title }}</span
                    >
                </el-menu-item>
            </router-link>
            <el-submenu v-else :index="item.name || item.path" :key="item.name">
                <template slot="title">
                    <svg-icon
                        v-if="item.meta && item.meta.icon"
                        :icon-class="item.meta.icon"
                    ></svg-icon>
                    <span v-if="item.meta && item.meta.title" slot="title">{{
                        item.meta.title
                    }}</span>
                </template>
                <template v-for="child in item.children" v-if="!child.hidden">
                    <sidebar-item
                        :is-nest="true"
                        class="nest-menu"
                        v-if="child.children && child.children.length > 0"
                        :routes="[child]"
                        :menus="[child]"
                        :key="child.path"
                    ></sidebar-item>
                    <router-link v-else :to="child.path" :key="child.name">
                        <el-menu-item :index="child.path" class="elMenuItem">
                            <svg-icon
                                v-if="child.meta && child.meta.icon"
                                :icon-class="child.meta.icon"
                            ></svg-icon>
                            <span
                                v-if="child.meta && child.meta.title"
                                slot="title"
                                >{{ child.meta.title }}</span
                            >
                        </el-menu-item>
                    </router-link>
                </template>
            </el-submenu> -->
        </template>
    </div>
</template>
<script>
export default {
    name: "SidebarItem",
    props: {
        menus: {
            type: Array,
            default() {
                return [];
            },
        },
        isNest: {
            type: Boolean,
            default: false,
        },
    },
    created() {
        // console.log("this.menus",JSON.stringify(this.menus));
        // console.log(this.menus)
    },
    methods: {
        hasOneShowingChildren(children, item) {
            if (!children) return true;
            const showingChildren = children.filter((item) => {
                return !item.hidden;
            });
            if (showingChildren.length === 1 && item.path === "") {
                return true;
            }
            return false;
        },
    },
};
</script>
<style scoped>
.menu-wrapper ::v-deep  .icon {
    vertical-align: -0.45em !important;
    margin-right: 10px !important;
}
/* .menu-wrapper ::v-deep  .el-submenu.is-active .el-submenu__title{background:rgba(192,0,0,1)!important;color:#fff!important;} */
.menu-wrapper ::v-deep  .el-submenu.is-active .el-submenu__title {
    /* border-top: #f5f5f5 1px solid; */
}
.elMenuItem ::v-deep  .icon {
    vertical-align: -0.45em !important;
    margin-right: 10px !important;
}
.nest-menu ::v-deep  .el-menu-item.is-active,
.nest-menu ::v-deep  .el-menu .el-submenu.is-active .el-submenu__title,
.nest-menu ::v-deep  .el-submenu.is-active .el-submenu__title,
.nest-menu ::v-deep  .is-active ul.el-menu li.is-active {
    background: rgba(192,0,0,1) !important;
    color: #fff !important;
}
.nest-menu ::v-deep  .is-active ul.el-menu li.is-active {
    line-height: 34px;
}
.nest-menu ::v-deep  .el-submenu.is-active .el-submenu__title {
    line-height: 32px;
}
.nest-menu ::v-deep  .el-submenu.is-active .el-submenu__title * {
    color: #fff !important;
}
.elMenuItem:hover,
.menu-wrapper ::v-deep  .elMenuItem.el-menu-item:hover,
.nest-menu ::v-deep  .el-submenu .el-submenu__title:hover,
.elMenuItem.is-active {
    background: rgba(192,0,0,1) !important;
    color: #fff !important;
}
.nest-menu ::v-deep  .el-submenu__icon-arrow {
    margin-top: -6px;
}
.nest-menu ::v-deep  .el-submenu.is-active .el-submenu__title {
    border-left: 0 none !important;
}
/* .el-submenu__title */
</style>
