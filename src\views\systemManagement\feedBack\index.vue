<template>
  <div class="app-container">
    <sb-el-table
        :table="table"
        @getList="getList"
        @handleTo="handleTo"
        @handleEdit="handleEdit"
        @handleFeed="handleFeed"
        @handleAdd="handleAdd"
        @updateTableData="updateTableData"
        @handleExport="handleExport"
        @handleAIAudit="handleAIAudit"
        @handleAIConfirm="handleAIConfirm"
        :on-ok="handleDoFun"
    >
      <template v-slot:cycle="{obj}">
        <span>第{{ obj.row.cycle }}期</span>
      </template>
      <template v-slot:data="{obj}">
        <span>{{ obj.row.startDate }}至{{ obj.row.endDate }}</span>
      </template>
    </sb-el-table>

    <el-dialog :title="dialogTitle" :visible.sync="viewD" :close-on-click-modal="false" append-to-body width="400">
      <div class="message tableForm">
        <sb-el-form
            ref="appForm1"
            :form="queryForm1"
            v-model="content"
            @handleStartTime="handleStartTime"
            @handleEndTime="handleEndTime"
            @handledeadline="handledeadline"
            :on-ok="handleDoFun"
        ></sb-el-form>
      </div>
      <div class="feedback-btn">
        <el-button type="primary" size="small" @click="onSave()">保存</el-button>
        <el-button type="primary" size="small" @click="dialogClose()">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import WorkOrder from "@/components/WorkOrder";
import FeedEdit from "@/components/FeedEdit";
import {feedexport, findProcessJoin, mzlRequest} from "@/api/process";
import {getQueryCycle, editQueryCycle, onPushCycle} from "@/api/systemManagement";
import store from "@/store";

export default {
  name: "processJoin",
  components: {WorkOrder, FeedEdit},
  data() {
    return {
      viewD: false,
      dialogTitle: "",

      gps: {
        type: "join",
        location: "",
        pmInsType: "",
      },

      cKey: 0,
      table: {
        modulName: "feedBack-反馈待办派发", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: true, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "80px",
          formItemList: [
            {label: "周期", key: "cycle", type: "input",},
          ],
        },
        tr: [
          {id: "cycle", label: "周期", prop: "cycle", show: 'template', template: 'cycle', width: 100},
          {id: "data", label: "填报周期", prop: "data", show: 'template', template: 'data', width: 200},
          {id: "deadline", label: "填报截止时间", prop: "deadline", width: 180},
          {id: "msg", label: "任务负责人短信模板", prop: "msg"},
          {id: "interfaceMsg", label: "接口人短信模板", prop: "interfaceMsg"},
          {id: "pushTime", label: "待办推送时间", prop: "pushTime", width: 180},
          {id: "applyTrueName", label: "派发人", prop: "applyTrueName", width: 140},
        ],
        // hasSetup:true,
        // setup:[],
        processType: [],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "600px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: "420",
          fixed: "right",
          data: [
            {id: "handleExport", name: "【导出】", fun: "handleExport"},
            {id: "handleTo", name: "【推送代办】", fun: "handleTo", show: "type|2"},
            {id: "handleEdit", name: "【编辑】", fun: "handleEdit", show: "type|2"},
            {id: "handleFeed", name: "【已派发】", fun: "handleFeed", show: "type|1"},
            {id: "handleAIConfirm", name: "【Ai周报确认】", fun: "handleAIConfirm", show: "isconfirm|1"},
            // {id: "handleAIAudit", name: "【AI周报审阅】", fun: "handleAIAudit"},
          ],
        },
        hasOtherQueryBtn: true, //是否有其他操作
        otherQueryBtn: {
          data: [
            {id: "add", type: "primary", name: "新增", fun: "handleAdd"}
          ]
        },
        hasPagination: false,
        listQuery: {size: 10, page: 1},
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
      //弹窗内容
      content: {
        applyUser: store.getters.user.truename,
        applyUserName: store.getters.user.username,
        startDate: '',
        endDate: '',
        deadline: '',
        msg: '',
        interfaceMsg: '',
        cycle: '',
      },
      queryForm1: {
        inline: true,
        labelWidth: "200px",
        labelPosition: 'left',
        formItemList: [
          {label: "周期", key: "cycle", type: "input", class: 'c12', disabled: true, rule: {required: true}},
          //   {class: "c4",label: "开始时间",key: "startTime",type: "date",subtype: "date",valueFormat: "yyyy-MM-dd",disabledDate: "afterDate",changeFun: "handleStartTime",rule: {required: true}},
          //   {class: "c4",label: "结束时间",key: "endTime",type: "date",subtype: "date",valueFormat: "yyyy-MM-dd",rule: {required: true}},
          {
            label: "填报周期开始时间",
            key: "startDate",
            type: "date",
            class: 'c12',
            subtype: "date",
            valueFormat: "yyyy-MM-dd",
            disabled: false,
            rule: {required: true},
            changeFun: "handleStartTime"
          },
          {
            label: "填报周期结束时间",
            key: "endDate",
            type: "date",
            class: 'c12',
            subtype: "date",
            valueFormat: "yyyy-MM-dd",
            disabled: false,
            rule: {required: true},
            changeFun: "handleEndTime"
          },
          {
            label: "填报截止时间",
            key: "deadline",
            type: "date",
            class: 'c12',
            subtype: "datetime",
            disabled: false,
            rule: {required: true},
            changeFun: "handledeadline"
          },
          {
            label: "任务负责人短信模板",
            key: "msg",
            type: "input",
            inputType: "textarea",
            class: 'c12',
            disabled: false,
            rule: {required: true}
          },
          {
            label: "接口人短信模板",
            key: "interfaceMsg",
            type: "input",
            inputType: "textarea",
            class: 'c12',
            disabled: false,
            rule: {required: true}
          },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    async handleAIAudit (scope) {
      await mzlRequest({
        url: '/action/collection/pushAi?latitudeId=' + scope.row.id + '&type=1',
      })
    },
    async handleAIConfirm (scope) {
      await mzlRequest({
        url: '/action/collection/pushAi?latitudeId=' + scope.row.id + '&type=0',
      })
    },
    async handleExport(scope) {
      let res = await mzlRequest({
        url: '/action/distribute/aiWord?latitudeId=' + scope.row.id,
        contentType: "application/json;charset=UTF-8",
        loading: true,
        responseType: "blob"
      })
      if (res.data) {
        this.util.blobDownload(res.data, res.filename);
      } else {
        this.$message({
          message: '导出失败',
          type: 'warning',
          duration: 1500
        });
      }
    },
    onSave() {
      let obj = JSON.parse(JSON.stringify(this.content))
      obj.cycle = obj.cycle.slice(1, -1)

      this.$refs['appForm1'].$children[0].validate((valid) => {
        if (!valid) {
          this.$message({
            message: "表单数据校验不通过",
            type: "warning",
            duration: 1500
          });
          return false;
        } else {
          let start = new Date(obj.startDate).getTime()
          let end = new Date(obj.endDate).getTime()
          let deadline = new Date(obj.deadline).getTime()
          if (!(end > start)) {
            this.content.endDate = ''
            this.$message({
              message: "填报周期结束时间要在填报周期开始时间之后",
              type: "warning",
              duration: 1500
            })
            return
          }
          if (!(deadline > start)) {
            this.content.deadline = ''
            this.$message({
              message: "填报截止时间要在填报周期开始时间之后",
              type: "warning",
              duration: 1500
            })
            return
          }
          editQueryCycle(obj).then((res) => {
            //   this.$message({
            // 	message: "保存成功",
            // 	type: "success",
            // 	duration: 1500
            // })
            this.dialogClose()
          }).catch((err) => {
            // this.$message({
            // 	message: err.msg,
            // 	type: "warning",
            // 	duration: 1500
            // })
          })
        }
      });
    },
    //【推送代办】
    handleTo(val) {

      this.$confirm('请确认是否推送待办？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // this.$message({
        //   type: 'success',
        //   message: '推送成功!'
        // });
        onPushCycle(val.row.id).then((res) => {

        }).catch((err) => {

        });

      }).catch(() => {

      });
    },
    //【编辑】
    handleEdit(val) {
      console.log(val);
      this.resetContnt()
      this.viewD = true
      this.dialogTitle = '编辑'
      this.content.cycle = `第${val.row.cycle}期`
      this.content.startDate = val.row.startDate
      this.content.endDate = val.row.endDate
      this.content.deadline = val.row.deadline
      this.content.msg = val.row.msg
      this.content.interfaceMsg = val.row.interfaceMsg
      this.content.id = val.row.id
    },
    //【已派发】
    handleFeed() {

    },
    resetContnt() {
      let obj = {
        applyUser: store.getters.user.truename,
        applyUserName: store.getters.user.username,
        startDate: '',
        endDate: '',
        deadline: '',
        msg: '',
        interfaceMsg: '',
        cycle: '',
      }
      this.content = obj
    },
    //新增
    handleAdd() {
      this.resetContnt()
      let list = this.table.data || []
      // console.log(list)
      let num = list.length > 0 ? list[0].cycle + 1 : 1
      this.viewD = true
      this.dialogTitle = '新增'
      this.content.cycle = `第${num}期`
      this.content.msg = `您好，您收到豫起奋发系统${this.content.cycle}的任务反馈待办，反馈XX月XX日至XX月XX日周期内的任务进展情况，截止时间XX月XX日XX:XX:XX，逾期后待办将无法办理，请您及时关注。`
      this.content.interfaceMsg = `您好，豫起奋发系统${this.content.cycle}的任务反馈待办已派发，任务负责人需反馈XX月XX日至XX月XX日期内的任务进展情况，截止时间XXXX-XX-XX XX:XX:XX。您可以进入系统的本期任务菜单查看填报内容并进行催办，请知悉。`
    },
    handleStartTime(obj, value) {
      let cycle = this.content.cycle
      let startDate = value || 'XX月XX日'
      let endDate = this.content.endDate || 'XX月XX日'
      let deadline = this.content.deadline || 'XXXX-XX-XX XX:XX:XX'
      this.content.msg = `您好，您收到豫起奋发系统${cycle}的任务反馈待办，反馈${startDate}至${endDate}周期内的任务进展情况，截止时间${deadline}，逾期后待办将无法办理，请您及时关注。`
      this.content.interfaceMsg = `您好，豫起奋发系统${cycle}的任务反馈待办已派发，任务负责人需反馈${startDate}至${endDate}日期内的任务进展情况，截止时间${deadline}。您可以进入系统的本期任务菜单查看填报内容并进行催办，请知悉。`
    },
    handleEndTime(obj, value) {

      let cycle = this.content.cycle
      let startDate = this.content.startDate || 'XX月XX日'
      let endDate = value || 'XX月XX日'
      let deadline = this.content.deadline || 'XXXX-XX-XX XX:XX:XX'
      this.content.msg = `您好，您收到豫起奋发系统${cycle}的任务反馈待办，反馈${startDate}至${endDate}周期内的任务进展情况，截止时间${deadline}，逾期后待办将无法办理，请您及时关注。`
      this.content.interfaceMsg = `您好，豫起奋发系统${cycle}的任务反馈待办已派发，任务负责人需反馈${startDate}至${endDate}日期内的任务进展情况，截止时间${deadline}。您可以进入系统的本期任务菜单查看填报内容并进行催办，请知悉。`
    },
    handledeadline(obj, value) {
      let cycle = this.content.cycle
      let startDate = this.content.startDate || 'XX月XX日'
      let endDate = this.content.endDate || 'XX月XX日'
      let deadline = value || 'XXXX-XX-XX XX:XX:XX'
      this.content.msg = `您好，您收到豫起奋发系统${cycle}的任务反馈待办，反馈${startDate}至${endDate}周期内的任务进展情况，截止时间${deadline}，逾期后待办将无法办理，请您及时关注。`
      this.content.interfaceMsg = `您好，豫起奋发系统${cycle}的任务反馈待办已派发，任务负责人需反馈${startDate}至${endDate}日期内的任务进展情况，截止时间${deadline}。您可以进入系统的本期任务菜单查看填报内容并进行催办，请知悉。`
    },

    // 查询列表
    getList(listQuery) {
      this.table.loading = true;
      getQueryCycle(listQuery || this.table.listQuery).then((res) => {
        this.table.loading = false;
        let list = res.data || []
        list.forEach((p) => {
          p.type = p.type == 1 ? 1 : 2
          p.isconfirm = new Date(p.deadline).getTime() < new Date().getTime() ? 1 : 0
        })
        this.table.data = list;
        // this.table.total = res.data.totalElements;
      }).catch((err) => {
        this.table.loading = false;
      });
    },

    // 办理
    handleTodo(obj) {
      // 参数
      this.gps = {
        type: "join",
        location: obj.row.activityDefId,
        pmInsType: obj.row.pmInsType,
        pmInsId: obj.row.pmInsId,
        taskId: obj.row.taskId,
        processInstId: obj.row.processInstId,
        processDefinitionId: obj.row.processDefId,
        processDefKey: obj.row.processDefId
      };

      // 工单标题
      var th = this.util.appNameTH(obj.row.pmInsType);
      this.dialogTitle = th.type + (obj.row.title || "") + "-审批";

      this.cKey++;
      this.viewD = true;
    },

    // 关闭弹框
    dialogClose() {
      this.viewD = false;
      this.resetContnt()
      this.getList();
    },

    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },

    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }
};
</script>
<style scoped>
::v-deep .el-table .cell > .inlineB > .el-button {
  padding: 7px 0;
}
/* 修改公共样式弹框样式 */
::v-deep .el-dialog__header {
  text-align: center !important;
  /* background: white !important; */
  /* color: black;
  font-size: 14px;
  font-weight: bold;
  border-bottom: 1px solid #f2f2f2 !important; */
}

/* ::v-deep .el-dialog__title{color: black !important;font-size: 15.5px;}
::v-deep .el-dialog__headerbtn .el-dialog__close{
  color: black;
} */
::v-deep .fl.ml15 {
  display: none;
}

.feedback-btn {
  margin: 20px auto 0;
  padding-bottom: 30px;
  width: 180px;
  display: flex;
  justify-content: space-between;
}
</style>