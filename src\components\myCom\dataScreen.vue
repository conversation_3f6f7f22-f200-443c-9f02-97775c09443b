<template>
  <div class="screen-wrapper">
    <div class="screen" id="screen">
      <div class="close"  @click="closeFun()">关闭</div>

      <div class="top_b">
        <img src="" alt="" />
      </div>
      <div class="content flexb">
        <div class="left_b">
          <div class="stat">
            <div class="s_tit">工单数量统计</div>
            <div class="chart chart1">
              <div class="lbox" ref="chart1"></div>
              <div class="rbox">
                <ul>
                  <li>
                    <div> <span style="color: #0099ff">•</span>&nbsp;工单总数量 </div>
                    <div style="font-size: 22px;cursor: pointer;"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;" @click="viewDialogs('all','all')">{{ data1.total }}</strong> </div>
                  </li>
                  <li>
                    <div> <span style="color: #0099ff">•</span>&nbsp;本月未办结数 </div>
                    <div style="font-size: 22px;"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;" @click="viewDialogs('all','processTask')">{{ data1.monthUndoneNum }}</strong> </div>
                  </li>
                  <li>
                    <div> <span style="color: #0099ff">•</span>&nbsp;本月已办结数 </div>
                    <div style="font-size: 22px"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;" @click="viewDialogs('all','processJoin')">{{ data1.monthDoneNum }}</strong> </div>
                  </li>
                  <li>
                    <div> <span style="color: #0099ff">•</span>&nbsp;平均满意度得分 </div>
                    <div style="font-size: 22px"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;">{{ data1.avgSatisfaction }}</strong> </div>
                  </li>
                  <li>
                    <div> <span style="color: #0099ff">•</span >&nbsp;全部工单平均处理时长 </div>
                    <div style="font-size: 22px"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;">{{ data1.avgTransitTime }}</strong> </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div class="stat">
            <div class="s_tit">政企客户部工单统计</div>
            <div class="chart chart2">
              <div class="lbox" ref="chart2"></div>
              <div class="rbox">
                <ul>
                  <li>
                    <div> <span style="color: #0099ff">•</span>&nbsp;工单总数量 </div>
                    <div style="font-size: 22px;cursor: pointer;"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;" @click="viewDialogs('department','all',code2,'政企客户部')">{{ data2.total }}</strong> </div>
                  </li>
                  <li>
                    <div> <span style="color: #0099ff">•</span>&nbsp;本月未办结数 </div>
                    <div style="font-size: 22px;"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;" @click="viewDialogs('department','processTask',code2,'政企客户部')">{{ data2.monthUndoneNum }}</strong> </div>
                  </li>
                  <li>
                    <div> <span style="color: #0099ff">•</span>&nbsp;本月已办结数 </div>
                    <div style="font-size: 22px"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;" @click="viewDialogs('department','processJoin',code2,'政企客户部')">{{ data2.monthDoneNum }}</strong> </div>
                  </li>
                  <li>
                    <div> <span style="color: #0099ff">•</span>&nbsp;平均满意度得分 </div>
                    <div style="font-size: 22px"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;">{{ data2.avgSatisfaction }}</strong> </div>
                  </li>
                  <li>
                    <div> <span style="color: #0099ff">•</span >&nbsp;全部工单平均处理时长 </div>
                    <div style="font-size: 22px"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;">{{ data2.avgTransitTime }}</strong> </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div class="stat">
            <div class="s_tit">信息服务能力中心工单统计</div>
            <div class="chart chart3">
              <div class="lbox" ref="chart3"></div>
              <div class="rbox">
                <ul>
                  <li>
                    <div> <span style="color: #0099ff">•</span>&nbsp;工单总数量 </div>
                    <div style="font-size: 22px;cursor: pointer;"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;" @click="viewDialogs('department','all',code3,'信息服务能力中心')">{{ data3.total }}</strong> </div>
                  </li>
                  <li>
                    <div> <span style="color: #0099ff">•</span>&nbsp;本月未办结数 </div>
                    <div style="font-size: 22px;"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;" @click="viewDialogs('department','processTask',code3,'信息服务能力中心')">{{ data3.monthUndoneNum }}</strong> </div>
                  </li>
                  <li>
                    <div> <span style="color: #0099ff">•</span>&nbsp;本月已办结数 </div>
                    <div style="font-size: 22px"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;" @click="viewDialogs('department','processJoin',code3,'信息服务能力中心')">{{ data3.monthDoneNum }}</strong> </div>
                  </li>
                  <li>
                    <div> <span style="color: #0099ff">•</span>&nbsp;平均满意度得分 </div>
                    <div style="font-size: 22px"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;">{{ data3.avgSatisfaction }}</strong> </div>
                  </li>
                  <li>
                    <div> <span style="color: #0099ff">•</span >&nbsp;全部工单平均处理时长 </div>
                    <div style="font-size: 22px"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;">{{ data3.avgTransitTime }}</strong> </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div class="stat">
            <div class="s_tit">行业客户拓展中心工单统计</div>
            <div class="chart chart4">
              <div class="lbox" ref="chart4"></div>
              <div class="rbox">
                <ul>
                  <li>
                    <div> <span style="color: #0099ff">•</span>&nbsp;工单总数量 </div>
                    <div style="font-size: 22px;cursor: pointer;"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;" @click="viewDialogs('department','all',code4,'行业客户拓展中心')">{{ data4.total }}</strong> </div>
                  </li>
                  <li>
                    <div> <span style="color: #0099ff">•</span>&nbsp;本月未办结数 </div>
                    <div style="font-size: 22px;"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;" @click="viewDialogs('department','processTask',code4,'行业客户拓展中心')">{{ data4.monthUndoneNum }}</strong> </div>
                  </li>
                  <li>
                    <div> <span style="color: #0099ff">•</span>&nbsp;本月已办结数 </div>
                    <div style="font-size: 22px"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;" @click="viewDialogs('department','processJoin',code4,'行业客户拓展中心')">{{ data4.monthDoneNum }}</strong> </div>
                  </li>
                  <li>
                    <div> <span style="color: #0099ff">•</span>&nbsp;平均满意度得分 </div>
                    <div style="font-size: 22px"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;">{{ data4.avgSatisfaction }}</strong> </div>
                  </li>
                  <li>
                    <div> <span style="color: #0099ff">•</span >&nbsp;全部工单平均处理时长 </div>
                    <div style="font-size: 22px"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;">{{ data4.avgTransitTime }}</strong> </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div class="stat">
            <div class="s_tit">网络部（网络优化中心）工单统计</div>
            <div class="chart chart5">
              <div class="lbox" ref="chart5"></div>
              <div class="rbox">
                <ul>
                  <li>
                    <div> <span style="color: #0099ff">•</span>&nbsp;工单总数量 </div>
                    <div style="font-size: 22px;cursor: pointer;"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;" @click="viewDialogs('all','all',code5,'网络部（网络优化中心）')">{{ data5.total }}</strong> </div>
                  </li>
                  <li>
                    <div> <span style="color: #0099ff">•</span>&nbsp;本月未办结数 </div>
                    <div style="font-size: 22px;"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;" @click="viewDialogs('all','processTask',code5,'网络部（网络优化中心）')">{{ data5.monthUndoneNum }}</strong> </div>
                  </li>
                  <li>
                    <div> <span style="color: #0099ff">•</span>&nbsp;本月已办结数 </div>
                    <div style="font-size: 22px"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;" @click="viewDialogs('all','processJoin',code5,'网络部（网络优化中心）')">{{ data5.monthDoneNum }}</strong> </div>
                  </li>
                  <li>
                    <div> <span style="color: #0099ff">•</span>&nbsp;平均满意度得分 </div>
                    <div style="font-size: 22px"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;">{{ data5.avgSatisfaction }}</strong> </div>
                  </li>
                  <li>
                    <div> <span style="color: #0099ff">•</span >&nbsp;全部工单平均处理时长 </div>
                    <div style="font-size: 22px"> &nbsp;&nbsp;&nbsp;<strong style="cursor: pointer;">{{ data5.avgTransitTime }}</strong> </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        <div class="mid_b">
          <div class="up">
            <div class="query">
              <span>月份：</span>
              <el-date-picker
                style="width: 160px"
                size="mini"
                @change="monthFun()"
                value-format="yyyy-MM"
                :clearable=false
                :editable=false
                v-model="queryMonth"
                type="month"
                placeholder="选择月"
              >
              </el-date-picker>
            </div>
            <div ref="map" id="map"></div>
          </div>
          <div class="down">
            <div class="s_tit">各部门工单分布</div>
            <div class="line" style="width:100%;border: 1px solid #f8f9fb;height: 1px;margin-top:10px"></div>
            <div class="chart6"  style="height:86%"   ref="barChart1" ></div>
          </div>
        </div>
        <div class="right_b">
          <div class="up">
            <div class="s_tit">行业客户拓展中心工单分布</div>
            <div class="line" style="width:100%;border: 1px solid #f8f9fb;height: 1px;margin-top:10px"></div>
            <div class="charta chart7" ref="chart7"></div>
          </div>
          <div class="mid">
            <div class="s_tit">政企客户部工单分布</div>
            <div class="line" style="width:100%;border: 1px solid #f8f9fb;height: 1px;margin-top:10px"></div>
            <div class="charta chart8" ref="chart8"></div>
          </div>
          <div class="down">
            <div class="s_tit">信息分布服务能力中心</div>
            <div class="line" style="width:100%;border: 1px solid #f8f9fb;height: 1px;margin-top:10px"></div>
            <div class="charta chart9"  ref="chart9"></div>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import { mapGetters } from "vuex";

import henanData from "./henan.js";




export default {
  name: "dateScreen",
  components: {  },
  computed: {
    ...mapGetters(["user"]),
  },
  created() {
    this.queryMonth = this.util.getNow().substring(0, 7);
  },
  mounted() {
    // 初始化自适应  ----在刚显示的时候就开始适配一次
    this.handleScreenAuto();
    // 绑定自适应函数   ---防止浏览器栏变化后不再适配
    window.onresize = () => this.handleScreenAuto();
    this.markChart1();
    this.markChart2();
    this.markChart3();
    this.markChart4();
    this.markChart5();
    this.mapEchartsFun();
    this.markbarChart1();

    this.markbarChart2()
    this.markbarChart3()
    this.markbarChart4()
  },

  destroyed() {
    window.onresize = null;
  },
  data() {
    return {
      queryMonth: "", //月份查询
      code2: "2140887567677891967", //政企客户部
      code3: "1117317882106929152", //信息服务能力中心
      code4: "2840736922849932269", //行业客户拓展中心
      code5: "4772352556325512569", //网络部(网络优化中心)

      data1: {},
      data2: {},
      data3: {},
      data4: {},
      data5: {},

      option:{},
      viewR:false,
      item:{},
      akey:0
    };
  },
  methods: {
    //#region
    markChart1() {
      const chart1 = this.$refs.chart1;
      if (chart1) {
        const myChart = this.$echarts.init(chart1);
        var option = {
          tooltip: { trigger: "item" },
          legend: {
            show: false,
            orient: "vertical",
            left: "left",
            itemWidth: 15, // 设置图例宽度
            itemHeight: 10, // 设置图例高度
            textStyle: {
              fontSize: 12, // 设置文字大小
            },
          },
          color: ["#016af4", "#ff8750", "#1adab5", "#C1EBDD", "#FFC851"],
          series: [
            {
              // name: '工单数量统计',
              type: "pie",
              radius: ["55%", "75%"],
              center: ["50%", "50%"], // 设置环形图的显示位置，此处为居中显示
              avoidLabelOverlap: false,
              label: { show: false, position: "center" },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 12,
                  fontWeight: "bold",
                  shadowColor: "rgba(0, 0, 0, 0.5)",
                },
              },
              labelLine: { show: false },
              data: [],
            },
          ],
        };

      }
    },
    markChart2() {
      const chart2 = this.$refs.chart2;
      let data2 = [];
      if (chart2) {
        const myChart = this.$echarts.init(chart2);
        var option = {
          tooltip: { trigger: "item" },
          legend: {
            show: false,
            orient: "vertical",
            left: "left",
            itemWidth: 15, // 设置图例宽度
            itemHeight: 10, // 设置图例高度
            textStyle: {
              fontSize: 12, // 设置文字大小
            },
          },
          color: ["#016af4", "#ff8750", "#1adab5", "#C1EBDD", "#FFC851"],
          series: [
            {
              // name: '政企客户部工单统计',
              type: "pie",
              radius: ["55%", "75%"],
              center: ["50%", "50%"], // 设置环形图的显示位置，此处为居中显示
              avoidLabelOverlap: false,
              label: { show: false, position: "center" },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 12,
                  fontWeight: "bold",
                  shadowColor: "rgba(0, 0, 0, 0.5)",
                },
              },
              labelLine: { show: false },
              data: [],
            },
          ],
        };

      }
    },
    markChart3() {
      const chart3 = this.$refs.chart3;
      let data3 = [];
      if (chart3) {
        const myChart = this.$echarts.init(chart3);
        var option = {
          tooltip: { trigger: "item" },
          legend: {
            show: false,
            orient: "vertical",
            left: "left",
            itemWidth: 15, // 设置图例宽度
            itemHeight: 10, // 设置图例高度
            textStyle: {
              fontSize: 12, // 设置文字大小
            },
          },
          color: ["#016af4", "#ff8750", "#1adab5", "#C1EBDD", "#FFC851"],
          series: [
            {
              // name: '信息服务能力中心工单统计',
              type: "pie",
              radius: ["55%", "75%"],
              center: ["50%", "50%"], // 设置环形图的显示位置，此处为居中显示
              avoidLabelOverlap: false,
              label: { show: false, position: "center" },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 12,
                  fontWeight: "bold",
                  shadowColor: "rgba(0, 0, 0, 0.5)",
                },
              },
              labelLine: { show: false },
              data: [],
            },
          ],
        };

      }
    },
    markChart4() {
      const chart4 = this.$refs.chart4;
      let data4 = [];
      if (chart4) {
        const myChart = this.$echarts.init(chart4);
        var option = {
          tooltip: { trigger: "item" },
          legend: {
            show: false,
            orient: "vertical",
            left: "left",
            itemWidth: 15, // 设置图例宽度
            itemHeight: 10, // 设置图例高度
            textStyle: {
              fontSize: 12, // 设置文字大小
            },
          },
          color: ["#016af4", "#ff8750", "#1adab5", "#C1EBDD", "#FFC851"],
          series: [
            {
              // name: '行业客户拓展中心工单统计',
              type: "pie",
              radius: ["55%", "75%"],
              center: ["50%", "50%"], // 设置环形图的显示位置，此处为居中显示
              avoidLabelOverlap: false,
              label: { show: false, position: "center" },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 12,
                  fontWeight: "bold",
                  shadowColor: "rgba(0, 0, 0, 0.5)",
                },
              },
              labelLine: { show: false },
              data: [],
            },
          ],
        };

      }
    },
    markChart5() {
      const chart5 = this.$refs.chart5;
      let data5 = [];
      if (chart5) {
        const myChart = this.$echarts.init(chart5);
        var option = {
          tooltip: { trigger: "item" },
          legend: {
            show: false,
            orient: "vertical",
            left: "left",
            itemWidth: 15, // 设置图例宽度
            itemHeight: 10, // 设置图例高度
            textStyle: {
              fontSize: 12, // 设置文字大小
            },
          },
          color: ["#016af4", "#ff8750", "#1adab5", "#C1EBDD", "#FFC851"],
          series: [
            {
              // name: '网络部（网络优化中心）工单统计',
              type: "pie",
              radius: ["55%", "75%"],
              center: ["50%", "50%"], // 设置环形图的显示位置，此处为居中显示
              avoidLabelOverlap: false,
              label: { show: false, position: "center" },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 12,
                  fontWeight: "bold",
                  shadowColor: "rgba(0, 0, 0, 0.5)",
                },
              },
              labelLine: { show: false },
              data: [],
            },
          ],
        };

      }
    },
    //#endregion

    monthFun() {
      this.markChart1();
      this.markChart2();
      this.markChart3();
      this.markChart4();
      this.markChart5();
      this.mapEchartsFun();
      this.markbarChart1();

      this.markbarChart2()
      this.markbarChart3()
      this.markbarChart4()
    },

    //地图渲染
    mapEchartsFun(areaName) {
      let mymapChart = this.$echarts.init(this.$refs.map);
      let that = this
      mymapChart.on('click', function (params) {
        that.viewDialogs('branch','all',params.data.branchCode,params.data.name)
      });
      this.$echarts.registerMap("henan", henanData);

      // 定义标记点的数据
      let optionMap = {
        legend: {},
        tooltip:{
          // show:true
          triger: 'item',
          formatter: function(params, ticket, callback) {
              if (params.componentType == "series") {
                  return  params.data.name + "-工单总数量：" + params.data.value;
              } else if (params.componentType == "markPoint") {
                return params.data.name + "-工单总数量：" + optionMap.series[0].data[0].value;
              }
          }
        },
        visualMap: {
          min: 0,
          max: 100,
          left: 20,
          bottom: 20,
          calculable: true,
          text: ["高", "低"],
          inRange: {
            color: [
              "rgb(70, 240, 252)",
              "rgb(250, 220, 46)",
              "rgb(245, 38, 186)",
            ],
          },
          textStyle: {
            color: "#fff",
          },
        },
        series: [
          {
            type: "map",
            map: "henan",
            aspectScale: 0.85, //用于 scale 地图的长宽比，如果设置了projection则无效
            zoom: 1.21, //当前视角的缩放比例
            roam: false, //是否开启鼠标缩放和平移漫游。默认不开启。如果只想要开启缩放或者平移，可以设置成 'scale' 或者 'move'。设置成 true 为都开启
            dragEnable: false, // 地图是否可通过鼠标拖拽平移，默认为true
            zoomEnable: true, //地图是否可缩放，默认值为true
            label: {
              normal: {
                show: true,
              },
              emphasis: {
                show: true,
              },
            },
            itemStyle: {
              //地图区域的多边形 图形样式。
              normal: {
                areaColor: "#d2eefc",
                borderColor: "#6bc6f3",
                borderWidth: 1.5,
              },
              emphasis: {
                areaColor: "#6bc6f3", //鼠标经过区域颜色
                label: {
                  //鼠标经过区域内文字样式
                  show: true,
                  textStyle: { color: "#333", fontSize: 14, fontWeight: 700 },
                },
              },
            },
            data: [],
            markPoint: {
              symbol: "triangle", // 标记点的图形
              symbolSize: 12, // 标记点的大小
              itemStyle: {
                color: "red", // 标记点的颜色
              },
              data: [
                {
                  name: "省公司",
                  branchCode:'4772338661636601428',
                  coord: [113.535912, 34.757975],
                  label: {
                    show: true, // 显示标记点的文本
                    formatter: "{b}", // 标记点文本的格式化字符串，这里使用{name}表示取数据项的name属性
                    position: "right", // 标记点文本的位置，可以是'top'、'bottom'、'left'、'right'等
                  },
                },
              ],
            },
          },
        ],
      };

    },


    // #region
    markbarChart1(){ //各部门工单分布
      let barChart1 = this.$refs.barChart1;
      let bardata1 = [];
      let myChart = this.$echarts.init(barChart1);
      let that = this
      myChart.on('click', function (params) {
        if(params.name == '政企客户部'){
          that.viewDialogs('department','all',that.code2,params.name)
        }
        if(params.name == '信息服务能力中心'){
          that.viewDialogs('department','all',that.code3,params.name)
        }
        if(params.name == '行业客户拓展中心'){
          that.viewDialogs('department','all',that.code4,params.name)
        }
        if(params.name == '网络部（网络优化中心）'){
          that.viewDialogs('department','all',that.code5,params.name)
        }
      });
      this.option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: { bottom:10},
        grid: {
          left: '5%',
          right: '10%',
          top:20,
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          // boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: ['网络部（网络优化中心）', '行业客户拓展中心', '信息服务能力中心', '政企客户部',]
        },
        series: [
          {
            name: '评价处理时长（小时）',
            type: 'bar',
            data: [0,0,0,0],
            itemStyle: { normal: { color: '#76b6ff' } }
          },
          {
            name: '工单数量',
            type: 'bar',
            data: [0,0,0,0],
            itemStyle: { normal: { color: '#ff696a' } }
          }
        ]
      };

      this.option && myChart.setOption(this.option);
    },
    
    //行业客户拓展中心
    markbarChart2(){
      const chart7 = this.$refs.chart7;
      const myChart = this.$echarts.init(chart7);
      let data = []
      let that = this
      myChart.on('click', function (params) {
        for(let i in data){
          if(params.data[0] == data[i].orgName){
            that.viewDialogs('org','all',data[i].orgCode,data[i].orgName)
          }
        }
      });
      let option = {
        legend: {},
        tooltip: {},
        grid: {
          left: '5%',
          right: '10%',
          top:30,
          bottom: '0%',
          containLabel: true
        },
        legend: { top:5},
        dataset: {
          source: [
            ['product', '工单数量', '评价处理时长（小时）'],
          ]
        },
        xAxis: { type: 'category' , axisLabel: {
            rotate: -20 // 旋转角度为 -45 度
        }},
        yAxis: {},
        series: [
            { type: 'bar', itemStyle: { normal: { color: '#5a9bd5' } } },
            { type: 'bar', itemStyle: { normal: { color: '#ed7d31' } } }
        ]
      };

    },

    //政企客户部工单分布
    markbarChart3(){
      const chart8 = this.$refs.chart8;
      const myChart = this.$echarts.init(chart8);
      let data = []
      let that = this
      myChart.on('click', function (params) {
        for(let i in data){
          if(params.data[0] == data[i].orgName){
            that.viewDialogs('org','all',data[i].orgCode,data[i].orgName)
          }
        }
      });
      let option = {
        legend: {},
        tooltip: {},
        grid: {
          left: '5%',
          right: '10%',
          top:30,
          bottom: '0%',
          containLabel: true
        },
        legend: { top:5},
        dataset: {
          source: [
            ['product', '工单数量', '评价处理时长（小时）'],
          ]
        },
        xAxis: { type: 'category' , axisLabel: {
            rotate: -20 // 旋转角度为 -20 度
        }},
        yAxis: {},
        series: [
            { type: 'bar', itemStyle: { normal: { color: '#186df5' } } },
            { type: 'bar', itemStyle: { normal: { color: '#4ac8de' } } }
        ]
      };

    },
    //信息分布服务能力中心
    markbarChart4(){
      const chart9 = this.$refs.chart9;
      const myChart = this.$echarts.init(chart9);
      let data = []
      let that = this
      myChart.on('click', function (params) {
        for(let i in data){
          if(params.data[0] == data[i].orgName){
            that.viewDialogs('org','all',data[i].orgCode,data[i].orgName)
          }
        }
      });
      let option = {
        legend: {},
        tooltip: {},
        grid: {
          left: '5%',
          right: '10%',
          top:30,
          bottom: '0%',
          containLabel: true
        },
        legend: { top:5},
        dataset: {
          source: [
             ['product', '工单数量', '评价处理时长（小时）'],
          ]
        },
        xAxis: { type: 'category' , axisLabel: {
            rotate: -20 // 旋转角度为 -20 度
        }},
        yAxis: {},
        series: [
            { type: 'bar', itemStyle: { normal: { color: '#76b6ff' } } },
            { type: 'bar', itemStyle: { normal: { color: '#ffc168' } } }
        ]
      };

    },
    // #endregion

    //数据大屏自适应函数
    handleScreenAuto() {
      const designDraftWidth = 1980; //设计稿的宽度
      const designDraftHeight = 855; //设计稿的高度
      //根据屏幕的变化适配的比例
      const scale =
        document.documentElement.clientWidth /
          document.documentElement.clientHeight <
        designDraftWidth / designDraftHeight
          ? document.documentElement.clientWidth / designDraftWidth
          : document.documentElement.clientHeight / designDraftHeight;
      //缩放比例
      document.querySelector(
        "#screen"
      ).style.transform = `scale(${scale}) translate(-50%)`;
    },

    closeFun(){
      this.$emit('closeDia')
    },
    closeDias(){
      this.viewR = false
    },
    viewDialogs(queryType,state,code,name){
      this.item = {
          'queryType':queryType,
          'state':state,
          'startTime':this.getFirstAndLastDayOfMonth(this.queryMonth).firstDay.toISOString().slice(0,10)+' '+'00:00:00',
          'endTime':this.getFirstAndLastDayOfMonth(this.queryMonth).lastDay.toISOString().slice(0,10)+' '+'23:59:59',
          'openName':name,
          'code':code,
      }

      this.akey++
      this.viewR = true
     
    },

    // 输入要获取的月份，格式为 'yyyy-mm'
    getFirstAndLastDayOfMonth(month) {
        // 将输入的月份字符串转换为日期对象
        var date = new Date(month + '-01');
        // 获取月份的第一天
        var firstDay = new Date(date.getFullYear(), date.getMonth(), 2);
        // 获取月份的最后一天
        var lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 1);
        // 返回第一天和最后一天的日期
        return {
            firstDay: firstDay,
            lastDay: lastDay
        };
    }
  },
};
</script>

<style scoped>
div {
  /* border: 1px solid #c52121; */
}
.top_b {
  width: 100%;
  margin-top: 10px;
  background-color: #fff;
  position: relative;
}
.top_b img {
  width: 100%;
}

.screen-root {
  height: 100%;
  width: 100%;
}

.screen {
  display: inline-block;
  width: 1980px;
  height: 945px;
  transform-origin: 0 0;
  position: absolute;
  left: 50%;
}
.flexb {
  display: flex;
  justify-content: space-between;
}
.content {
  width: 100%;
  height: calc(100% - 70px);
  padding: 10px;
  background-color: #f8f9fb;
}
.content .left_b,
.content .mid_b,
.content .right_b {
  width: 33%;
  height: 100%;
  /* padding: 10px; */
  border-radius: 5px;
}
.content .left_b {
  background-color: #fff;
}

.stat {
  height: 20%;
  /* padding: 10px; */
}
.stat .chart {
  width: 100%;
  height: 88%;
  display: flex;
}

.stat .chart .lbox {
  width: 30%;
  height: 100%;
}
.stat .chart .rbox {
  width: 70%;
  font-size: 16px;
}

.mid_b .up {
  width: 100%;
  height: 59%;
  padding: 10px;
  background-color: #fff;
}
.mid_b .down {
  width: 100%;
  height: 40%;
  background-color: #fff;
  margin-top: 10px;
  padding: 10px;
}
.right_b .up,
.right_b .down,
.right_b .mid {
  width: 100%;
  height: 32.6%;
  padding: 10px;
  background-color: #fff;
  margin-bottom: 10px;
}

.chart7,
.chart8,
.chart9{
  height: 86%;
}



.s_tit {
  font-size: 16px;
  border-left: 3px solid #0099ff;
  padding-left: 5px;
  font-weight: 700;
}
.chart .rbox ul {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  font-size: 16px;
  padding: 10px;
}
.chart .rbox ul li {
  min-width: 33.3%;
  height: 50%;
}

#map {
  width: 100%;
  height: 94%;
}
.mid_b .query {
  font-size: 16px;
  font-weight: 700;
  margin-left: 5%;
  width: 35%;
}

.close{
  position: absolute;
  /* top: 20; */
  /* right: 20; */

  /* float: right; */
  z-index: 999999;
  right: 50px;
  top: 26px;

  width: 60px;
  height: 30px;
  text-align: center;
  background: #5a9bd5;
  color: #fff;
  line-height: 30px;
  font-size: 16px;
  cursor: pointer;
}
</style>