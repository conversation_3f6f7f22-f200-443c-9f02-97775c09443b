import request from "@/assets/js/request";

//生成uniqueId
export function getUniqueId(params) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/usExpert/getUniqueId`,
    contentType: "application/json; charset=utf-8",
    data: params
  });
}

// 获取专家信息列表
export function getzjAll(params) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/usExpert/findAllInfoByPage`,
    contentType: "application/json; charset=utf-8",
    data: params
  });
}

    // 获取专家项目经历列表
export function getzjUploadAll(params) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/usExpertItem/findAllInfo`,
    contentType: "application/json; charset=utf-8",
    data: params,
  });
}

//根据用户名查询专家信息
export function searchExpertInfo(params) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/usExpert/findByUsername?username=${params}`,
    contentType: "application/json; charset=utf-8",
  });
}
//专家信息上报保存
export function saveValue(params) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/usExpert/create`,
    contentType: "application/json; charset=utf-8",
    data: params
  });
}
//专家信息删除
export function deleteExpert(id) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/usExpert/deleteById?id=${id}`,
    contentType: "application/json; charset=utf-8",

  });
}


//专家信息编辑回显
export function findExpertById(id) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/usExpert/findById?id=${id}`,
    contentType: "application/json; charset=utf-8",
  });
}
//专家信息上报编辑
export function editValue(params) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/usExpert/update`,
    contentType: "application/json; charset=utf-8",
    data: params
  });
}

//查询项目经历
export function findProjectById(id) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/usExpertItem/findById?id=${id}`,
    contentType: "application/json; charset=utf-8",
    // data: params
  });
}

//编辑专家项目经历
export function editzjProject(params) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/usExpertItem/update`,
    contentType: "application/json; charset=utf-8",
    data: params
  });
}
//新增专家项目经历
export function addzjProject(params) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/usExpertItem/create`,
    contentType: "application/json; charset=utf-8",
    data: params
  });
}
//删除项目经历
export function deleteProjectById(id) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/usExpertItem/deleteById?id=${id}`,
    contentType: "application/json; charset=utf-8",
    // data: params
  });
}
//新增用户
export function addUser(params) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/role/saveInfo`,
    contentType: "application/json; charset=utf-8",
    data: params
  });
}
//删除用户
export function deleteUserById(id) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/role/deleteInfo?id=${id}`,
    contentType: "application/json; charset=utf-8",
    // data: params
  });
}
//查询公司列表
export function findPOrgAndCityOrg() {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/uums/sys/org/findPOrgAndCityOrg?appcode=${process.env.VUE_APP_APPCODE}`
  });
}
export function findOrgByUserMap() {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/uums/sys/org/findOrgByUserMap?appcode=${process.env.VUE_APP_APPCODE}`
  });
}

// 导出
export function exportParameter(params) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/common/exportMCExcel`,
    contentType: "application/json; charset=utf-8",
    data: params,
    responseType: "blob"
  });
}
// 时长导出
export function exportTime(params) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/applicationForm/exportCallExpertDuration`,
    contentType: "application/json; charset=utf-8",
    data: params,
    responseType: "blob"
  });
}
// 专家调用导出
export function exportHandleExpert(params) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/applicationForm/exportCallExpert`,
    contentType: "application/json; charset=utf-8",
    data: params,
    responseType: "blob"
  });
}

// 导入
export function importdata(params) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/usExpert/importInfo`,
    contentType: "application/json; charset=utf-8",
    data: params
  });
}







