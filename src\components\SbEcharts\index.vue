<template>
    <div
        id="myEchart"
        :style="`padding: 20px;width:${
            this.control.styleWidth ? this.control.styleWidth : '100%'
        };height:${
            this.control.styleHeight ? this.control.styleHeight : '400px'
        };background-color: 'red'`"
    />
</template>

<script>
import * as echarts from "echarts";
import util from "@/assets/js/public";

export default {
    name: "sb-echarts",
    props: {
        item: {
            type: Object,
            required: true,
        },
        control: {
            type: Object,
            required: true,
        },
    },
    mounted() {
        // 如果有有接口并且调通就接口，没借口就自动默认用静态数据
        this.handleEcharts(this.control.data);
    },

    methods: {
        // 后续有接口了使用init
        init() {
            let echartsList = []
            if (Object.keys(this.control.api).length > 0) {
                getEchartsInfo().then((res)=>{
                  if(res.status === 200) {
                    echartsList = res.data;   // 把请求数据赋给echarts处理的数据，后续根据接口书写处理方法
                  }
                })
            } else {
                echartsList = this.control.data
            }
            this.handleEcharts(echartsList);
        },
        handelAPI() {
            const { type, reqUrl } = this.control.api;
            let params = {};
            this.control.api.params.forEach((item) => {
                params[item.key] = item.value;
            });
            let data = {};
            if (this.control.api.radio === "form-data") {
                this.control.api.body.listValue.forEach((item) => {
                    data[item.key] = item.value;
                });
            } else {
                const list = JSON.parse(
                    JSON.stringify(this.control.api.body.jsonValue)
                );
                list.forEach((item) => {
                    data[item.key] = item.value;
                });
            }
            return {
                type,
                reqUrl,
                params,
                data
            }
        },
        getEchartsInfo() {
            const {type, reqUrl, params, data} = handelAPI()
            return request({
                method: type,
                url: util.toUrl(`/${process.env.VUE_APP_APPCODE}${reqUrl}`),
                params: params,
                data: data,
                contentType: "application/json;charset=UTF-8",
            });
        },
        handleEcharts(echartsList) {
            const echartsType = this.control.type.split("#")[0];
            // 其他类型
            const otherType = this.control.type.includes("#")
                ? this.control.type.split("#")[1]
                : null;
            if (!echartsList.length) return false;
            let legend = [];
            let xAxisData = [];
            let xAxis = {};
            let yAxis = {};
            let seriesData = [];
            let tooltip = {};
            if (["line", "bar"].includes(echartsType)) {
                echartsList.forEach((item, index) => {
                    legend.push(item.type);
                    const valueList = item.list.map((data) => {
                        return data[this.control.mapY];
                    });
                    if (index === 0) {
                        xAxisData = item.list.map((data) => {
                            return data[this.control.mapX];
                        });
                    }
                    seriesData.push({
                        name: item.type,
                        type: echartsType,
                        stack: this.control.stack ? "Total" : undefined,
                        smooth: this.control.smooth,
                        data: valueList,
                    });
                    // 如果是面积图
                    if (otherType == "areaStyle") {
                        seriesData[index].areaStyle = {};
                    }
                });
                tooltip = {
                    trigger: "axis",
                    axisPointer: {
                        type: "cross",
                        label: {
                            backgroundColor: "#6a7985",
                        },
                    },
                };
            } else if (["pie"].includes(echartsType)) {
                const valueList = echartsList[0].list.map((item) => {
                    legend.push(item[this.control.mapX]);
                    return { name: item[this.control.mapX], value: item[this.control.mapY] };
                });
                seriesData.push({
                    name: echartsList[0].type,
                    radius: otherType == "radius" ? ["40%", "70%"] : "50%",
                    type: echartsType,
                    data: valueList,
                });
            }
            // 如果是条形图
            if (otherType == "yx") {
                yAxis = { data: xAxisData };
                xAxis = { type: "value" };
            } else {
                xAxis = { data: xAxisData };
                yAxis = { type: "value" };
            }
            const myChart = echarts.init(document.getElementById("myEchart"));
            myChart.clear();
            const option = {
                title: {
                    show: this.control.showTitle,
                    text: this.control.title,
                    left: "center", // 标题位置，可以是'left', 'center', 'right'
                    textStyle: {
                        fontSize: 20, // 标题字体大小
                        fontWeight: "bold", // 标题字体粗细
                        color: "#333", // 标题字体颜色
                    },
                },
                tooltip: tooltip,
                legend: {
                    show: this.control.showLegend,
                    top: "bottom",
                    data: this.control.legend ? legend : [],
                },
                grid: {
                    left: "3%",
                    right: "3%",
                    bottom: "10%",
                    top: "80px",
                    containLabel: true,
                },
                xAxis: { ...xAxis, show: ["line", "bar"].includes(echartsType)?this.control.showX:false },
                yAxis: { ...yAxis, show: ["line", "bar"].includes(echartsType)?this.control.showY:false },
                series: seriesData,
            };
            myChart.setOption(option);
            myChart.resize();
        },
    },
};
</script>
