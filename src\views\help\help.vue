<template>
  <div class="warp">
    <center>
      <font size="5"><b>“豫起奋发 担当作为”操作手册</b></font>
    </center>
    <div class="clsCaption">
      <font color="blue">一、办理路径</font>
    </div>
    <p class="p-title">（一）门户-待办事项-审批类</p>
    <p class="p-text">从门户审批类待办找到工单即可办理。</p>
    <img src="./images/1.png" border="0" class="p-img"/>
    <p class="p-title">（二）门户应用导航-党建纪检审计-豫起奋发 担当作为</p>
    <p class="p-text">从应用导航“党建纪检审计”栏目，找到“豫起奋发 担当作为”即可进入系统。</p>
    <img src="./images/2.png" border="0" class="p-img"/>
    <p class="p-title">（三）门户应用导航-搜索</p>
    <p class="p-text">从应用导航搜索“豫起奋发”可进入系统办理工单。</p>
    <img src="./images/3.png" border="0" class="p-img"/>
    <img src="./images/4.png" border="0" class="p-img"/>
    <div class="clsCaption">
      <font color="blue">二、各角色办理说明</font>
    </div>
    <p class="p-title">（一）牵头部门正职领导派发</p>
    <p class="p-text">牵头部门正职领导需确认“部门接口人”并指定本部门任务的“办理人”。</p>
    <p class="p-text">部门接口人负责整个部门任务落实情况的统筹工作，后续需收集任务进度并填写通报。（牵头部门接口人负责整个条线，因前期已收到数据，牵头部门接口人系统默认带出）</p>
    <p class="p-text">办理人可选择部门分管领导或主管，由部门分管领导或主管继续派发任务。</p>
    <p class="p-text">若任务有变更，正职领导可以新增或编辑任务。</p>
    <img src="./images/5.png" border="0" class="p-img"/>
    <p class="p-title">（二）协办部门正职领导派发</p>
    <p class="p-text">协办部门正职领导需选择部门接口人、责任人。</p>
    <p class="p-text">部门接口人负责整个部门任务落实情况的统筹工作，后续需收集任务进度并填写通报。</p>
    <p class="p-text">责任人可选择部门分管领导或主管，由部门分管领导或主管继续派发任务。</p>
    <img src="./images/6.png" border="0" class="p-img"/>
    <p class="p-title">（三）责任人派发</p>
    <p class="p-text">责任人一般为部门分管领导或主管，责任人可以继续选择任务的责任主管，由责任主管继续派发或落实执行。</p>
    <img src="./images/7.png" border="0" class="p-img"/>
    <p class="p-title">（四）责任主管派发</p>
    <p class="p-text">责任主管可以继续选择任务负责人。任务负责人对任务的落实执行负责，后续需反馈任务进展情况。</p>
    <img src="./images/8.png" border="0" class="p-img"/>
    <p class="p-title">（五）任务负责人确认</p>
    <p class="p-text">任务负责人对责任主管派发的内容进行确认，确认后任务派发流程即归档，后续需按期反馈任务进展情况。</p>
    <img src="./images/9.png" border="0" class="p-img"/>
  </div>
</template>
<script>
import store from "@/store";
import util from "@/assets/js/public";

export default {
  name: "help",
  data() {
    return {}
  },
  created() {
  }
};
</script>
<style scoped>
.warp {
  padding: 10px;
  font: 16px "宋体";
  line-height: 200%;
}

.clsCaption {
  font: bold 20px "宋体";
  line-height: 200%;
}

.p-title {
  font: bold 18px "宋体";
  line-height: 30px;
  margin: 0;
  padding-left: 20px;
}

.p-text {
  font: normal 16px "宋体";
  line-height: 30px;
  margin: 0;
  padding-left: 30px;
}

.p-img {
  width: 80%;
  margin-left: 20px;
  margin-bottom: 20px;
}
</style>