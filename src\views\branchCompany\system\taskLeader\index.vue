<template>
  <div :class="'p10'">
    <div class="flex" style="margin-top: 8px;align-items: flex-end">
      <sb-el-form
          class="flex1"
          style="margin-right: 40px"
          :form="queryForm"
          v-model="listQuery"
          :from="true"
      ></sb-el-form>
      <div style="width: 300px;
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-end;margin-bottom:18px;margin-right:20px;">
        <el-button type="primary" size="small" @click="getList()">查询</el-button>
        <el-button size="small" @click="resetHandle()">重置</el-button>
        <el-button type="primary" size="small" @click="handleDaochu()">导出</el-button>
      </div>
     
    </div>
    <el-table
        v-loading.fullscreen.lock="tableLoading"
        element-loading-text="请稍后，正在查询..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255, 255, 255, 0.5)" class="tableCustom" :class="{'nodata': !dataList.length}"
        :data="dataList"
        style="width: 100%;" border :span-method="arraySpanMethod" :cell-style="{background: '#ffffff'}">
      <el-table-column prop="lineType" label="专项组" width="100" align="center">
      </el-table-column>
      <el-table-column prop="targetIntorAndInfo" label="项目名称" width="120">
        <template v-slot:default="scope">
          <!-- <span style="font-weight: bold" v-html="scope.row.targetIntor"></span>
          <br/> -->
          <span v-html="scope.row.targetInfo"></span>
        </template>
      </el-table-column>
      <el-table-column label="时间要求" width="100">
        <template v-slot:default="scope">
          <span v-html="scope.row.finishDataStr"></span>
        </template>
      </el-table-column>
      <el-table-column prop="actionInfo" label="目标" width="200">
        <template v-slot:default="scope">
          <span v-html="scope.row.actionInfo"></span>
        </template>
      </el-table-column>
      <el-table-column prop="taskInfo" label="任务" min-width="300">
        <template v-slot:default="scope">
          <span v-html="scope.row.taskInfo"></span>
        </template>
      </el-table-column>
       <el-table-column prop="taskJcInfo" label="举措" min-width="300">
        <template v-slot:default="scope">
          <span v-html="scope.row.taskJcInfo"></span>
        </template>
      </el-table-column>
      <el-table-column prop="companyName" label="责任部门" width="120" align="center">
      </el-table-column>
      <el-table-column prop="taskInfoTrueName" label="任务负责人" width="90" align="center">
      </el-table-column>
      <el-table-column prop="modifiedTime" label="最后修改时间" width="160" align="center">
      </el-table-column>
      <el-table-column label="操作" width="220">
        <template v-slot="scope">
          <div class="inlineC">
            <el-button size="mini" type="primary" @click="changeHandle(scope.row)">【变更】</el-button>
            <el-button size="mini" type="primary" @click="lookHandle(scope.row)">【查看变更记录】</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 变更记录 -->
    <el-dialog title="查看变更记录" :visible.sync="lookD" v-dialogDrag :close-on-click-modal="false" append-to-body
               width="1200px">
      <el-table :data="lookList" style="width: 100%;" border :cell-style="{background: '#ffffff'}">
        <el-table-column label="序号" type="index" align="center">
        </el-table-column>
        <el-table-column prop="beforeTrueName" label="变更前姓名">
        </el-table-column>
        <el-table-column prop="beforeUserName" label="变更前OA账号">
        </el-table-column>
        <el-table-column prop="afterTrueName" label="变更后姓名">
        </el-table-column>
        <el-table-column prop="afterUserName" label="变更后OA账号">
        </el-table-column>
      </el-table>
      <div style="height: 20px;"></div>
    </el-dialog>
    <!-- 变更 -->
    <el-dialog title="变更" :visible.sync="changeD" v-dialogDrag :close-on-click-modal="false" append-to-body
               width="700px">
      <div class="tableForm">
        <sb-el-form v-if="changeD" ref="appForm" :form="appForm" v-model="appFormValue" :disabled="appForm.formDisabled"
                    :on-ok="handleDoFun">
        </sb-el-form>
      </div>
      <span slot="footer" class="dialog-footer">
				<el-button @click="changeD = false" size="small">关闭</el-button>
				<el-button type="primary" @click="handleConfirm()" size="small">确认</el-button>
			</span>
    </el-dialog>
  </div>
</template>
<script>
import {
 taskUserexport,queryTaskUser,updateTaskUser,changelist
} from "@/api/branchCompany";

let optionsList = [{name: '经营条线', value: '经营条线'}, {name: 'IT条线', value: 'IT条线'}, {
  name: '综合条线',
  value: '综合条线'
}, {name: '网络条线', value: '网络条线'}, {name: '监督条线', value: '监督条线'}]
export default {
  name: "application",
  data() {
    return {
      nowTime: this.util.getNow("yyyy-MM-dd hh:mm:ss"),

      dataList: [],
      spanArr: [],//二维数组，用于存放单元格合并规则
      position: 0,//用于存储相同项的开始index

      queryForm: {
        inline: true,
        labelPosition: 'right',
        labelWidth: "120px",
        formItemList: [
          {label: "专项组", key: "lineType", type: "input", class: 'c4', options: optionsList},
          {label: "项目名称", key: "targetInfo", type: "input", class: 'c4'},
          {label: "任务负责人姓名", key: "taskInfoTrueName", type: "input", class: 'c4'},
          {label: "目标", key: "actionInfo", type: "input", class: 'c4'},
          {label: "任务", key: "taskInfo", type: "input", class: 'c4'},
          {label: "举措", key: "taskJcInfo", type: "input", class: 'c4'},
          {label: "责任部门", key: "companyName", type: "input", class: 'c4'},
          {label: "部门接口人", key: "companyTrueName", type: "input", class: 'c4'},

        ],
      },
      listQuery: {
        lineType: '',
        taskInfo: '',
        currentTrueName: '',
        targetIntor: '',
        actionInfo: '',
        taskHostUnitName: ''
      },
      exportId: '',
      tableLoading: false,
      lookD: false,
      lookList: [],
      changeD: false,
      appFormValue: Object.assign({}),
      appForm: {
        formDisabled: false,
        labelWidth: "160px",
        inline: true,
        formItemList: [
          {
            class: 'c12', label: '任务负责人姓名', key: 'currentTrueName', type: 'user',
            readonly: true, mulitple: false, stepLoad: true, appendShow: true, rule: {required: true},
            relevancy: "currentTrueName-name,currentUsername-id", defaultProps: {
              children: "children",
              label: "name",
              isLeaf: 'leaf',
            }
          },
          {class: "c12", label: "任务负责人OA账号", key: "currentUsername", type: "input", disabled: true}
        ],
      },
    }
  },
  created() {
    setTimeout(() => {
      let height = window.innerHeight - 262
      let dom = document.querySelector('.tableCustom .el-table__body-wrapper')
      dom.setAttribute('style', `max-height: ${height}px;overflow: auto;`)
    }, 0)
    this.getList()
  },
  methods: {
    resetHandle() {
      Object.assign(this.listQuery, {
        lineType: '',
        taskInfo: '',
        currentTrueName: '',
        targetIntor: '',
        actionInfo: '',
        taskHostUnitName: ''
      })
      this.getList()
    },
    async handleConfirm() {
      let res = await updateTaskUser(this.appFormValue,'0')
      this.changeD = false
      this.getList()
    },
    changeHandle(data) {
      Object.assign(this.appFormValue, {
        id: data.id,
        currentUsernameOld: data.currentUsername,
        currentTrueNameOld: data.currentTrueName,
        currentTrueName: data.currentTrueName,
        currentUsername: data.currentUsername
      })
      this.changeD = true
    },
    async lookHandle(data) {
      let res = await changelist(data.id,'0')
      this.lookList = res.data
      this.lookD = true
    },
    async handleDaochu() {
      let res = await taskUserexport(this.listQuery,'0')
      if (res.data) {
        this.util.blobDownload(res.data, res.filename);
      } else {
        this.$message({
          message: '导出失败',
          type: 'warning',
          duration: 1500
        });
      }
    },
    rowspan(idx, prop) {
      this.spanArr[idx] = [];
      this.position = 0;
      this.dataList.forEach((item, index) => {
        if (index === 0) {
          this.spanArr[idx].push(1);
          this.position = 0;
        } else {
          if (this.dataList[index][prop] === this.dataList[index - 1][prop]) {
            this.spanArr[idx][this.position] += 1;//有相同项
            this.spanArr[idx].push(0); // 名称相同后往数组里面加一项0
          } else {
            this.spanArr[idx].push(1);//同列的前后两行单元格不相同
            this.position = index;
          }
        }
      })
    },
    // 合并行
    arraySpanMethod({row, column, rowIndex, columnIndex}) {
      for (let i = 0; i < 6; i++) {
        if (columnIndex === i) {
          const _row = this.spanArr[i][rowIndex];
          const _col = _row > 0 ? 1 : 0;
          // console.log('第'+rowIndex+'行','第'+i+'列','rowspan:'+_row,'colspan:'+_col)
          return {
            rowspan: _row,
            colspan: _col
          }
        }
      }
    },
    async getList() {
      this.tableLoading = true
      let list = await queryTaskUser(this.listQuery,'0')

      this.dataList = list.data.map(item => {
        item.targetIntor = this.util.htmlDecode(item.targetIntor)
        item.targetInfo = this.util.htmlDecode(item.targetInfo)
        item.actionInfo = this.util.htmlDecode(item.actionInfo)
        item.taskInfo = this.util.htmlDecode(item.taskInfo)
        item.taskJcInfo = this.util.htmlDecode(item.taskJcInfo)
        item.modifiedTime = this.util.getNow('yyyy-MM-dd hh:mm:ss', item.modifiedTime)
        return {
          intorAndInfoAndDate: item.targetIntor + item.targetInfo + item.finishDataStr,
          targetIntorAndInfo: item.targetIntor + item.targetInfo,
          ...item
        }
      });

      this.rowspan(0, 'lineType');
      this.rowspan(1, 'targetIntorAndInfo');
      this.rowspan(2, 'intorAndInfoAndDate');
      this.rowspan(3, 'actionInfo');
      this.rowspan(4, 'taskInfo')
      this.rowspan(5, 'taskJcInfo')

      this.tableLoading = false
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }

};
</script>
<style scoped>
::v-deep .tableCustom:before {
  height: 0;
}

::v-deep .nodata:before {
  height: 1px;
}

::v-deep .el-table__header-wrapper .el-table__cell.gutter {
  width: 20px !important;
  display: block !important;
}

::v-deep .el-input.is-disabled .el-input__inner {
  background-color: #FFFFFF;
  color: #000;
}

::v-deep .el-input .el-input__suffix {
  line-height: 28px;
}

::v-deep .el-form-item__content {
  flex: 1;
}

.f-1 {
  position: relative;
}

.f-2 {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 99;
  font-size: 12px;
  line-height: 26px;
  background: #fff;
  width: 100%;
  text-align: right;
}

.f-3 {
  text-decoration: underline;
  cursor: pointer;
}

::v-deep .tableCustom .f-4 span {
  white-space: normal;
  overflow: visible;
  text-overflow: inherit;
  width: 100%;
}

::v-deep .el-input__count {
  background: rgba(247, 234, 233, 0);
}

.red {
  color: rgba(192, 0, 0, 1);
  cursor: pointer;
}

::v-deep .tableCustom .el-textarea__inner {
  padding: 0;
  border: none;
  min-height: 80px !important;
  padding-bottom: 26px;
}

::v-deep .tableCustom .cell {
  white-space: normal;
  overflow: visible;
  text-overflow: inherit;
}

::v-deep .el-table .cell > .inlineC > .el-button {
  background: transparent;
  border: none;
  margin: 0;
  font-size: 14px;
  color: rgba(192, 0, 0, 1);
  padding: 7px 0px;
  white-space: normal;
}

::v-deep .el-input-group__append {
  background-color: #fff3f3;
  border-color: #fff3f3;
}

::v-deep .upload_D {
  min-width: 100%;
}

::v-deep .upload_Btn .uploadB {
  right: 0;
  left: auto;
}

.m-title {
  width: 100%;
  padding-left: 10px;
  border-left: 4px solid rgba(192, 0, 0, 1);
  padding: 10px;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  border-bottom: 1px solid #ebebeb;
  border-right: 1px solid #ebebeb;
}

.m-4 {
  height: 32px;
  display: flex;
  align-items: center;
  padding: 0;
  border-right: 1px solid #ebebeb;
  border-bottom: 1px solid #ebebeb;
}

.m-4:first-child {
  border-top: 1px solid #ebebeb;
}

::v-deep .m-4 .el-input__inner {
  height: 30px;
  line-height: 30px;
  border: none;
}

::v-deep .m-4 .el-input__icon {
  line-height: 30px;
}

.m-2 {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100px;
  height: 100%;
  background: #DDF1FE;
  padding: 0 10px;
  font-size: 14px;
  color: #606266;
  font-weight: bold;
}

.m-5 {
  flex: 1;
}
</style>