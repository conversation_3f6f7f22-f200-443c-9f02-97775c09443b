<template>
	<div class="app-container" style="display:flex;justify-content: space-between;">
		<div class="container-right">
			<sb-el-table :table="table" @getList="getList" @handleOrg="handleOrg" @handlePerson="handlePerson" @handleRole="handleRole" @handleUpDataGetRow="handleUpDataGetRow" 
					@handleAddData="handleAddData" @handleUpData="handleUpData" @handleDelete="handleDelete" @updateTableData="updateTableData" @handleAddBef="handleAddBef" :on-ok="handleDoFun">
				<template v-slot:menuLevel="{ obj }">
					<div>{{ obj.row.menuLevel=='1'?'一级': 
						obj.row.menuLevel=='2'?'二级': 
						obj.row.menuLevel=='3'?'三级':
						obj.row.menuLevel=='4'?'四级': '根级'}}</div>
				</template>
				<template v-slot:parentId="{ obj }">
					<div>{{ (obj.row.parentId).split() }}</div>
				</template>
			</sb-el-table>
		</div>
		<ConfigDialog ref="orgDialog" :item="orgAllocatData" @chooseData="chooseData" @getYiYouList="queryOrgByPermissionId"/>
		<ConfigDialog ref="userDialog" :item="userAllocatData" @chooseData="chooseData" @getYiYouList="queryUserByPermissionId"/>
		<el-dialog title="角色权限配置" :visible.sync="roleAllocationDialog" width="900px" center :destroy-on-close="true">
			<div style="display:flex;flex-direction: column;min-height:630px">
				<!-- <sb-el-table :table="roleAllocatTable" @getList="getRoleList">
					<template v-slot:isApplicationRole="{ obj }">
						<div>{{ obj.row.isApplicationRole==true?'是':'否' }}</div>
					</template>
				</sb-el-table> -->
				<el-form :inline="true" :model="roleAllocatTable.listQuery" class="demo-form-inline" style="display:flex">
					<el-form-item label="角色编码">
						<el-input v-model="roleAllocatTable.listQuery.roleCode" placeholder="请输入角色编码" size="small" clearable></el-input>
					</el-form-item>
					<el-form-item label="角色名称">
						<el-input v-model="roleAllocatTable.listQuery.roleName" placeholder="请输入角色名称" size="small" clearable></el-input>
					</el-form-item>
					<el-form-item>
						<el-button type="primary" @click="getRoleList" size="small" style="margin-top:5px">查询</el-button>
					</el-form-item>
				</el-form>
				 <el-table
					ref="multipleTable"
					v-loading="roleAllocatTable.loading"
					:data="roleAllocatTable.data"
					tooltip-effect="dark"
					style="width: 100%"
					border
					height="600"
					@selection-change="handleSelectionChange">
					<el-table-column
						type="selection"
						width="55">
					</el-table-column>
					<el-table-column
						prop="roleCode"
						label="角色编码">
					</el-table-column>
					<el-table-column
						prop="roleName"
						label="角色名称">
					</el-table-column>
					<el-table-column
						prop="isApplicationRole"
						label="是否是业务角色">
						<template slot-scope="scope">{{ scope.row.isApplicationRole==true?'是':'否' }}</template>
					</el-table-column>
				</el-table>
				<el-pagination
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
					:current-page="roleAllocatTable.page"
					:page-sizes="[10, 20, 30, 40]"
					:page-size="roleAllocatTable.size"
					layout="total, sizes, prev, pager, next, jumper"
					:total="roleAllocatTable.total"
					background
					style="text-align: right;">
				</el-pagination>
				<el-form label-position="right" label-width="80px" :model="formLabelAlign" class="menuRolepz">
					<el-form-item label="角色名称">
						<el-input v-model="formLabelAlign.roleName" size="small"></el-input>
					</el-form-item>
					<el-form-item label="角色编码">
						<el-input v-model="formLabelAlign.roleCode" size="small"></el-input>
					</el-form-item>
					<el-form-item label="指定角色">
						<el-input v-model="formLabelAlign.id" size="small"></el-input>
					</el-form-item>
				</el-form>
			</div>
			<span slot="footer" class="dialog-footer">
				<el-button @click="roleAllocationDialog = false">取 消</el-button>
				<el-button type="primary" @click="creatMenuRolePz">确 定</el-button>
			</span>
		</el-dialog>
		<el-dialog title="新增菜单" :visible.sync="menuDialog" width="900px">
			<div style="display:flex;flex-direction: column;">
				<sb-el-form
					:form="menuForm"
					v-model="menuFormModul"
					:from="true"
					>
					<template
						v-for="item in menuForm.formItemList"
						v-slot:[item.template]="obj"
					>
						<slot
							v-if="item.template"
							:name="item.template"
							:obj="obj.obj"
						></slot>
					</template>
					<template v-slot:parentId="{ obj }">
						<div>{{ (obj.row.parentId).split() }}</div>
					</template>
				</sb-el-form>
			</div>
			<span slot="footer" class="dialog-footer">
				<el-button @click="menuDialog = false">取 消</el-button>
				<el-button type="primary" @click="handlemenuForm">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>
<script>
// import { findAll, deleteById, changeStatus, getTableField, getSyncField, batchUpdateField, create, update, findAllDb, saveGroupDataAdd, saveGroupDataUpdate, saveGroupData, delectGroupData, queryGroupList, addGroupJY, deleteGroupById } from '@/api/dataCollect/dataSet';
// import { ListByDataSetId } from '@/api/dataCollect/datasetForm';
import { getMenuList,updateOrgByPermissionId,updateUserByPermissionId,addMenu,findRoleNameIsARoleDim,updateSysPermission,
updateMenuCustom,deleteMenuCustom,updateRoleByPermissionId ,queryOrgByPermissionId,queryUserByPermissionId,queryRoleByPermissionId} from '@/api/system/cdgl.js';
import ConfigDialog from "../component/configDialog";

export default {
	name: 'dataSet',
	props: ["tableType", "tableTitle"],
	components: {
		ConfigDialog
	},
	data() {
		return {
			table: {
				border: true, // 是否带纵向边框
				loading: false, // 加载中动画
				modulName: 'cdgl-菜单信息', // 列表中文名称
				stripe: true, // 是否为斑马条样式
				hasSelect: true, // 是否有复选框
				showIndex: false, // 序号
				data: [], // 数据
				addAndUpdateType: 'dialog',
				total: null,
				fixed: false,
				hasQueryForm: false, // 是否有查询条件
				queryForm: {
					inline: true,
					labelWidth: '110px',
					labelPosition: 'right',
					formItemList: [
						// { label: '应用编码', key: 'name', type: 'input' },
						// { label: '应用名称', key: 'name', type: 'input' },
					],
				},
				tr: [
					{ id: 'description', label: '菜单名称', prop: 'description',  },
					{ id: 'id', label: '菜单ID', prop: 'id' },
					{ id: 'menuLevel', label: '菜单层级', prop: 'menuLevel', show: 'template',template:'menuLevel' },
					{ id: 'url', label: '资源路径', prop: 'url',  },
					{ id: 'permissionType', label: '菜单类型', prop: 'permissionType',  },
					{ id: 'icon', label: '资源图标', prop: 'icon',  },
					{ id: 'displayOrder', label: '排序', prop: 'displayOrder',  },
				],
				multipleSelection: [], //多选选中数据存放变量
				dialogVisible: false, //默认对话框关闭
				form: {
					width: '800px',
					labelWidth: '100px',
					inline: true,
					labelPosition: 'right',
					formItemList: [
						{ class: 'c6', label: '上级菜单', key: 'parentName', type: 'treeSelect', data:[],props: {value: 'id', label: 'remark', children: 'children'}, show: 'template', rule: { required: true }, clearable: true },
						{ class: 'c6', label: '权限标识', key: 'permissionCode', type: 'input', rule: { required: true } , clearable: true},
						{ class: 'c6', label: '资源图标', key: 'icon', type: 'input', rule: { required: false }, clearable: true },
						{ class: 'c6', label: '菜单名称', key: 'description', type: 'input', rule: { required: true } , clearable: true},
						{ class: 'c6', label: '资源路径', key: 'url', type: 'input', rule: { required: true } , clearable: true},
						{ class: 'c6', label: '权限类型', key: 'permissionType', type: 'select', rule: { required: true } ,dictType: "permissionType", from: true, clearable: true},
						{ class: 'c6', label: '菜单级别', key: 'menuLevel', type: 'select', rule: { required: true } ,options:[{name:'一级',value:'1'},{name:'二级',value:'2'},{name:'三级',value:'3'},{name:'四级',value:'4'}], clearable: true},
						{ class: 'c6', label: '显示顺序', key: 'displayOrder', type: 'input', rule: { required: true }, clearable: true },
						{ class: 'c12', label: '资源描述', key: 'remark', type: 'input',inputType: 'textarea', rule: { required: true }, clearable: true },
					],
				},
				listFormModul: {},
				hasOperation: true, //是否有操作列表
				operation: {
					width: '300',
					data: [
						{ id: 'addmenu', name: '新增',beforeFun: "handleAddBef", fun: 'handleAddData' },
                        { id: 'read', name: '查看', fun: 'handleUpDataGetRow' },
						{ id: 'update', name: '编辑', fun: 'handleUpData', beforeFun: "handleUpDataGetRow" },
						{ id: 'deleteY', name: '启用', fun: 'handleDelete',show: "menuLevel|1|2|3|4|5,enabled|0"},
						{ id: 'deleteN', name: '弃用', fun: 'handleDelete',show: "menuLevel|1|2|3|4|5,enabled|1"},
						{ id: 'org', name: '组织', fun: 'handleOrg', hide: "menuLevel|0"},
						{ id: 'person', name: '人员', fun: 'handlePerson',hide: "menuLevel|0"},
						{ id: 'role', name: '角色', fun: 'handleRole',hide: "menuLevel|0"},
					],
				},
				hasPagination: false,
				listQuery: {  },
				hasBatchOperate: false, //有无批量操作
				batchOperate: {},
				hasOtherQueryBtn: true,
				otherQueryBtn: {
					data: [
                        {
                            id: 'export',
                            name: '导出 ',
                            fun: 'exportSelect',
                        },
                    ],
				},
				hasGroupTabs: true,
				tabsList: [],
				tabsPosition: '全部',
				defaultExpand: false,
				treeProps: {children: 'children', hasChildren: 'hasChildren'}
			},
			menuDialog: false,
			menuFormModul:{},
			menuForm: {
				width: '800px',
				labelWidth: '100px',
				inline: true,
				labelPosition: 'right',
				formItemList: [
					{ class: 'c6', label: '上级菜单', key: 'parentName', type: 'treeSelect', data:[],props: {value: 'id', label: 'description', children: 'children'}, show: 'template', rule: { required: true } , clearable: true},
					{ class: 'c6', label: '权限标识', key: 'permissionCode', type: 'input',placeholder: 'system:cdgl', rule: { required: true } , clearable: true},
					{ class: 'c6', label: '资源图标', key: 'icon', type: 'input',placeholder: '', rule: { required: false } , clearable: true},
					{ class: 'c6', label: '菜单名称', key: 'description', type: 'input',placeholder: '工单查询', rule: { required: true } , clearable: true},
					{ class: 'c6', label: '资源路径', key: 'url', type: 'input',placeholder: '【权限类型】是【访问路径】时要添加此项', rule: { required: true } , clearable: true},
					{ class: 'c6', label: '权限类型', key: 'permissionType', type: 'select', rule: { required: true } ,dictType: "permissionType", from: true, clearable: true},
					{ class: 'c6', label: '菜单级别', key: 'menuLevel', type: 'select', rule: { required: true } ,options:[{name:'一级',value:'1'},{name:'二级',value:'2'},{name:'三级',value:'3'},{name:'四级',value:'4'}], clearable: true},
					{ class: 'c6', label: '显示顺序', key: 'displayOrder', type: 'input',placeholder: '请填写排序值', rule: { required: true } , clearable: true},
					{ class: 'c12', label: '资源描述', key: 'remark', type: 'input',inputType: 'textarea',placeholder: '最多输入200字', rule: { required: true }, clearable: true },
				],
			},
            defaultProps: {
                children: 'children',
                label: 'label'
            },
			orgAllocatData: {
				inputType: 'text',
				title: '组织权限设置',
				appendShow: true,
				rows: 12,
				type: 'ZZ',
				btnText: '搜索',
				mulitple: true,
				dialogVisible: false,
				defaultProps: {
					children: "children",
					label: "displayName",
					isLeaf: 'leaf',
				},
			},
			userAllocatData: {
				inputType: 'text',
				title: '用户权限设置',
				appendShow: true,
				rows: 12,
				type: 'RY',
				btnText: '搜索',
				mulitple: true,
				dialogVisible: false,
				defaultProps: {
					children: "children",
					label: "name",
					isLeaf: 'leaf',
				},
			},
			roleAllocationDialog: false,
			roleAllocatTable: {
				border: true, // 是否带纵向边框
				loading: false, // 加载中动画
				modulName: 'role-角色配置', // 列表中文名称
				stripe: true, // 是否为斑马条样式
				hasSelect: true, // 是否有复选框
				showIndex: true, // 序号
				data: [], // 数据
				addAndUpdateType: 'dialog',
				total: null,
				hasQueryForm: true, // 是否有查询条件
				queryForm: {
					roleCode: '',
					roleName: ''
					// inline: true,
					// labelWidth: '80px',
					// labelPosition: 'right',
					// formItemList: [
					// 	{ label: '角色编码', key: 'roleCode', type: 'input' },
					// 	{ label: '角色姓名', key: 'roleName', type: 'input' },
					// ],
				},
				// tr: [
				// 	{ id: 'roleCode', label: '角色编码', prop: 'roleCode', },
				// 	{ id: 'roleName', label: '角色姓名', prop: 'roleName' },
				// 	{ id: 'isApplicationRole', label: '是否是业务角色', prop: 'isApplicationRole', show: 'template',template:'isApplicationRole' },
				// ],
				initSelection: [],
				multipleSelection: [], //多选选中数据存放变量
				dialogVisible: false, //默认对话框关闭
				form: {
					width: '800px',
					labelWidth: '100px',
					inline: true,
					labelPosition: 'right',
					formItemList: [
						
					],
				},
				listFormModul: {},
				hasOperation: false, //是否有操作列表
				operation: {
					width: '250',
					data: [],
				},
				hasPagination: true,
				listQuery: { size: 10, page: 1 , roleCode: '', roleName: ''},
				hasBatchOperate: false, //有无批量操作
				batchOperate: {},
				hasOtherQueryBtn: false,
				otherQueryBtn: {
					data: [],
				},
				total: 0
			},
			formLabelAlign: {
				roleName: '',
				roleCode: '',
				id: ''
			},
			currentMenuRows: {},
			menuRoleFlag: true,
			menuFormData: []
		};
	},
	watch: {
		
	},
	activated() {

	},
	mounted(){
		this.getList()
	},
	methods: {
		//更新菜单角色
		creatMenuRolePz() {
			var roles = [];
			this.roleAllocatTable.multipleSelection.forEach(item=>{
				roles.push(item.id)
			})
			var params = {
				appId: this.currentMenuRows.appId,
				permissionId: this.currentMenuRows.id,
				updlist: roles
			}
			updateRoleByPermissionId(params).then((res) => {
				if(res.status == 200)  {
					this.roleAllocationDialog = false;
					this.getList()
				}
			}).catch((err) => {
				
			});
		},
		//弹窗内部搜索OA账号
		getRoleList() {
			this.roleAllocatTable.loading = true;
			findRoleNameIsARoleDim(this.roleAllocatTable.listQuery).then((res) => {
				if(res.status == 200)  {
					this.roleAllocatTable.data = res.data&&res.data.content? res.data.content:[]
					this.roleAllocatTable.loading = false;
					this.roleAllocatTable.total = res.data.totalElements;
				}
			}).catch((err) => {
				this.roleAllocatTable.loading = false;
			});
		},
		handleSizeChange(val) {
			this.roleAllocatTable.listQuery.size = val
			this.getRoleList()
		},
		handleCurrentChange(val) {
			this.roleAllocatTable.listQuery.page = val
			this.getRoleList()
		},
		handleSelectionChange(val) {
			val.forEach((item,index) => {
				if(!item) {
					val.splice(index,1);
					this.menuRoleFlag = false;
				}
			})
			this.roleAllocatTable.multipleSelection = this.menuRoleFlag? val : [...val,...this.roleAllocatTable.initSelection];
			let roleName = []
			let roleCode=[]
			let ids = []
			this.roleAllocatTable.multipleSelection.forEach(item=>{
				roleName.push(item.roleName)
				roleCode.push(item.roleCode)
				ids.push(item.id)
			})
			this.formLabelAlign.roleName = roleName.join(',')
			this.formLabelAlign.roleCode = roleCode.join(',')
			this.formLabelAlign.id = ids.join(',')
			// if (this.roleAllocatTable.multipleSelection) {
			// 	this.roleAllocatTable.multipleSelection.forEach((row) => {
			// 		// 不能自己自定义对象来设置选中（原因如下分析），那我可以从列表中找到需要选中的那个对象，然后把它拿过来作为选中的项就可以了
			// 		this.$refs.multipleTable.toggleRowSelection(
			// 			this.roleAllocatTable.data.find((item) => {
			// 				return row.id == item.id; // 注意这里寻找的字段要唯一，示例仅参考
			// 			}),
			// 			true
			// 		);
			// 	});
			// }
		},
		handleOrg(obj) {
			this.currentMenuRows = obj.row;
			this.orgAllocatData.dialogVisible = true;
			this.queryOrgByPermissionId()
		},
		handlePerson(obj) {
			this.currentMenuRows = obj.row;
			this.userAllocatData.dialogVisible = true;
			this.queryUserByPermissionId()
		},
		handleRole(obj) {
			this.currentMenuRows = obj.row;
			this.roleAllocationDialog = true;
			this.queryRoleByPermissionId()
			this.getRoleList()
		},
		//查询已有组织
		queryOrgByPermissionId() {
			queryOrgByPermissionId(this.currentMenuRows.id).then((res) => {
				if(res.status == 200) this.$refs.orgDialog.multipleSelection = res.data? res.data:[]
			}).catch((err) => {
				
			});
		},
		//查询已有人员
		queryUserByPermissionId() {
			queryUserByPermissionId(this.currentMenuRows.id).then((res) => {
				if(res.status == 200) {
					if(res.data) {
						res.data.forEach((user) =>{
							user.name = user.truename
							user.id = user.username
						})
						this.$refs.userDialog.multipleSelection = res.data
					} else {
						this.$refs.userDialog.multipleSelection = []
					}
				}
			}).catch((err) => {
				
			});
		},
		//查询已有角色
		queryRoleByPermissionId() {
			queryRoleByPermissionId(this.currentMenuRows.id).then((res) => {
				if(res.status == 200) {
					this.roleAllocatTable.multipleSelection = res.data;
					this.roleAllocatTable.initSelection = res.data;
					this.$nextTick(()=>{
						if (this.roleAllocatTable.multipleSelection) {
							this.roleAllocatTable.multipleSelection.forEach((row) => {
								// 不能自己自定义对象来设置选中（原因如下分析），那我可以从列表中找到需要选中的那个对象，然后把它拿过来作为选中的项就可以了
								let that = this;
								this.$refs.multipleTable.toggleRowSelection(
									that.roleAllocatTable.data.find((item) => {
										return row.id == item.id; // 注意这里寻找的字段要唯一，示例仅参考
									}),
									true
								);
							});
						}
					})
					
				}
			}).catch((err) => {
				
			});
		},
		//更新菜单组织、人员信息
		chooseData(array,type) {
			if(type == 'ZZ') {
				var orgs = [];
				array.forEach(item=>{
					orgs.push(item.orgCode)
				})
				var params = {
					appId: this.currentMenuRows.appId,
					permissionId: this.currentMenuRows.id,
					updlist: orgs
				}
				// console.log(this.currentMenuRows);
				updateOrgByPermissionId(params).then(({data}) => {
					if(res.status == 200) this.getList()
				}).catch((err) => {
					
				});
			} else if(type == 'RY') {
				var users = [];
				array.forEach(item=>{
					users.push(item.id)
				})
				var params = {
					appId: this.currentMenuRows.appId,
					permissionId: this.currentMenuRows.id,
					updlist: users
				}
				updateUserByPermissionId(params).then(({data}) => {
					if(res.status == 200) this.getList()
				}).catch((err) => {
					
				});
			}
			
		},
		// 查询列表
		getList() {
			this.table.loading = true;
			console.log(11);
			getMenuList().then(({data}) => {
				this.table.loading = false;
				this.menuFormData = data;
				let newData = this.util.toTreeData(
					data,
					'id',
					'parentId',
					'id,appId,parentId,description,permissionCode,remark,menuLevel,url,permissionType,icon,displayOrder,enabled'
				);
				this.table.data = newData;
				this.tableData = newData;
				this.table.total = data.totalElements;
				this.table.form.formItemList[0].data = newData;//上级菜单下拉树
				this.menuForm.formItemList[0].data = newData;//上级菜单下拉树
			}).catch((err) => {
				this.table.loading = false;
			});
		},
		handleAddBef(){
			this.table.operation.nowBtn = true;
			this.table.form.formItemList.forEach((item)=>{
				item.disabled = false;
			})
			return true
		},
		// 新增
		handleAddData(obj) {
			this.menuFormModul.parentId = obj.row.parentId
			this.menuFormModul.appId = obj.row.appId
			this.menuFormModul.appCode = process.env.VUE_APP_APPCODE
			this.menuFormModul.id = ''
			this.menuFormData.forEach((item)=>{
				if(item.id === obj.row.id) {
					this.menuFormModul.parentName = item.remark
				}
			})
			this.menuDialog = true;
		},
		handlemenuForm() {
			this.menuFormData.forEach((item)=>{
				if(item.remark === this.menuFormModul.parentName) {
					this.menuFormModul.parentId = item.id
				}
			})
			this.menuFormModul.type = this.menuFormModul.permissionType
			delete this.menuFormModul.parentName
			addMenu(this.menuFormModul).then(res => {
				this.table.dialogVisible = false
				if (res.status == 200){
					this.menuDialog = false;
					this.getList();
				} 
			})
		},

		// 编辑
		handleUpData() {
			if(this.table.listFormModul.children) {
				delete this.table.listFormModul.children
			}
			this.table.listFormModul.type = this.table.listFormModul.permissionType
			updateMenuCustom(this.table.listFormModul).then(res => {
				this.table.dialogVisible = false
				this.getList();
			})
		},
		// 启用、弃用
		handleDelete(obj) {
			this.$confirm(
              "是否要" + (obj.id=="deleteY"?'启用':'弃用') + "该条数据",
              "提示",
              {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
              }
            )
			.then(() => {
				let params = {
					id: obj.row.id,
					status: obj.row.enabled==1?0:1
				}
				updateSysPermission(params).then((res) => {
					this.getList();
				});
			})
			.catch(() => {});
		},
		updateTableData(obj) {
			for (let i in obj) {
				this.$set(this.table, i, obj[i]);
			}
		},
		// 根据id查询行数据
		handleUpDataGetRow(row) {
			this.table.form.formItemList.forEach((item)=>{
				item.disabled = row.read;
			})
			this.menuFormData.forEach((item)=>{
				if(item.id === row.parentId) {
					row.parentName = item.remark
				}
			})
			this.table.listFormModul = row
		},
	
		handleDoFun(obj, fun, data) {
			let n = this[obj[fun]].call(this, obj, data);
			return n;
		},
		// 修改是否启用
		handleChangeEnable(index, row) {
			// changeStatus({ id: row.id, enableStatus: row.enableStatus }).then((res) => { }).catch((error) => {
			// 	this.table.data[index].enableStatus = this.table.data[index].enableStatus;
			// });
		},
		
		getTableField() {
			this.fieldTableData = [];
			// getTableField(this.datasetId).then((res) => {
			// 	this.fieldTableData = res.data;
			// 	this.quotaChange();
			// });
		},
		
	},
};
</script>
<style scoped>
.table-name {
    font-weight: bold;
}
.container-left {
    width: 300px;
    height: 100%;
    overflow: auto;
    background-color: var(--el-bg-color-overlay);
    border: 1px solid var(--el-border-color-light);
    border-radius: 4px;
    box-shadow: var(--el-box-shadow-light);
}
.container-right {
    width: calc(100% - 10px);
    height: 100%;
	position: relative;
}
::-webkit-scrollbar {
    display: none;
}
::v-deep .el-card__header{
	padding: 8px 15px;
}
.treeData{
	width:100%;
	display:flex;
	justify-content: space-between;
	font-size: 15px;
	height: 30px;
	line-height: 30px;
	color: #444;
	padding-left: 8px;
}
.treeData .text{
	width: 215px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	/* padding: 5px; */
	cursor: pointer;
}
.treeData .text:hover{
	color: rgba(192,0,0,1);
}
.treeData .execute{
	width: 70px;
	color: rgba(192,0,0,1);
	cursor: pointer;
	text-align: right;
}
.treeData .execute:hover{
	text-decoration: underline;
}
::v-deep .el-card__body{
	padding: 0 15px;
	padding-bottom: 15px;
}
.head-container{
	height: calc(100vh - 180px);
	overflow-y: auto;
    padding: 20px;
}
::v-deep .el-table__body-wrapper{
	max-height: calc(100vh - 320px);
	overflow-y: auto;
}
.acceptanceBox{
    position: relative;
	/* height: 250px; */
}
.footer{
    width: 100%;
    /* position: absolute; */
    left: 0;
    bottom: 0;
    text-align: center;
    margin-top: 20px;
	margin-bottom: 20px;
}
.selectedColor {
	background-color: #eeeeee;
}
.menuRolepz {
	padding: 0 20%;
	margin-top: 25px;
}
::v-deep .menuRolepz .el-form-item {
	margin-bottom: 0px;
}
</style>
