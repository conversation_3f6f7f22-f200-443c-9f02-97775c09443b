import request from "@/assets/js/request";
import store from "@/store";
import util from "@/assets/js/public";

// AI周报
export function AIdate(id) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/collection/exportPolish?id=${id}`,
      contentType: "application/json;charset=UTF-8",
      loading: true,
      responseType: "blob"
  });
}
// 反馈台账导出
export function feedexport(id, type,year) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/collection/export?id=${id}&type=${type}&year=${year}`,
      contentType: "application/json;charset=UTF-8",
      loading: true,
      responseType: "blob"
  });
}
// 催办
export function cuiban(data) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/collection/sendMessage`,
      contentType: "application/json;charset=UTF-8",
      loading: true,
      data
  });
}
// 任务管理获取反馈台账
export function queryCollection(id, type,year) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/collection/queryCollection?id=${id}&type=${type}&year=${year}`,
      contentType: "application/json;charset=UTF-8"
  });
}
// 历史台账通报导出
export function sendexport(latitudeId) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/collection/exportQuery?latitudeId=${latitudeId}`,
      contentType: "application/json;charset=UTF-8",
      loading: true,
      responseType: "blob"
  });
}
// 分公司任务管理
export function queryConclusion(data) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/subInfo/querySunInfo?source=PC`,
      contentType: "application/json;charset=UTF-8",
      data,
  });
}
// 模板下载
export function downloadTemplate(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/subInfo/downloadTemplate?source=PC`,
      contentType: "application/json; charset=utf-8",
      responseType: "blob"
  });
}
// 分公司任务保存导入内容
export function saveSunInfo(data) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/subInfo/saveSunInfo?source=PC`,
      contentType: "application/json;charset=UTF-8",
      data,
  });
}
// 反馈台账下拉框
export function getQueryCycle(data) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/latitude/queryCycle?cycle=${data.cycle || -1}&year=2025`,
      contentType: "application/json;charset=UTF-8",
  });
}
// 添加周期--修改周期
export function editQueryCycle(data) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/latitude/addCycle`,
      contentType: "application/json;charset=UTF-8",
      loading: true,
      data
  });
}
// 周期推送代办
export function onPushCycle(id) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/collection/pushCycle?id=${id}`,
      contentType: "application/json;charset=UTF-8",
      loading: true,
  });
}
// 任务管理获取反馈周期
export function getfeeddate(type,year) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/latitude/queryCycle?type=${type}&year=${year}`,
      contentType: "application/json;charset=UTF-8"
  });
}
// 获取派发工单详情
export function getFeedFormDetail(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/collection/getDetailForm?source=PC&pmInsId=${params.pmInsId}&location=${params.location}`,
      contentType: "application/json; charset=utf-8",
  });
}
// 反馈获取Ai润色内容
export function getAIContent(params, data) {
  let url = `/${process.env.VUE_APP_APPCODE}/action/branch/collection/polish?source=PC&location=${params.location}&pmInsId=${params.pmInsId}&taskId=${params.taskId}`;
  return request({
      url,
      contentType: "application/json;charset=UTF-8",
      data
  });
}
// 反馈保存反馈信息
export function saveFeedInfo(data) {
  let url = `/${process.env.VUE_APP_APPCODE}/action/branch/collection/saveCollection`;
  return request({
      url,
      contentType: "application/json;charset=UTF-8",
      data
  });
}
// 反馈流转下一步
export function toNextProcessFeed(params, data) {
  let url = `/${process.env.VUE_APP_APPCODE}/action/branch/collection/handle?source=PC&location=${params.location}&pmInsId=${params.pmInsId}&taskId=${params.taskId}`;
  return request({
      url,
      contentType: "application/json;charset=UTF-8",
      data
  });
}
// 结项管理
export function queryContent(data) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/check/queryContent?source=PC`,
      contentType: "application/json;charset=UTF-8",
      data,
  });
}
// 任务管理结项详情
export function conclusionDetail(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/check/queryDetail?source=PC&pmInsId=${params.pmInsId}&id=${params.id}`,
      contentType: "application/json;charset=UTF-8"
  });
}
// 周报维护获取
export function queryAIReport(latitudeId, taskInfo) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/polish/aiDetail`,
      contentType: "application/json;charset=UTF-8",
      data: {
          latitudeId,
          taskInfo
      }
  });
}
// 任务管理结项获取人员
export function conclusionPerson(data,params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/check/findNextUser?source=PC&id=${params.id}`,
      contentType: "application/json;charset=UTF-8",
      data
  });
}
// 任务管理结项提交 退回
export function conclusionSubmit(params, data) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/check/submit?source=PC&location=${params.location}&pmInsId=${params.pmInsId}&taskId=${params.taskId}&type=${params.type}&nextUser=${params.nextUser}`,
      contentType: "application/json;charset=UTF-8",
      data
  });
}
// 获取条线列表
export function queryLineType(){
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/document/queryLineType`,
      contentType: "application/json;charset=UTF-8"
  });
}
// 新增用户权限
export function userroleadd(data){
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/user/addUser`,
      contentType: "application/json;charset=UTF-8",
      data
  });
}
// 获取用户权限
export function userrolelist(data){
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/user/queryAll?source=PC`,
      contentType: "application/json;charset=UTF-8",
      data
  });
}
// 删除用户权限
export function userroleremove(id){
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/user/deleteUser`,
      contentType: "application/json;charset=UTF-8",
      data: { id }
  });
}
// 修改用户权限
export function userroleupdate(data){
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/user/updateUser`,
      contentType: "application/json;charset=UTF-8",
      data
  });
}
// 获取AI周报详情
export function AiDetail(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/distribute/distributeDetail?source=PC&pmInsId=${params.pmInsId}&taskId=${params.taskId}&location=${params.location}`,
      contentType: "application/json; charset=utf-8",
  });
}
// AI周报流转
export function AiNext(params, data) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/distribute/handle?source=PC&pmInsId=${params.pmInsId}&taskId=${params.taskId}&location=${params.location}`,
      contentType: "application/json; charset=utf-8",
      data
  });
}
// 获取变更记录
export function changelist(id,type){
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/admin/queryUpdateLog?id=${id}&type=${type}`,
      contentType: "application/json;charset=UTF-8"
  });
}
// 导出接口人
export function interfaceexport(data){
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/interface/export`,
      contentType: "application/json;charset=UTF-8",
      data,
      responseType: "blob"
  });
}
// 获取接口人
export function interfacelist(params, data){
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/interface/queryAdmin`,
      contentType: "application/json;charset=UTF-8",
      data
  });
}
// 删除接口人
export function interfaceremove(id){
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/interface/deleteById?id=${id}`,
      contentType: "application/json;charset=UTF-8",
  });
}
// 修改接口人
export function interfaceupdate(data){
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/interface/updateAdmin`,
      contentType: "application/json;charset=UTF-8",
      data
  });
}
// 导出任务负责人
export function taskUserexport(data,type){
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/admin/export?type=${type}`,
      contentType: "application/json;charset=UTF-8",
      data,
      responseType: "blob"
  });
}
// 获取任务负责人
export function queryTaskUser (data,type){
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/admin/queryTaskUser?type=${type}`,
      contentType: "application/json;charset=UTF-8",
      data
  });
}
// 修改任务负责人
export function updateTaskUser (data,type){
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/admin/updateTaskUser?type=${type}`,
      contentType: "application/json;charset=UTF-8",
      data
  });
}
// 修改任务负责人
export function isBraAdmin (){
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/branch/admin/isBraAdmin`,
      contentType: "application/json;charset=UTF-8",
  });
}
