
<template>
  <div style="width:100%;margin-bottom:10px">
    <div class="toptext"  v-if="tableTitle">
      <span>{{ tableTitle }}</span>
    </div>
    <sb-el-table v-if="childTypes == '1'" :table="table" @handleCreate="handleCreate" @handleDelete="handleDelete" @handleUpdate="handleUpdate" @handleUpdateGetRow="handleUpdateGetRow" :on-ok="handleDoFun"></sb-el-table>
    <el-table v-else size="small" border :show-summary="summary" :summary-method="getSummaries" :data="typetableArrInfo" style="width: 100%">
      <el-table-column type="index" width="50" />
      <template v-for="(item, index) in typetableArr">
        <el-table-column :label="item.label" :prop="item.id" min-width="200" :align="tableInfo.control.labelAlign || 'left'">
          <template slot-scope="scope">
            <el-select @change="handleUpdate2(scope.row,scope.$index)" v-if="item.type === 'select'" v-model="scope.row[item.id]" :size="item.size || 'mini'" :placeholder="item.placeholder || '请选择'" :multiple="item.multiple || false" :disabled="item.disabled || false" :clearable="item.clearable || false" :multiple-limit="item.multipleLimit || 0" :props="item.props || {value: 'value', label: 'name'}">
              <el-option v-for="(o, i) in item.options" :key="o.value" :label="o.name" :value="o.value">
              </el-option>
            </el-select>
            <el-date-picker @change="handleUpdate2(scope.row,scope.$index)" v-model="scope.row[item.id]" v-else-if="item.type === 'date'" :type="item.subtype || 'datetime'" :value-format="item.valueFormat || 'yyyy-MM-dd HH:mm:ss'" 
                :format="item.viewFormat || item.valueFormat || 'yyyy-MM-dd HH:mm:ss'" :size="item.size || 'mini'" :disabled="item.disabled || false" :readonly="item.readonly || false" :editable="item.editable || true" 
                :placeholder="item.placeholder || '请选择' + item.label || '请选择'" 
                v-bind="$attrs" v-on="$listeners" range-separator="至" start-placeholde="开始时间" end-placeholde="结束时间" 
                :default-time="item.defaultTime ||(!item.subtype || (item.subtype && item.subtype.indexOf('time') > -1) ? item.subtype == 'datetimerange'? ['00:00:00', '23:59:59'] : '00:00:00': null)">
            </el-date-picker>
            <el-input @blur="handleUpdate2(scope.row,scope.$index)" v-else :size="item.size || 'mini'" v-model="scope.row[item.id]" :disabled="item.disabled || false"/>
          </template>
        </el-table-column>
      </template>
      <el-table-column align="right" width="80" v-if="!taleDisabled" fixed="right">
        <template #header>
          <el-button size="mini" type="primary" @click="handleAdd">新增</el-button>
        </template>
        <template slot-scope="scope">
          <!-- <el-button size="mini" type="primary" @click="handleUpdate2(scope.row,scope.$index)">编辑</el-button> -->
          <el-button size="mini" type="danger" @click="handleDelete2(scope.row,scope.$index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import store from "@/store";
import util from "@/assets/js/public";
import { getDelete ,getUpdateFormSubmit } from '@/api/public'
export default {
  name: 'sublist',
  props: {
    tableInfo: {
      type: Object,
      required: true
    },
    item: {
      type: Object,
      required: true
    },
    gps: {
      type: Object,
      default:function () {  
        return {  
          location:'',
          action:'',
          type:''
        };  
      }
    },
    appFormValue: {
      type: Object,
      required: true
    },
  },
  data() {
    return {
      summary:false,
      // 1弹窗 2行内编辑
      childTypes: '1',
      tableName: '',
      tableTitle: '',
      table: {
        modulName: 'table-子表', // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: 'dialog',
        hasShowSummary:false, //是否合计
        total: null,
        hasQueryForm: false, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: '90px',
          formItemList: [],
        },
        tr: [],
        // hasSetup:true,
        // setup:[],
        processType: [],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: '800px',
          labelWidth: '160px',
          inline: true,
          formItemList: [],
          labelPosition: 'right',
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: '100',
          fixed: 'right',
          data: [
            {
              id: 'add', 
              name: '新增',
              fun: 'handleCreate', 
            },
            // {id: "handleTodo",name: "查看", fun:"handleTodo"},
            { id: 'update', name: '编辑', beforeFun: 'handleUpdateGetRow', fun: 'handleUpdate' },
            { id: 'delete', name: '删除', fun: 'handleDelete' },
          ],
        },
        hasPagination: false,
        listQuery: { size: 10, page: 1 },
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },

      // 行内编辑
      typetableArrInfo: [],
      tablenameObject: {},
      typetableArr: [],
      taleDisabled: false,
    }
  },
  created() {
    this.gettableData();
  },
  mounted() { },
  methods: {
    gettableData() {
      this.tableTitle = this.tableInfo.item.label
      if (this.tableInfo.control.childTypes == '1') {
        this.childTypes = '1'
        let tableArr = []
        this.table.modulName = 'table-' + this.tableInfo.item.label
        this.tableInfo.list.forEach((element) => {
          let tableObject = {
            id: element.name,
            prop: element.name,
            label: element.item.label,
            minWidth: (Number(element.item.label.length) * 40).toString()
          }
          if(!element.control.ishide){
            tableArr.push(tableObject)
          }
        })
        this.table.tr = tableArr
        this.table.form.formItemList = this.util.goToHandelFormList(this.tableInfo.list)
        this.table.hasShowSummary = this.tableInfo.control.summary
      } else {
        this.childTypes = '2'
        let tableArr = []
        let tablenameObject = {}
        this.tableInfo.list.forEach((element) => {
          let tableObject = {
            id: element.name,
            label: element.item.label,
            placeholder: element.control.placeholder,
            type: element.type,
            show: true
          }
          if (this.gps.location || (this.gps.action && this.gps.action == 'read')) {
              if (
                (this.gps.type != 'draft' && this.gps.type != 'task') ||
                this.gps.location != process.env.VUE_APP_APPCODE + '.start'
              ) {
                if(this.gps.type == 'task' && this.tableInfo.control.processArr && this.tableInfo.control.processArr.length>0){
                  tableObject.disabled = this.getChangeData("changeData",this.tableInfo.control.processArr)
                }else{
                  tableObject.disabled = true
                }
              }else{
                tableObject.disabled = false
              }
          }
          if (element.type == 'datePicker') {
            tableObject.type = 'date'
            tableObject.subtype = element.control.type
            tableObject.valueFormat = element.control.format
            tableObject.viewFormat = element.control.format
          }
          if (element.type == 'select') {
            tableObject.options = element.options
          }
          tableArr.push(tableObject)
          tablenameObject[element.name] = ''
        })
        this.typetableArr = tableArr
        this.tablenameObject = tablenameObject
        this.summary = this.tableInfo.control.summary
      }


      if (this.gps.location || (this.gps.action && this.gps.action == 'read')) {
        if(this.appFormValue && this.item && this.item.key){
          if (this.tableInfo.control.childTypes == '1') {
            this.table.data = this.appFormValue[this.item.key] ? this.appFormValue[this.item.key] : []
          }else{
            this.typetableArrInfo = this.appFormValue[this.item.key] ? this.appFormValue[this.item.key] : []
          }
        }
        if (
          (this.gps.type != 'draft' && this.gps.type != 'task') ||
          this.gps.location != process.env.VUE_APP_APPCODE + '.start'
        ) {
          if(this.gps.type == 'task' && this.tableInfo.control.processArr && this.tableInfo.control.processArr.length>0){
            this.table.hasOperation = !this.getChangeData("changeData",this.tableInfo.control.processArr)
            this.taleDisabled = this.getChangeData("changeData",this.tableInfo.control.processArr)
          }else{
            this.table.hasOperation = false
            this.taleDisabled = true
          }
        }else{
          this.table.hasOperation = true
          this.taleDisabled = false
        }
      }
    },
    // 弹窗确定事件
    handleCreate() {
      this.table.data.push(this.table.listFormModul)
      this.table.dialogVisible = false
      this.$emit("chooseData", this.table.data);
    },
    handleUpdate(){
      if(this.gps.location && this.gps.type == 'task'){
        let tableName = this.tableInfo.control.childId
        let appCode = process.env.VUE_APP_APPCODE
        getUpdateFormSubmit(
          appCode,
          tableName,
          this.table.listFormModul
        ).then(() => {
          this.table.data.splice(this.table.listFormModul.updateIndex,1,this.table.listFormModul)
        })
      }else{
        this.table.data.splice(this.table.listFormModul.updateIndex,1,this.table.listFormModul)
      }
      this.table.dialogVisible = false
      this.$emit("chooseData", this.table.data);
    },
    handleUpdateGetRow(row){
      for (var i in this.table.form.formItemList) {
        this.table.form.formItemList[i].disabled = row.read ? true : false;
      }
      this.table.form = JSON.parse(JSON.stringify(this.table.form));
      this.table.listFormModul = row
    },
    // 子表删除事件
    handleDelete(data, index) {
      if(this.gps.location && this.gps.type == 'task'){
        let tableName = this.tableInfo.control.childId
        let appCode = process.env.VUE_APP_APPCODE
        getDelete(
          appCode,
          tableName,
          data.id
        ).then(() => {
          this.table.data.splice(index, 1)
        })
      }else{
        this.table.data.splice(index, 1)
      }
      this.$emit("chooseData", this.table.data);
    },
    handleAdd() {
      let newAdd = JSON.parse(JSON.stringify(this.tablenameObject))
      this.typetableArrInfo.push(newAdd)
      this.$emit("chooseData", this.typetableArrInfo);
    },
    handleDelete2(data,index) {
      if(this.gps.location && this.gps.type == 'task'){
        let tableName = this.tableInfo.control.childId
        let appCode = process.env.VUE_APP_APPCODE
        getDelete(
          appCode,
          tableName,
          data.id
        ).then(() => {
          this.typetableArrInfo.splice(index, 1)
        })
      }else{
       this.typetableArrInfo.splice(index, 1)
      }
      this.$emit("chooseData", this.typetableArrInfo);
    },
    handleUpdate2(data,index){
        if(this.gps.location && this.gps.type == 'task'){
          let tableName = this.tableInfo.control.childId
          let appCode = process.env.VUE_APP_APPCODE
          getUpdateFormSubmit(
            appCode,
            tableName,
            data
          ).then(() => {
            this.typetableArrInfo.splice(index,1,data)
            this.$emit("chooseData", this.typetableArrInfo);
          })
        }
    },
    getChangeData(type, arr) {
      // type showData是否显示  changeData是否编辑
      let arrs = arr
      if (this.gps.location == process.env.VUE_APP_APPCODE + '.') {
        return true
      } else {
        let location = this.gps.location ? this.gps.location : process.env.VUE_APP_APPCODE + '.start'
        const apiInfo = arrs.filter((item) => item.activityDefId == location)
        if (apiInfo.length > 0) {
          return !apiInfo[0][type]
        } else {
          return true
        }
      }
    },
     getSummaries(param) {
        const { columns, data } = param;
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '合计';
            return;
          }
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr == 0 ? '' : prev + curr;
              } else {
                return prev;
              }
            }, 0);
            sums[index] == 0 ? '' : sums[index];
          } else {
            sums[index];
          }
        });
        return sums;
      },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n;
      if (obj) {
        n = this[obj[fun]].call(this, obj, data);
      } else {
        n = this[fun].call(this, data);
      }
      return n;
    },
  }
}
</script>

<style scoped>
.toptext {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
}
::v-deep .table-container{
  padding: 0;
}
::v-deep .table-container .el-table{
  border: none !important;
}
</style>
