<template>
  <div class="w100 inputBtn">
    <el-container class="contwarp">
      <el-main class="warpLeft">
        <!--        <div class="card-header">-->
        <!--          <el-input v-model="searchName" placeholder="请输入姓名进行搜索" style="width:200px" clearable/>-->
        <!--          <el-button class="button" type="primary" style="margin-left:10px" @click="searchNames()" size="mini">搜索</el-button>-->
        <!--        </div>-->
        <el-tree v-if="!trueBtn" :data="treeData" class="tree1" :key="clickKey" style="height: 56vh;overflow: auto;padding-bottom: 32px;"
                 :props="item.defaultProps || defaultProps" ref="chooseOrgTree" :default-expand-all="true" :default-expanded-keys="expandKeys"
                 :node-key="item.nodeKey || 'id'" :check-on-click-node="true" @node-expand="nodeExpand">
          <!-- :expand-on-click-node="false" -->
          <template #default="{ node, data }">
            <div @click="onTreeNodeClick(node, data)" style="width:100%;height: 100%;line-height: 26px;">{{ data.name }}</div>
          </template>
        </el-tree>
        <el-tree class="tree2" v-if="trueBtn" :key="(clickKey + 1)" style="height: 56vh;overflow: auto;padding-bottom: 32px;"
                 :props="item.defaultProps || defaultProps" ref="chooseOrgTree" :data="filteredTreeData" default-expand-all :highlight-current="true"
                 :node-key="item.nodeKey || 'id'" :check-on-click-node="true">
          <template #default="{ node, data }">
            <div @click="onTreeNodeClick(node, data)" style="width:100%;height: 100%;line-height: 26px;">{{ data.name }}</div>
          </template>
        </el-tree>
      </el-main>
      <el-aside width="320px" class="asideR">
        <h5 class="fbold">已选人员</h5>
        <div class="choose-department-checked">
          <div class="choose-department-item" v-for="(citem, index) in multipleSelection" :key="citem[item.nodeKey || 'id']">
            <div class="choose-department-item-text ellipsis-line-1">
              <!-- {{ citem[item.defaultProps.label || "name"] }} -->
              {{ citem.name }}
            </div>
            <span class="choose-department-item-close" @click="delChoose(citem)">x</span>
          </div>
        </div>
      </el-aside>
    </el-container>
    <span slot="footer" class="footer">
			<el-button @click="handleUp" size="small">取消</el-button>
			<el-button type="primary" @click="handleUp('yes')" size="small">确定</el-button>
		</span>
  </div>
</template>
<script>
import {conclusionPerson, getUserByType} from "@/api/branchCompany";
import {findOneStep} from "@/api/senior";

export default {
  name: "chooseUser",
  props: {
    item: {
      type: Object,
      required: true
    },
    onOk: {
      type: Function
    },
    cd: {type: Array,},
    gps: {
      type: Object,
      default: {}
    },
    appFormValue: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      dialogVisible: false,
      defaultProps: {
        children: "children",
        label: "name",
        isLeaf: 'leaf',
      },
      treeData: [],
      multipleSelection: [],
      defaultCheckedKs: [],
      reqs: [],
      searchName: "",
      trueBtn: false,
      filteredTreeData: [],
      clickKey: 0,
      expandKeys: []
    };
  },
  methods: {
    nodeExpand(obj, node) {
      // if (node.level > 3 && this.item.type == 4) {
      //   if (obj.children?.[0].name != null) return // 如果有孩子了就说明刚才已经加载过，就不继续请求接口了
      //   /** 点击第二层时请求接口 */
      //   findOneStep(obj.id).then(res => {
      //     res.data.forEach(a => {
      //       if (a.treeType == 'org') {
      //         a.children = [{name: null}]
      //       }
      //     })
      //     obj.children = res.data // 这个地方感觉想到的非常棒！目前还没测出bug（把请求的第三层的数据直接加载点击的第二层的对象的孩子下！，利用了vue响应式数据的原理应该）
      //   })
      // }
    },
    // 按钮事件
    handleUp(op) {
      if (op == "yes") {
        this.$emit("flowdata", this.multipleSelection);
      }
      this.$emit("closeshowDialogFun");
      this.$emit("closeshowDialog");
    },
    searchNames() {
      if (this.searchName.trim()) {
        this.trueBtn = true
        this.$nextTick(() => {
          this.clickKey++;
          findDimUserTree(this.searchName.trim()).then(({data}) => {
            let newData = this.util.toTreeData(
                data,
                'id',
                'parentId',
                'id,parentId,name,treeLevel,treeType,orgDisplayName'
            );
            this.filteredTreeData = newData
          })
        })

      } else {
        this.trueBtn = false
        this.$nextTick(() => {
          this.clickKey++;
        })
      }
    },
    onTreeNodeClick(node, data) {
      if (data.treeType == 'user') {
        if (this.trueBtn) {
          data.orgDisplayName = node.parent.data.orgDisplayName
        }
        if (
            (!this.item.mulitple && this.item.mulitple !== false) ||
            this.item.mulitple === true
        ) {
          //多选
          this.writeNodeToLocalChecked(data);
        } else {
          this.multipleSelection = [data]
        }
      }
    },
    writeNodeToLocalChecked(node) {
      if (!this.arrayIsIncludeTheObject(this.multipleSelection, node)) {
        this.multipleSelection.push({...node})
      }
    },
    arrayIsIncludeTheObject(arr, obj, key = 'id') {
      return arr.some((every) => Object.is(every[key], obj[key]))
    },


    openDialog(e) {
      // this.trueBtn = false
      this.multipleSelection = [];
      //console.log(this.$refs.elInput);
      let inputData = this.$refs.elInput._props;
      let formData = this.$refs.elInput.elForm._props.model;
      // console.log(formData);
      let relevancy = this.item.relevancy.split(",");
      let di = formData[relevancy[0].split("-")[0]] ?
          formData[relevancy[0].split("-")[0]].split(",").length :
          0;
      if (di === 0) {
        this.multipleSelection = [];
        this.defaultCheckedKs = [];
        if (this.$refs.chooseOrgTree)
          this.$refs.chooseOrgTree.setCheckedKeys([]);
      }
      for (let i = 0; i < di; i++) {
        let datai = {};
        for (let j in relevancy) {
          var reF = relevancy[j].split("-");
          if (reF.length === 1) reF.push(reF[0]);
          datai[reF[1]] = formData[reF[0]] ?
              formData[reF[0]].split(",")[i] :
              "";
        }
        var ml = 0;
        for (let k in this.multipleSelection) {
          for (let j in relevancy) {
            var reF = relevancy[j].split("-");
            if (reF.length === 1) reF.push(reF[0]);
            if (datai[reF[1]] === this.multipleSelection[k][reF[1]]) ml++;
          }
        }
        if (ml !== relevancy.length) {
          this.multipleSelection.push(datai);
          this.defaultCheckedKs.push(datai[this.item.nodeKey || "id"]);
        }
      }
      this.dialogVisible = true;
    },
    handleConfirm() {
      this.dialogVisible = false;
      this.$emit("chooseData", this.multipleSelection);
      if (this.onOk && this.item.handleUser) {
        this.onOk(this.item, "handleUser", this.multipleSelection);
      }
    },
    delChoose(row) {
      if (
          (!this.item.mulitple && this.item.mulitple !== false) ||
          this.item.mulitple === true
      ) {
        //多选
        let arry = JSON.parse(JSON.stringify(this.multipleSelection));
        for (let i in arry) {
          if (arry[i][this.item.nodeKey || "id"] === row[this.item.nodeKey || "id"]) arry.splice(i, 1);
        }
        this.multipleSelection = arry;
      } else {
        //单选
        this.multipleSelection = [];
        this.$refs.chooseOrgTree.setCheckedKeys([]);
      }
    },
    async getList() {
      let res = await conclusionPerson({
        location: this.gps.location || 'yqff.start',
        type: this.item.type,
        taskId: this.gps.taskId || '',
        pmInsId: this.gps.pmInsId || '',
        interfaceAdminCode: this.appFormValue.interfaceAdminCode,
        interfaceAdminName: this.appFormValue.interfaceAdminName,
        
      },{id:this.appFormValue.id});

      this.treeData = this.arrayToTree(res.data || [])
    },
    arrayToTree(items) {
      const result = []; // 结果数组，将存放所有顶级节点
      const itemMap = {}; // 以id为键存储节点对象的映射表

      // 首先将所有节点放入映射表中，便于快速查找
      items.forEach(item => {
        item.children = []; // 初始化children数组
        itemMap[item.id] = item; // 存入映射表
      });

      items.forEach(item => {
        const parentId = item.parentId;
        if (parentId === null || parentId === undefined) {
          // 如果没有父节点，说明这是一个顶级节点
          result.push(item);
        } else {
          // 如果有父节点，尝试从映射表中获取父节点
          const parent = itemMap[parentId];
          if (parent) {
            parent.children.push(item); // 将当前节点加入到父节点的children数组中
          }
        }
      });

      return result;
    }

  },
  async created() {
    await this.getList()
    if (this.cd.length > 0) {
      this.multipleSelection = this.cd
    }
  }
};
</script>
<style scoped>
.icon {
  margin: 0;
}

.el-dialog__body {
  padding: 0px 20px 30px;
}

.el-main {
  padding: 0px;
  border-left: 0px solid #e0e0e0;
}

.asideR {
  border-left: 1px solid #e0e0e0;
  padding: 0px 15px 0px;
}

.chooseD a {
  display: block;
  padding: 5px 0;
}

.choose-department-checked {
  width: 100%;
  box-sizing: border-box;
  padding: 10px;
  height: calc(100% - 40px);
  overflow-y: auto;
  border: 1px solid #e6ebf5;
}

.choose-department-item {
  height: 30px;
  -js-display: flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.choose-department-item-text {
  width: 80%;
  user-select: none;
}

.choose-department-item-close {
  cursor: pointer;
  font-size: 16px;
}

.contwarp {
  height: 58vh;
  overflow: hidden;
}

.warpLeft {
  height: 100%;
  overflow: hidden;
}

.warpLeft ::v-deep .el-input__validateIcon {
  display: none;
}

.footer {
  display: flex;
  justify-content: flex-end;
  padding: 15px;
}
</style>