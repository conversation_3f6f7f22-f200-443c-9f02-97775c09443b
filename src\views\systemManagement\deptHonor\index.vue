<template>
  <div class="app-container">
    <sb-el-table
      :table="table"
      @getList="getList"
      @handleUpdate="handleUpdate"
      @handleDeleteNew="handleDeleteNew"
      @handleCreate="handleCreate"
      @uploadFileList="uploadFileList"
      @handleUpdateGetRow="handleUpdateGetRow"
      :on-ok="handleDoFun"
    >
      <template v-slot:RECEIPTTILE="{ obj }">
        <span class="toDetail" @click="handleTodo(obj)">{{
          obj.row.title
        }}</span>
      </template>
      <template v-slot:formFileList="{ obj }">
           <span class="toDetail" @click="down(obj)">{{
          obj.row.formFileList.length>0?obj.row.formFileList[0].fileName:'暂无'
        }}</span>
        </template>
    </sb-el-table>
  </div>
</template>
<script>
import { uploadProcessFiles } from "@/api/public";
import WorkOrder from "@/components/WorkOrder";
import { selectAll,UsDeptHonorDelete,UsDeptHonorEdit ,UsDeptHonorAdd} from "@/api/apply/application.js";
export default {
  name: "processTask",
  components: { WorkOrder },
  data() {
    return {
      viewD: false,
      dialogTitle: "",

      gps: {
        type: "task",
        location: "",
        pmInsType: "",
      },

      cKey: 0,
      table: {
        modulName: "processTask-待办列表", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: true, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "90px",
          formItemList: [{ label: "荣誉名称", key: "name", type: "input" }],
        },
        tr: [
          { id: "name", label: "荣誉名称", prop: "name", width: 400 },
          { id: "describe", label: "荣誉描述", prop: "describe", width: 400 },
          { id: "formFileList", label: "相关附件", prop: "formFileList",show: 'template' },
        ],
        // hasSetup:true,
        // setup:[],
        processType: [],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "600px",
          labelWidth: "100px",
          inline: true,
          formItemList: [
            {
              class: "c12",
              label: "荣誉名称",
              key: "name",
              placeholder: "",
              type: "input",
              rule: { required: true },
            },
            {
              class: "c12",
              label: "荣誉描述",
              key: "describe",
              type: "input",
              placeholder: "",
              inputType: "textarea",
              rule: { required: true },
              maxlength: 4000,
            },
            {
              class: "c12",
              label: "成果附件",
              key: "formFileList",
              type: "sbUpload",
              btnText: "",
              fun: "uploadFileList",
              listType: "text",
              multiple: false,
              rule: { required: true },
            },
          ],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: "120",
          fixed: "right",
          data: [
            { id: "update", name: "编辑", fun: "handleUpdate" },
            { id: "del", name: "删除", fun: "handleDeleteNew" },
            { id: "add", name: "添加", fun: "handleCreate" },
          ],
        },
        hasPagination: true,
        listQuery: { size: 10, page: 1 },
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
      tableIndex: 0,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 查询列表
    getList(listQuery) {
      this.table.loading = true;
      selectAll(listQuery || this.table.listQuery)
        .then((res) => {
          this.table.loading = false;
          this.table.data = res.data.content;
          this.table.total = res.data.totalElements;
        })
        .catch((err) => {
          this.table.loading = false;
        });
    },
    handleUpdateGetRow(row, index) {
      console.log(row);
      this.table.listFormModul = row;
     
    },
    handleUpdate() {
       delete this.table.listFormModul.modifiedTime;
      delete this.table.listFormModul.createdTime;
      console.log(this.table.listFormModul);
        UsDeptHonorEdit(this.table.listFormModul)
       .then((res) => {
          this.getList();
        this.table.dialogVisible = false;
        })
       .catch((err) => {
          this.$message.error(err);
        });
      
      this.table.dialogVisible = false;
    },
    handleCreate() {
      
      UsDeptHonorAdd(this.table.listFormModul)
       .then((res) => {
          this.getList();
        this.table.dialogVisible = false;
        })
       .catch((err) => {
          this.$message.error(err);
        });
    },
    handleDeleteNew(obj) {
        console.log(obj);
        this.$confirm("确定删除该条数据吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          UsDeptHonorDelete({
            id: obj.row.id,
          })
          .then((res) => {
              this.getList();
            })
          .catch((err) => {
              this.$message.error(err);
            });
        });
     
    },
    uploadFileList(obj) {
      uploadProcessFiles(obj.formData)
        .then(async (res) => {
          obj.content.onSuccess(res, obj.content.file, []);
        })
        .catch(() => {
          obj.content.onError();
        });
    },
    down(obj){
        this.util.fileOpen(obj.row.formFileList[0]?.id);

    },

    // 关闭弹框
    dialogClose() {
      this.viewD = false;
      this.getList();
    },

    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },

    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    },
  },
};
</script>
<style scoped>
/* 修改公共样式弹框样式 */
::v-deep .el-dialog__header {
  text-align: center !important;
  background: white !important;
  color: black;
  font-size: 14px;
  font-weight: bold;
  border-bottom: 1px solid #f2f2f2 !important;
}
::v-deep .el-dialog__title {
  color: black !important;
  font-size: 15.5px;
}
::v-deep .el-dialog__headerbtn .el-dialog__close {
  color: black;
}
</style>