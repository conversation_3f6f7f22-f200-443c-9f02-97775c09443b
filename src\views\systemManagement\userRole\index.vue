<template>
  <div class="app-container" style="padding-bottom: 80px;">
    <sb-el-table :table="table" @getList="getList" @updateTableData="updateTableData" :on-ok="handleDoFun" @handleAdd="handleAdd" @editHandle="editHandle"
                 @editHandleGetRow="editHandleGetRow" @handleCopyGetCopy="handleCopyGetCopy" @lookHandle="lookHandle" @removeHandle="removeHandle" @uploadFileList="uploadFileList" @handlePush="handlePush"
                 @handlePushRecords="handlePushRecords" @handleCopy="handleCopy">
      <template v-slot:isIndex="{obj}">
        {{obj.row.isIndex == 1 ? '是' : '否'}}
      </template>
      <template v-slot:latitudeId="{obj}">
        {{ latitudeList.filter(a => a.value == obj.row.latitudeId)?.[0].name }}
      </template>
      <template v-slot:lineTypeId="{obj}">
        {{ lineTypeList.filter(a => a.value == obj.row.lineTypeId)?.[0].name }}
      </template>
      <template v-slot:isPush="{obj}">
        <span v-if="obj.row.isPush == 1">已推送</span>
        <span v-if="obj.row.isPush == 0">未推送</span>
      </template>
    </sb-el-table>

    <!-- 推送记录对话框 -->
    <el-dialog
      title="推送记录"
      :visible.sync="pushRecordsDialogVisible"
      width="80%"
      :close-on-click-modal="false"
      v-dialogDrag
    >
      <el-table
        v-loading="pushRecordsLoading"
        :data="pushRecordsData"
        border
        style="width: 100%"
      >
        <el-table-column prop="belongCompanyName" label="公司" align="center"></el-table-column>
        <el-table-column prop="belongDepartmentName" label="部门" align="center"></el-table-column>
        <el-table-column prop="belongOrgName" label="科室" align="center"></el-table-column>
        <el-table-column prop="applyTrueName" label="推送人" align="center"></el-table-column>
        <el-table-column prop="createdTime" label="推送时间" align="center"></el-table-column>
        <el-table-column prop="status" label="状态" align="center"></el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="push-records-pagination">
        <el-pagination
          @size-change="handlePushRecordsSizeChange"
          @current-change="handlePushRecordsCurrentChange"
          :current-page="pushRecordsQuery.page"
          :page-sizes="[5, 10, 20, 50]"
          :page-size="pushRecordsQuery.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pushRecordsTotal"
          background>
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getfeeddate,
  queryLineType,
  userroleadd,
  userrolelist,
  userroleremove,
  userroleupdate,
  pushAi,
  queryDictByType,
  getPushRecords
} from "@/api/process";
import {uploadProcessFiles} from "@/api/public";

export default {
  data() {
    return {
      title: "",
      showDialog: false,
      curComponent: "",
      pageLabel: "taskInfo",
      groupOptions: [], // 发送群组选项
      originalGroupData: [], // 原始群组数据
      pushRecordsDialogVisible: false, // 推送记录对话框是否可见
      pushRecordsLoading: false, // 推送记录加载状态
      pushRecordsData: [], // 推送记录数据
      pushRecordsTotal: 0, // 推送记录总数
      currentDistributeId: '', // 当前查看的推送记录ID
      pushRecordsQuery: { // 推送记录查询参数
        page: 1,
        size: 10
      },
      table: {
        modulName: "周报推送管理-周报推送", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: false, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: false, // 是否有查询条件
        queryForm: {
          inline: true,
          labelPosition: 'right',
          labelWidth: "120px",
          formItemList: [
            {label: "反馈周期", key: "latitudeId", type: "select", class: 'c4', options: []},
          ],
        },
        tr: [
          // {id: "latitudeId", label: "反馈周期", prop: 'latitudeId', show: 'template', template: 'latitudeId', width: 320},
          {id: "title", label: "标题", prop: 'title'},
          {id: "situation", label: "周报内容", prop: 'situation'},
          // {id: "lineTypeId", label: "所属条线", prop: 'lineTypeId', show: 'template', template: 'lineTypeId', width: 100},
          // {id: "isIndex", label: "是否默认展示看板", prop: 'isIndex', show: 'template', template: 'isIndex', width: 140},
          {id: "trueNames", label: "查看人员范围", prop: 'trueNames'},
          {id: "groupName", label: "发送群组", prop: 'groupName'},
          {id: "modifiedTime", label: "最后修改时间", prop: 'modifiedTime', width: 160},
          {id: "isPush", label: "推送状态", prop: 'isPush', width: 160,show: 'template', template: 'isPush'}

        ],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "1000px",
          labelWidth: "150px",
          inline: true,
          formItemList: [
            // {class: "c12", label: "反馈周期", key: "latitudeId", type: "select", options: [], rule: { required: true}},
            {class: "c12", label: "类型", key: "sendType", type: "select", options: [
              {name: "周报", value: "01"},
              {name: "党建资讯", value: "02"}
            ], rule: { required: true}},
            {class: "c12", label: "标题", key: "title", type: "input", rule: {}, show: false},
            {class: "c12", label: "发送群组", key: "groupIds", type: "checkbox", options: [], rule: {}, show: false},
            {class: "c12", label: "周报内容", key: "situation", type: "input", inputType: 'textarea', autosize: {minRows: 2}, rule: { required: true }},
            // {class: "c12", label: "所属条线", key: "lineTypeId", type: "select", options: [], rule: { required: true}},
            // {class: "c12", label: "是否默认展示看板", key: "isIndex", type: "radio", options: [{name: '是', value: '1'}, {name: '否', value: '0'}], rule: { required: true}},
            {
              class: 'c12', label: '查看人员姓名', key: 'trueNames', type: 'user',
              readonly: true, mulitple: true, stepLoad: true, appendShow: true, rule: { required: true },
              relevancy: "trueNames-name,userNames-id", defaultProps: {
                children: "children",
                label: "name",
                isLeaf: 'leaf',
              }
            },
            {class: "c12", label: "查看人员OA账号", key: "userNames", type: "input", disabled: true},
				    {class: "c12",label: "开始时间",key: "startDate",type: "date",subtype: "date",valueFormat: "yyyy-MM-dd",rule: {required: true}},
				    {class: "c12",label: "结束时间",key: "endDate",type: "date",subtype: "date",valueFormat: "yyyy-MM-dd",rule: {required: true}},
            {
              class: "c12",
              label: "查看附件",
              key: "file",
              type: "sbUpload",
              btnText: "上传",
              fun: "uploadFileList",
              listType: "text",
              multiple: false,
              rule: {required: true},
              show: true,
              isfile: true,
              // accept: '.doc, .docx, .pdf'
            }
          ],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: "400",
          fixed: "right",
          data: [
            // {id: "read", name: "【查看】", fun: "lookHandle"},
            {id: "update", name: "【编辑】", fun: "editHandle",show:'isPush|0'},
            {id: "delete", name: "【删除】", fun: "removeHandle",show:'isPush|0'},
            {id: "copy", name: "【复制】", fun: "handleCopy"},
            {id: "push", name: "【推送】", fun: "handlePush", show:'isPush|0'},
            {id: "records", name: "【推送记录】", fun: "handlePushRecords"},
            {id: "add", type: "danger", name: "新增", fun: "handleAdd"},
          ],
        },
        hasOtherQueryBtn: false, //是否有其他操作
        otherQueryBtn: {
          data: [
            // {id: "export", type: "success", name: "导出", fun: "handleExport"}
          ]
        },
        hasPagination: true,
        listQuery: {
          latitudeId: null,
          page: 1,
          size: 10
        },
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
      optionsList: []
    }
  },
  computed: {
    latitudeList() {
      return this.table.queryForm.formItemList[0].options
    },
    lineTypeList() {
      return this.table.form.formItemList[2].options
    }
  },
  created() {
    this.getList()
    // this.getFeedDateList()
    // this.getLineTypeList()
    // 设置默认值
    this.$set(this.table.listFormModul, 'sendType', '01')
    // 加载群组数据
    this.loadGroupOptions()
  },
  watch: {
    'table.listFormModul.sendType': {
      handler: function(newVal) {
        // 控制字段的显示和隐藏以及验证规则
        if (newVal === '01') { // 周报
          // 隐藏标题字段并移除必填验证
          this.table.form.formItemList[1].show = false
          this.table.form.formItemList[1].rule = {}

          // 隐藏发送群组字段并移除必填验证
          this.table.form.formItemList[2].show = false
          this.table.form.formItemList[2].rule = {}

          // 显示周报内容并添加必填验证
          this.table.form.formItemList[3].show = true
          this.table.form.formItemList[3].rule = { required: true }

          // 显示查看人员姓名、查看人员OA账号并添加必填验证
          this.table.form.formItemList[4].show = true
          this.table.form.formItemList[4].rule = { required: true }
          this.table.form.formItemList[5].show = true

          // 显示开始时间、结束时间并添加必填验证
          this.table.form.formItemList[6].show = true
          this.table.form.formItemList[6].rule = { required: true }

          this.table.form.formItemList[7].show = true
          this.table.form.formItemList[7].rule = { required: true }
        } else if (newVal === '02') { // 案例
          // 显示标题字段并添加必填验证
          this.table.form.formItemList[1].show = true
          this.table.form.formItemList[1].rule = { required: true }

          // 显示发送群组字段并添加必填验证
          this.table.form.formItemList[2].show = true
          this.table.form.formItemList[2].rule = { required: true }
          this.table.form.formItemList[2].options = this.groupOptions

          // 隐藏周报内容并移除必填验证
          this.table.form.formItemList[3].show = false
          this.table.form.formItemList[3].rule = {}

          // 隐藏查看人员姓名、查看人员OA账号并移除必填验证
          this.table.form.formItemList[4].show = false
          this.table.form.formItemList[4].rule = {}
          this.table.form.formItemList[5].show = false

          // 隐藏开始时间、结束时间并移除必填验证
          this.table.form.formItemList[6].show = false
          this.table.form.formItemList[6].rule = {}

          this.table.form.formItemList[7].show = false
          this.table.form.formItemList[7].rule = {}
        }

        // 强制刷新表单显示
        this.$forceUpdate()
      },
      immediate: true
    }
  },
  methods: {
    handlePush(obj){
      this.$confirm('确定推送该周报吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        pushAi(obj.row.id).then(() => {
          this.getList()
        });

      }).catch(() => {
        this.$message({
          type: 'info',
        })
      })

    },
    uploadFileList(obj) {
      uploadProcessFiles(obj.formData).then(async res => {
        obj.content.onSuccess(res, obj.content.file, []);
      }).catch(() => {
        obj.content.onError();
      });
    },
    async getLineTypeList() {
      let res = await queryLineType()
      this.table.form.formItemList[2].options = res.data.map(item => {
        return {
          name: item.line_type || item.LINE_TYPE,
          value: item.LINE_TYPE_ID
        }
      })
    },
    async getFeedDateList() {
      let res = await getfeeddate('')
      this.table.queryForm.formItemList[0].options = res.data.map(item => {
        return {
          name: `第${item.cycle}期（${this.dateToText(item.startDate)} - ${this.dateToText(item.endDate)}）`,
          value: item.id
        }
      })
      this.table.form.formItemList[0].options = this.table.queryForm.formItemList[0].options
      if (res.data.length) {
        this.table.listQuery.latitudeId = res.data[0].id
        await this.getList()
      }
    },
    dateToText(date) {
      if (date) {
        let arr = date.split('-')
        return arr[0] + '年' + arr[1] + '月' + arr[2] + '日'
      }
      return ''
    },
    lookHandle(row) {
      Object.assign(this.table.listFormModul, row)
    },
    editHandleGetRow(row) {
      // 先清空表单
      this.table.listFormModul = {}
      // 设置默认类型
      this.$set(this.table.listFormModul, 'sendType', row.sendType || '01')
      // 然后填充数据
      Object.assign(this.table.listFormModul, row)

      // 处理群组数据
      if (row.sendType === '02' && row.groupId) {
        // 将逗号分隔的groupId转换为数组
        const groupIds = row.groupId.split(',');
        this.$set(this.table.listFormModul, 'groupIds', groupIds);
        console.log('编辑时设置的群组IDs:', groupIds);

        // 确保群组选项已加载
        if (this.groupOptions.length === 0) {
          this.loadGroupOptions();
        }
      }
    },
    // 复制
    handleCopyGetCopy(row) {
      console.log(row)
       // 先清空表单
      this.table.listFormModul = {}
      // 设置默认类型
      this.$set(this.table.listFormModul, 'sendType', row.sendType || '01')
      // 然后填充数据
      Object.assign(this.table.listFormModul, row)

      // 处理群组数据
      if (row.sendType === '02' && row.groupId) {
        // 将逗号分隔的groupId转换为数组
        const groupIds = row.groupId.split(',');
        this.$set(this.table.listFormModul, 'groupIds', groupIds);
        console.log('复制时设置的群组IDs:', groupIds);

        // 确保群组选项已加载
        if (this.groupOptions.length === 0) {
          this.loadGroupOptions();
        }
      }
      
    },
    async removeHandle(row, index) {
      await userroleremove(row.id)
      // this.$message.success('删除成功')
      this.table.data.splice(index, 1)
    },
    async editHandle() {
      console.log(this.table.listFormModul)
      // 处理表单数据
      const formData = { ...this.table.listFormModul }
      delete formData.modifiedTime
      delete formData.createdTime

      // 处理发送群组数据
      if (formData.sendType === '02' && formData.groupIds && formData.groupIds.length > 0) {
        // 从原始数据中获取选中的群组信息
        const selectedGroups = this.originalGroupData.filter(item => formData.groupIds.includes(item.id));

        // 使用原始数据中的 id 和 name 字段
        formData.groupId = selectedGroups.map(item => item.id).join(',');
        formData.groupName = selectedGroups.map(item => item.name).join(',');

        console.log('提交的群组数据:', { groupId: formData.groupId, groupName: formData.groupName });
      } else if (formData.sendType === '01') {
        // 如果是周报类型，清空群组数据
        delete formData.groupIds;
        delete formData.groupId;
        delete formData.groupName;
      }

      await userroleupdate(formData)
      // this.$message.success('修改管理员成功')
      this.table.dialogVisible = false
      this.getList()
    },
    // 复制
    async handleCopy(){
        const formData = { ...this.table.listFormModul }
        // 处理发送群组数据
        if (formData.sendType === '02' && formData.groupIds && formData.groupIds.length > 0) {
          // 从原始数据中获取选中的群组信息
          const selectedGroups = this.originalGroupData.filter(item => formData.groupIds.includes(item.id));

          // 使用原始数据中的 id 和 name 字段
          formData.groupId = selectedGroups.map(item => item.id).join(',');
          formData.groupName = selectedGroups.map(item => item.name).join(',');

          console.log('提交的群组数据:', { groupId: formData.groupId, groupName: formData.groupName });
        } else if (formData.sendType === '01') {
          // 如果是周报类型，清空群组数据
          delete formData.groupIds;
          delete formData.groupId;
          delete formData.groupName;
        }
        console.log('复制的表单数据:', formData);
        delete formData.id
        delete formData.modifiedTime
        delete formData.createdTime
        delete formData.isIndex
        delete formData.isPush
        await userroleadd(formData)
        // this.$message.success('新增管理员成功')
        this.table.dialogVisible = false
        this.getList()

    },
    async handleAdd() {
      // 处理表单数据
      const formData = { ...this.table.listFormModul }

      // 处理发送群组数据
      if (formData.sendType === '02' && formData.groupIds && formData.groupIds.length > 0) {
        // 从原始数据中获取选中的群组信息
        const selectedGroups = this.originalGroupData.filter(item => formData.groupIds.includes(item.id));

        // 使用原始数据中的 id 和 name 字段
        formData.groupId = selectedGroups.map(item => item.id).join(',');
        formData.groupName = selectedGroups.map(item => item.name).join(',');

        console.log('提交的群组数据:', { groupId: formData.groupId, groupName: formData.groupName });
      } else if (formData.sendType === '01') {
        // 如果是周报类型，清空群组数据
        delete formData.groupIds;
        delete formData.groupId;
        delete formData.groupName;
      }

      await userroleadd(formData)
      // this.$message.success('新增管理员成功')
      this.table.dialogVisible = false
      this.getList()
    },
    // 查询列表
    getList(listQuery) {
      this.table.loading = true;
      // 确保查询参数中包含分页信息
      const query = listQuery || this.table.listQuery;
      if (!query.page) query.page = 1;
      if (!query.size) query.size = 10;

      userrolelist(query).then((res) => {
        this.table.loading = false;

        // 根据返回的数据结构调整
        if (res.data && res.data.content) {
          // 实际数据在 data.content 中
          this.table.data = res.data.content;
          // 总数在 data.totalElements 中
          this.table.total = res.data.totalElements;

          // 如果后端返回的页码与前端不一致，同步页码
          if (res.data.number !== undefined && res.data.number !== query.page - 1) {
            this.table.listQuery.page = res.data.number + 1; // 后端页码从0开始，前端从1开始
          }

          // 如果后端返回的每页大小与前端不一致，同步每页大小
          if (res.data.size && res.data.size !== query.size) {
            this.table.listQuery.size = res.data.size;
          }
        } else {
          // 兼容旧的数据结构
          this.table.data = res.data || [];
          this.table.total = res.total || (res.data ? res.data.length : 0);
        }
      }).catch(() => {
        this.table.loading = false;
      });
    },
    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    },

    // 处理推送记录按钮点击
    handlePushRecords(obj) {
      this.currentDistributeId = obj.row.id;
      this.pushRecordsQuery.page = 1;
      this.pushRecordsQuery.size = 10;
      this.pushRecordsDialogVisible = true;
      this.getPushRecordsList();
    },

    // 获取推送记录列表
    getPushRecordsList() {
      if (!this.currentDistributeId) return;

      this.pushRecordsLoading = true;
      getPushRecords(this.currentDistributeId, this.pushRecordsQuery.page - 1, this.pushRecordsQuery.size)
        .then(res => {
          this.pushRecordsLoading = false;
          if (res.data && res.data.content) {
            this.pushRecordsData = res.data.content;
            this.pushRecordsTotal = res.data.totalElements || 0;
          } else {
            this.pushRecordsData = [];
            this.pushRecordsTotal = 0;
          }
        })
        .catch(error => {
          this.pushRecordsLoading = false;
          console.error('获取推送记录失败:', error);
          this.$message.error('获取推送记录失败');
        });
    },

    // 处理推送记录分页大小变化
    handlePushRecordsSizeChange(size) {
      this.pushRecordsQuery.size = size;
      this.getPushRecordsList();
    },

    // 处理推送记录页码变化
    handlePushRecordsCurrentChange(page) {
      this.pushRecordsQuery.page = page;
      this.getPushRecordsList();
    },

    // 加载群组数据
    async loadGroupOptions() {
      try {
        const res = await queryDictByType('groupType');
        if (res && res.data) {
          // 保存原始数据，以便后续处理
          this.originalGroupData = res.data;

          // 映射为复选框需要的格式
          // 注意：复选框组件可能需要 name 和 value 字段，而不是 label 和 value
          this.groupOptions = res.data.map(item => {
            return {
              name: item.name, // 使用接口返回的 name 字段作为显示名称
              value: item.id    // 使用接口返回的 id 字段作为值
            };
          });

          // 更新表单中的选项
          if (this.table.form.formItemList[2]) {
            this.table.form.formItemList[2].options = this.groupOptions;
          }

          console.log('群组选项:', this.groupOptions);
        }
      } catch (error) {
        console.error('加载群组数据失败:', error);
      }
    },
  }
}
</script>

<style scoped>

::v-deep .inlineB .el-form-item__content {
  flex: 1;
}

.title {
  width: 99%;
  height: 30px;
  line-height: 30px;
  padding-left: 10px;
  border-left: #39aef5 4px solid;
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.titleName {
  font-size: 15px;
  color: #333;
  font-weight: 700;
}

::v-deep .table-container {
  /* 去除项目经历左右边距 */
  padding: 0;
}

.push-records-pagination {
  background-color: #fff;
  padding: 15px;
  text-align: right;
  margin-top: -10px;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

/* 确保表格和分页之间没有间隙 */
.el-dialog .el-table {
  margin-bottom: 0;
}
</style>