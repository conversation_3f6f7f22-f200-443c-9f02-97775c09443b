<template>
  <div :class="gps.location ? 'w99' : 'p10'">
    <div class="pageInfo" v-if="!gps.action">
			<span v-show="nextBtn" class="btn nextBtn" @click="handleNextBtn()"><svg-icon icon-class="random"></svg-icon>
				<font>提交</font>
			</span>
      <!--      <span v-show="gps.location == 'handled_by'" class="btn nextBtn" @click="showUserDialog = true"><svg-icon icon-class="random"></svg-icon>-->
      <!--				<font>协办</font>-->
      <!--			</span>-->
      <!--      <span v-show="processImg" class="btn processImg" @click="handleProcessImg()"><svg-icon icon-class="liuchengtu"></svg-icon>-->
      <!--				<font>流程图</font>-->
      <!--			</span>-->
      <span class="btn optClose" @click="handleOptClose()"><svg-icon icon-class="close"></svg-icon>
				<font>关闭</font>
			</span>
    </div>

    <!-- 业务表单 -->
    <div class="message tableForm">
      <div class="orderTitle" style="">豫起奋发 更加出彩</div>
      <sb-el-form ref="appForm" :form="appForm" v-model="appFormValue" :disabled="appForm.formDisabled"
                  :on-ok="handleDoFun">
        <template v-slot:targetIntor>
          <div class="plf15 f12">
            <b>{{ appFormValue.targetIntor }}</b>
            <span class="ml10">{{ appFormValue.targetInfo }}</span>
          </div>
        </template>
        <template v-slot:taskInfo>
          <span class="f12 lh20" style="padding: 6px 15px;">{{ appFormValue.taskInfo }}</span>
        </template>
      </sb-el-form>
      <div class="m-title">
        <span>任务负责人填报反馈</span>
      </div>
      <sb-el-form ref="changeForm" :form="changeForm" v-model="changeFormValue" :disabled="changeForm.formDisabled"
                  :on-ok="handleDoFun"
                  @uploadFileList="uploadFileList">
        <template v-slot:progress>
          <div class="flex column w100 f-1">
            <el-input type="textarea" v-model="changeFormValue.progress" placeholder="请输入进展情况"
                      @blur="inputBlurFun"></el-input>
            <div class="f-2 flex a-c j-s">
              <span>请输入 <i class="red">20</i> - <i class="red"> 80</i> 字，您已输入 <i
                  class="red">{{ changeFormValue.progress?.length || 0 }}</i> 字。</span>
              <div class="flex">
                <el-button @click="recOpen" class="m-btn" size="mini" type="primary">开启录音权限</el-button>
                <el-button v-if="!canAudio" @click="recStart" class="m-btn" size="mini" type="primary">开始录音</el-button>
                <el-button v-else @click="recStop" class="m-btn" size="mini" type="primary">结束录音</el-button>
              </div>
            </div>
          </div>
        </template>
        <template v-slot:branchCompanySituation>
          <div class="flex column w100 f-1">
            <el-input type="textarea" v-model="changeFormValue.branchCompanySituation"
                      placeholder="请输入地市分公司情况" @blur="inputBlurFun"></el-input>
              <span class="f-2">请输入 <i class="red">0</i> - <i class="red"> 40</i> 字，您已输入 <i
                  class="red">{{ changeFormValue.branchCompanySituation?.length || 0 }}</i> 字。</span>
          </div>
        </template>
        <template v-slot:completionRate>
          <el-input type="text" v-model="changeFormValue.completionRate" placeholder="请输入正整数" maxlength="3"
                    @input="numberCheck" @blur="inputBlurFun">
            <template slot="append">%</template>
          </el-input>
        </template>
      </sb-el-form>
    </div>

    <!-- 按钮 -->
<!--    <div> -->
<!--      <button @click="recOpen">打开录音,请求权限</button>-->
<!--      |-->
<!--      <button @click="recStart">开始录音</button>-->
<!--      <button @click="recStop">结束录音</button>-->
<!--      |-->
<!--      <button @click="recPlay">本地试听</button>-->
<!--    </div>-->
<!--    <div style="padding-top:5px"> &lt;!&ndash; 波形绘制区域 &ndash;&gt;-->
<!--      <div style="border:1px solid #ccc;display:inline-block;vertical-align:bottom">-->
<!--        <div style="height:100px;width:300px;" ref="recwave"></div>-->
<!--      </div>-->
<!--    </div>-->

    <!-- 流程图 -->
    <el-dialog title="流程图" :visible.sync="diagramD" v-dialogDrag :close-on-click-modal="false" append-to-body
               width="1000px">
      <process-diagram :key="diagramKey" :gps="gps"></process-diagram>
    </el-dialog>

    <!-- 选择人员、部门 -->
    <el-dialog title="选择人员" :visible.sync="showUserDialog" v-dialogDrag :close-on-click-modal="false" append-to-body
               top="10vh" width="50%">
      <chooseUser v-if="showUserDialog" pmInsType="B" :item="itemUser" :key="itemUser.key"
                  @closeshowDialogFun="showUserDialog=false" :cd="cd"
                  @flowdata="flowdata"/>
    </el-dialog>

    <!-- AI润色 -->
    <el-dialog title="您的反馈内容润色后如下，请确认内容无误后提交。" :visible.sync="AIDialog" v-dialogDrag
               :close-on-click-modal="false" append-to-body top="10vh" width="50%">
      <el-input type="textarea" v-model="AIContent" class="ai-area" show-word-limit maxlength="80"></el-input>
      <span slot="footer" class="dialog-footer">
				<el-button type="primary" @click="handleConfirm()" size="small">确定</el-button>
				<el-button type="primary" @click="AIHandle()" size="small">重新润色</el-button>
				<el-button @click="AIDialog = false" size="small">关闭</el-button>
			</span>
    </el-dialog>
  </div>
</template>
<script>
import {
  uploadProcessFiles,
} from "@/api/public";
import {
  cuiban,
  deleteDraft, getAIContent, mzlRequest, saveFeedInfo, toNextProcessFeed
} from "@/api/process";
import {
  saveDraft,
  startProcess, getFeedFormDetail
} from "@/api/apply/application";

let defaultAppFormValue = {
  pmInsId: "",
  id: "",
  blank: "blank",
  applyNumber: '',
  title: ''
};

import ProcessNext from '@/components/Process/ProcessNext.vue'
import ProcessDiagram from "@/components/Process/ProcessDiagram";
import chooseUser from "@/components/chooseUser";

//必须引入的核心
import Recorder from 'recorder-core'

//引入mp3格式支持文件；如果需要多个格式支持，把这些格式的编码引擎js文件放到后面统统引入进来即可
// import 'recorder-core/src/engine/mp3'
// import 'recorder-core/src/engine/mp3-engine'
//录制wav格式的用这一句就行
import 'recorder-core/src/engine/wav'

//可选的插件支持项，这个是波形可视化插件
// import 'recorder-core/src/extensions/waveview'


export default {
  name: "application",
  props: {
    href: {
      type: Object,
      default() {
        return {};
      }
    },
    showBtn: {
      type: Object,
      // default(){
      // 	return {};
      // }
    },
    // 流程跟踪
    doFlowTrack: {
      type: Function
    },
    // 查看意见
    doViewComments: {
      type: Function
    },
    // 关闭
    dialogClose: {
      type: Function
    },
    types: {
      type: String,
      default: ''
    },
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  components: {
    ProcessNext,
    ProcessDiagram,
    chooseUser
  },
  data() {
    return {
      gps: this.href,
      pmInsId: '',
      orderId: '', // 保存草稿 重置功能用

      processD: false,
      pnKey: 0,
      clickFlag: true, //防止多次点击

      nowTime: this.util.getNow("yyyy-MM-dd hh:mm:ss"),

      showUserDialog: false,
      itemUser: {key: 0, nodeKey: 'id', mulitple: true, type: '3'}, // type 1 责任人 2 责任主管 3 经办人
      cd: [],

      // 业务表单
      initValue: {},
      appFormValue: Object.assign({}, defaultAppFormValue),
      appForm: {
        formDisabled: true,
        labelWidth: "200px",
        inline: true,
        formItemList: [
          {class: "c8", label: "工单标题", key: "title", type: "input"},
          {class: "c4", label: "工单编号", key: "applyNumber", type: "input"},
          {class: "c4", label: "责任部门", key: "taskHostUnitName", type: "input"},
          {class: "c4", label: "部门接口人", key: "interfaceAdminName", type: "input"},
          {class: "c4", label: "时间要求", key: "finishDataStr", type: "input"},
          {class: "c12", label: "目标", key: "targetIntor", type: "template", template: 'targetIntor'},
          {class: "c12", label: "举措", key: "actionInfo", type: "input"},
          {class: "c12", label: "任务", key: "taskInfo", type: "template", template: 'taskInfo'}
        ],
      },

      formData: {},

      // 流程图
      diagramKey: 0,
      diagramD: false,

      changeFormValue: Object.assign({}),
      changeForm: {
        formDisabled: false,
        labelWidth: "200px",
        inline: true,
        formItemList: [
          {
            class: "c4",
            label: "任务负责人",
            key: "feedbackPersonTrueName",
            type: "input",
            rule: {required: true},
            disabled: true
          },
          {
            class: "c4",
            label: "是否存在延期风险",
            key: "isExtension",
            type: "select",
            rule: {required: true},
            options: [{label: '是', value: '是'}, {label: '否', value: '否'}],
            changeFun: 'inputBlurFun'
          },
          {
            class: "c4",
            label: "完成率（指标）",
            key: "completionRate",
            type: "template",
            template: 'completionRate',
            rule: {required: true}
          },
          {
            class: "c12",
            label: "进展情况",
            key: "progress",
            type: "template",
            template: "progress",
            rule: {required: true}
          },
          // {class: "c12", label: "地市分公司情况", key: "branchCompanySituation", type: "template", template: "branchCompanySituation", rule: {required: true}},
          {
            class: "c12",
            label: "附件",
            key: "sysFiles",
            type: "sbUpload",
            btnText: "",
            fun: "uploadFileList",
            listType: "text",
            multiple: true,
            rule: {required: false}
          }
        ],
      },

      AIDialog: false,
      AIContent: '',

      wave: null,
      canAudio: false
    }
  },
  computed: {
    processImg() {
      return !this.gps.modify && (this.gps.type == "task" || this.gps.type == "join" || this.gps.type == "toRead" || this.gps.type == "doRead")
    },
    nextBtn() {
      return (!this.gps.modify && (this.gps.type == "task" || this.gps.type == "toRead"))
    },
  },
  created() {
    var query = this.util.getQueryString();
    this.gps = Object.assign(this.gps, query);
    // console.log('gps', JSON.parse(JSON.stringify(this.gps)));

    this.initValue = {
      applyUser: this.$store.getters.user.truename,
      applyUserName: this.$store.getters.user.username,
      belongCompanyName: this.$store.getters.user.belongCompanyName,
      belongDepartmentName: this.$store.getters.user.belongDepartmentName,
      applyPhone: this.$store.getters.user.preferredMobile,
      applyTime: this.nowTime
    };
    this.appFormValue = Object.assign({}, defaultAppFormValue, this.initValue);

    this.initFun(); //初始化
  },
  methods: {
    recOpen() {
      //创建录音对象
      this.rec = Recorder({
        type: "wav", //录音格式，可以换成wav等其他格式
        sampleRate: 16000, //录音的采样率，越大细节越丰富越细腻
        bitRate: 16, //录音的比特率，越大音质越好
        onProcess: (buffers, powerLevel, bufferDuration, bufferSampleRate, newBufferIdx, asyncEnd) => {
          //录音实时回调，大约1秒调用12次本回调
          //可实时绘制波形，实时上传（发送）数据
          if (this.wave) this.wave.input(buffers[buffers.length - 1], powerLevel, bufferSampleRate);
        }
      });

      //打开录音，获得权限
      this.rec.open(() => {
        this.$message.success("录音已打开")
        if (this.$refs.recwave) {//创建音频可视化图形绘制对象
          this.wave = Recorder.WaveView({elem: this.$refs.recwave});
        }
      }, (msg, isUserNotAllow) => {
        //用户拒绝了录音权限，或者浏览器不支持录音
        this.$message.error((isUserNotAllow ? "UserNotAllow，" : "") + "无法录音:" + msg)
      });
    },
    recStart() {
      if (!this.rec) {
        this.$message.error("未打开录音");
        return
      }
      this.rec.start();
      this.canAudio = true
      this.$message.success("已开始录音");
    },
    recStop() {
      if (!this.rec) {
        this.$message.error("未打开录音");
        return
      }
      this.rec.stop((blob, duration) => {
        //blob就是我们要的录音文件对象，可以上传，或者本地播放
        this.recBlob = blob;
        //简单利用URL生成本地文件地址，此地址只能本地使用，比如赋值给audio.src进行播放，赋值给a.href然后a.click()进行下载（a需提供download="xxx.mp3"属性）
        var localUrl = (window.URL || webkitURL).createObjectURL(blob);
        // console.log("录音成功", blob, localUrl, "时长:" + duration + "ms");
        this.$message.success('录音成功')
        this.upload(blob);//把blob文件上传到服务器
        // this.rec.close();//关闭录音，释放录音资源，当然可以不释放，后面可以连续调用start
        this.canAudio = false
        // this.rec = null;
      }, (err) => {
        this.$message.error("结束录音出错：" + err);
        this.rec.close();//关闭录音，释放录音资源，当然可以不释放，后面可以连续调用start
        this.canAudio = false
        this.rec = null;
      });
    },
    async upload(blob) {
      let data = new FormData()
      data.append('file', blob)
      let res = await mzlRequest({
        url: '/action/writing/info',
        contentType: 'multipart/form-data',
        data,
        loading: true
      })
      await this.audioToContent(res.data.uuid)
    },
    async audioToContent(uuid) {
      const loading = this.$loading({
        lock: true,
        text: '文字生成中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      let res = await mzlRequest({
        url: '/action/writing/queryExt?uuid=' + uuid,
      })
      this.changeFormValue.progress = res.data.content
      if (res.data.state == 0 || res.data.state == 2) {
        setTimeout(async () => {
          await this.audioToContent(uuid)
        }, 500)
      } else if(res.data.state == 1) {
        loading.close()
      }
    },
    recPlay() {
      //本地播放录音试听，可以直接用URL把blob转换成本地播放地址，用audio进行播放
      var localUrl = URL.createObjectURL(this.recBlob);
      var audio = document.createElement("audio");
      audio.controls = true;
      document.body.appendChild(audio);
      audio.src = localUrl;
      audio.play(); //这样就能播放了

      //注意不用了时需要revokeObjectURL，否则霸占内存
      setTimeout(function () {
        URL.revokeObjectURL(audio.src)
      }, 5000);
    },
    async inputBlurFun(data) {
      await saveFeedInfo(this.changeFormValue)
    },
    numberCheck(val) {
      setTimeout(() => {
        this.changeFormValue['completionRate'] = this.util.checkInput(val)
      }, 0)
    },
    async flowdata(arr) {
      let username
      if (arr.length == 1) {
        username = arr[0].id
      } else {
        username = arr.map(item => item.id)?.join(',')
      }

      this.formData = JSON.parse(JSON.stringify(this.appFormValue))
      this.nextCallBack(username, this.formData)
    },
    // 流程图
    handleProcessImg() {
      this.diagramKey++;
      this.diagramD = true;
    },
    uploadFileList(obj) {
      uploadProcessFiles(obj.formData).then(async res => {
        obj.content.onSuccess(res, obj.content.file, []);
        await saveFeedInfo(this.changeFormValue)
      }).catch(error => {
        obj.content.onError();
      });
    },
    // 初始化
    async initFun() {
      this.loadForm();
    },
    // 获取工单详情
    loadForm() {
      var data = {
        pmInsId: this.gps.pmInsId,
        location: this.gps.location
      };
      getFeedFormDetail(data).then((res) => {
        Object.assign(this.appFormValue, res.data)
        if (res.data.dataCollectionInfos) {
          Object.assign(this.changeFormValue, res.data.dataCollectionInfos)
        }
        if (this.gps.type == 'join' || this.appFormValue.type == 1) {
          this.changeForm.formDisabled = true
          //单独处理上传附件
          for (var i = 0; i < this.changeForm.formItemList.length; i++) {
            if (this.changeForm.formItemList[i].type == "sbUpload") {
              this.changeForm.formItemList[i].disabled = true;
            }
          }
        }
        if (this.gps.type == 'task' && this.appFormValue.type == 1) {
          this.$alert('您的待办事项填报时间已截止，暂无法提交。', '提示', {
            confirmButtonText: '我知道了'
          });
        }
      });
    },
    //封装验证函数
    submitForm(formUser) {
      return new Promise((resolve, reject) => {
        if (this.$refs[formUser]) {
          this.$refs[formUser].$children[0].validate((valid) => {
            if (valid) {
              resolve()
            } else {
              reject(new Error('错误'))
            }
          })
        } else {
          resolve()
        }
      })
    },
    // 流转下一步
    handleNextBtn() {
      // 超时了
      if (this.appFormValue.type == 1) {
        this.$alert('您的待办事项填报时间已截止，暂无法提交。', '提示', {
          confirmButtonText: '我知道了'
        });
        return;
      }

      Promise.all([this.submitForm('changeForm')])
          .then(async () => {
            if (this.changeFormValue.progress?.length > 80 || this.changeFormValue.progress?.length < 20) {
              this.$message.warning('表单数据校验不通过')
              return
            }

            // 原来的逻辑
            // this.$confirm('请确认是否已经过领导审核？', '提示', {
            //   cancelButtonText: '关闭'
            // }).then(async () => {
            //   await toNextProcessFeed(params, formData)
            //   this.afterClick()
            // }).catch(async () => {
            //   await saveFeedInfo(this.changeFormValue)
            // })

            await this.AIHandle()
            this.AIDialog = true
          })
          .catch(() => {
            this.$message.warning('表单数据校验不通过')
          })
    },
    async AIHandle() {
      const loading = this.$loading({
        lock: true,
        text: 'AI润色中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      let formData = {
        id: this.appFormValue.id,
        dataCollectionInfos: this.changeFormValue
      }
      let params = {location: this.gps.location, pmInsId: this.gps.pmInsId, taskId: this.gps.taskId}

      let res = await getAIContent(params, formData)
      loading.close()
      this.AIContent = res.data.anser
    },
    afterClick() {
      if (this.gps.myFrom && this.$router.currentRoute.path == "/workOrder") { //单点
        if(this.gps.flushPortalUrl){
            // 集团单点流转
            var flushPortalUrl = decodeURIComponent(this.gps.flushPortalUrl);
            var params = {
                "appcode": this.gps.appcode,
                "uniqueId": this.gps.uniqueId,
                "itemId": this.gps.itemId,
            }
            var pageUrlNew = this.util.toUrl(flushPortalUrl,params);
            window.location.replace(pageUrlNew)
          }else{
             window.opener = null;
            window.open("", "_self");
            window.close();

          }
      } else {
        if (!this.gps.location) {
          this.$router.push({
            name: "processTask"
          });
        } else {
          this.dialogClose();
        }
      }
    },
    // 确认
    async handleConfirm() {
      if (this.AIContent?.length > 80 || this.AIContent?.length < 20) {
        this.$message.warning('润色后内容不得大于80字或者小于20字')
        return
      }

      let formData = {
        id: this.appFormValue.id,
        dataCollectionInfos: this.changeFormValue
      }
      Object.assign(formData.dataCollectionInfos, {polishContent: this.AIContent})
      let params = {location: this.gps.location, pmInsId: this.gps.pmInsId, taskId: this.gps.taskId}

      await toNextProcessFeed(params, formData)
      this.afterClick()
      this.AIDialog = false
    },
    // 关闭
    handleOptClose() {
      if (this.dialogClose) {
        //待办打开
        this.dialogClose();
      } else if (this.gps.myFrom && this.$router.currentRoute.path == "/workOrder") {//单点
        //单点
        window.opener = null;
        window.open("", "_self");
        window.close();
      } else {
        // let item = this.tabnav.find(item => item.path === this.$route.path);
        // this.$store.dispatch("CloseTabnav", item).then(res => {
        //   if (item.path === this.$route.path) {
        //     const lastTag = res.slice(-1)[0];
        //     // 前一个 tab-view 页面存在，就跳；不存在就到首页
        //     if (lastTag) {
        //       this.$router.push({ path: lastTag.path });
        //     } else {
        //       this.$router.push({ path: "/mywork/processTask" });
        //     }
        //   }
        // });
      }
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }

};
</script>
<style scoped>
::v-deep .ai-area .el-textarea__inner {
  min-height: 200px !important;
}

.f-1 {
  position: relative;
}

.f-2 {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 99;
  font-size: 12px;
  line-height: 26px;
  background: #fff;
  width: 100%;
  padding-left: 15px;
}

::v-deep .el-input__count {
  background: rgba(247, 234, 233, 0);
}

.red {
  color: rgba(192, 0, 0, 1);
  cursor: pointer;
}

::v-deep .tableCustom1 {
  position: relative;
}

.look-more {
  position: absolute;
  right: 5px;
  top: 0px;
  z-index: 999;
  font-size: 13px;
  cursor: pointer;
}


::v-deep .el-textarea__inner {
  padding-bottom: 25px;
}

::v-deep .tableCustom .el-textarea {
  //border: 1px solid #ebebeb;
  margin: 5px 0;
}

::v-deep .tableCustom .cell {
  white-space: normal;
  overflow: visible;
  text-overflow: inherit;
}

::v-deep .el-table .cell > .inlineC > .el-button {
  background: transparent;
  border: none;
  margin: 0;
  font-size: 14px;
  color: rgba(192, 0, 0, 1);
  padding: 7px 0px;
  white-space: normal;
}

::v-deep .el-input-group__append {
  background-color: #fff3f3;
  border-color: #fff3f3;
}

::v-deep .upload_D {
  min-width: 100%;
}

::v-deep .upload_Btn .uploadB {
  right: 0;
  left: auto;
}

.m-title {
  width: 100%;
  padding-left: 10px;
  border-left: 4px solid rgba(192, 0, 0, 1);
  padding: 10px;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  border-bottom: 1px solid #ebebeb;
  border-right: 1px solid #ebebeb;
}

.m-4 {
  height: 32px;
  display: flex;
  align-items: center;
  padding: 0;
  border-right: 1px solid #ebebeb;
  border-bottom: 1px solid #ebebeb;
}

.m-4:first-child {
  border-top: 1px solid #ebebeb;
}

::v-deep .m-4 .el-input__inner {
  height: 30px;
  line-height: 30px;
  border: none;
}

::v-deep .m-4 .el-input__icon {
  line-height: 30px;
}

.m-2 {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100px;
  height: 100%;
  background: #DDF1FE;
  padding: 0 10px;
  font-size: 14px;
  color: #606266;
  font-weight: bold;
}

.m-5 {
  flex: 1;
}
</style>