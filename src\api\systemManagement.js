import request from "@/assets/js/request";
import store from "../store";
import util from "@/assets/js/public";

// 反馈台账下拉框
export function getQueryCycle(data) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/collection/queryCycle?cycle=${data.cycle || -1}`,
        contentType: "application/json;charset=UTF-8",
    });
}
// 添加周期--修改周期
export function editQueryCycle(data) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/collection/addCycle`,
        contentType: "application/json;charset=UTF-8",
        loading: true,
        data
    });
}
// 周期推送代办
export function onPushCycle(id) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/collection/pushCycle?id=${id}`,
        contentType: "application/json;charset=UTF-8",
        loading: true,
    });
}

