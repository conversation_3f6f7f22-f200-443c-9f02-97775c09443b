<template>
  <div>
     <div class="flex" style="margin: 8px 0;align-items: center;justify-content: flex-end;">
    <el-upload 
            class="upload-demo ml10"
            :action="uploadData.action"
            :on-success="uploadData.isPopup?uploadData.isDialog?handleDialog:handlePopup:handleSuccess"
            :before-upload="handleProgress"
            multiple
            :limit="uploadData.limit"
            :show-file-list="false"
            ref="upload">
            <el-button :key="uploadData.id" :size="uploadData.size || 'small'" type="primary">{{uploadData.name}}</el-button>
        </el-upload>
      <el-button type="primary" size="small" style="margin-left: 8px;" @click="handleDown()">模板下载</el-button>
      
    </div>
    <el-table
        v-loading.fullscreen.lock="tableLoading"
        element-loading-text="请稍后，正在查询..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255, 255, 255, 0.5)" class="tableCustom" :class="{'nodata': !dataList.length}" :data="dataList"
        style="width: 100%;" border :span-method="arraySpanMethod" :cell-style="{background: '#ffffff'}">
      <el-table-column prop="lineType" label="专项组" width="100" align="center">
      </el-table-column>
      <el-table-column prop="targetIntorAndInfo" label="项目名称" width="120">
        <template v-slot:default="scope">
          <!-- <span style="font-weight: bold" v-html="scope.row.targetIntor"></span>
          <br/> -->
          <span v-html="scope.row.targetInfo"></span>
        </template>
      </el-table-column>
      <el-table-column label="时间要求" width="100">
        <template v-slot:default="scope">
          <span v-html="scope.row.finishDataStr"></span>
        </template>
      </el-table-column>
      <el-table-column prop="actionInfo" label="目标" min-width="200">
        <template v-slot:default="scope">
          <span v-html="scope.row.actionInfo"></span>
        </template>
      </el-table-column>
      <el-table-column prop="taskInfo" label="任务" min-width="300">
        <template v-slot:default="scope">
          <span v-html="scope.row.taskInfo"></span>
        </template>
      </el-table-column>
       <el-table-column prop="taskJcInfo" label="举措" min-width="300">
        <template v-slot:default="scope">
          <span v-html="scope.row.taskJcInfo"></span>
        </template>
      </el-table-column>
      <el-table-column prop="companyName" label="责任部门" width="120" align="center">
      </el-table-column>
      <el-table-column prop="companyTrueName" label="部门接口人" width="90" align="center">
      </el-table-column>
      <el-table-column prop="taskInfoTrueName" label="任务负责人" width="90" align="center">
      </el-table-column>
      <!-- <el-table-column label="状态" width="100" align="center">
        <template v-slot="scope">
          {{ scope.row.checkState == 0 ? '未结项' : scope.row.checkState == 1 ? '结项中' : '已结项' }}
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="操作" width="120">
        <template v-slot="scope">
          <div class="inlineC">
            <el-button v-if="scope.row.checkState == 0 && scope.row.feedbackPersonTrueName == user.user.truename" size="mini" type="primary" @click.stop="conclusionHandle(scope.row, 'send')">【发起结项】
            </el-button>
            <el-button v-if="scope.row.checkState != 0" size="mini" type="primary" @click.stop="conclusionHandle(scope.row, 'look')">【查看】</el-button>
          </div>
        </template>
      </el-table-column> -->
    </el-table>
    <!-- <sb-el-table
      :table="table"
      @getList="getList"
      :on-ok="handleDoFun"
      @handleDown="handleDown"
      @getUpload="getUpload"
      :span-method="arraySpanMethod"
    >
      <template v-slot:BUSINESS_TITLE="{obj}">
        <span class="toDetail" @click="handleTodo(obj)">{{obj.row.BUSINESS_TITLE}}</span>
      </template>
       <template v-slot:isEffective="{obj}">
        <span v-if="obj.row.isEffective == 0">无效</span>
        <span v-if="obj.row.isEffective == 1">有效</span>
      </template>
       <template v-slot:CREATED_TIME="{obj}">
        <span>{{util.getTimeDate(obj.row.CREATED_TIME,"yyyy-MM-dd HH:mm:ss")}}</span>
      </template>
    </sb-el-table> -->
     <div style="padding:20px 0; margin-left:40%">
               <el-button  @click="handleUp('no')" type="primary" size="small">关闭</el-button>
               <el-button type="primary" @click="handleUp('yes')" size="small">确认</el-button>

          </div>
  </div>
</template>
<script>
import { geRecordtQueryPage,downloadTemplate } from "@/api/branchCompany/index";
export default {
   props: {
    mainId: { type: String, }
   },
  data(){
    return {
      dataList:[],
      spanArr: [],//二维数组，用于存放单元格合并规则
      position: 0,//用于存储相同项的开始index
      tableLoading: false,
        uploadData: {
          id: "b",
          action: `/${process.env.VUE_APP_APPCODE}/action/branch/subInfo/importExcel?source=PC`,
          name: "导入",
          isPopup: false,
          isDialog: false,
        },
      table: {
        modulName: "customer-客户管理", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: false, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "120px",
          formItemList: [
            // {class: "c4",label: "流程状态",key: "currentState",type: "select"},
          ],
        },
        tr: [
          {id: "lineType",label: "条线",prop: "lineType",width: 60},
          {id: "targetIntorAndInfo",label: "目标",prop: "targetIntorAndInfo", width:200},
          {id: "finishDataStr",label: "时间要求",prop: "finishDataStr",width: 120},
          {id: "typeTwo",label: "行业子链",prop: "typeTwo",width: 120},
          {id: "proTrueName",label: "省级行业客户经理姓名",prop: "proTrueName",width: 200},
          {id: "proUserName",label: "省级行业客户经理账号",prop: "proUserName",width: 200},
          {id: "proTel",label: "省级行业客户经理联系方式",prop: "proTel",width: 200},
          {id: "CREATED_TIME",label: "更新时间",prop: "CREATED_TIME",width: 200, show: "template" },
          {id: "isEffective",label: "状态",prop: "isEffective",width: 200,show:'template'  },

        ],
        // hasSetup:true,
				// setup:[],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "400px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: false, //是否有操作列表
        operation: {
          width: "200",
          fixed: "right",
          data: [
            

          ],
        },
        hasUploadData: true,
        uploadData: {
          id: "b",
          action: `/${process.env.VUE_APP_APPCODE}/action/branch/subInfo/importExcel?source=PC`,
          name: "导入",
          isPopup: false,
          isDialog: false,
        },
        hasOtherQueryBtn: true, //是否有其他操作
        otherQueryBtn: {
          data: [
            {id: "down",type: "primary",name: "模板下载",fun: "handleDown"},

           

          ]
        },
        hasPagination: true,
        listQuery: {size: 10,page: 1},
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
      dataListData:[]

    }
  },
  created() {
    // this.getList(this.mainId)
    this.dataList = []

  },
  methods:{
     handleProgress(){
        this.tableLoading = true
    },
    handleSuccess(response,file) {
      console.log(response)
      if(response.data && response.status==200){
        this.$message({
            type:'success',
            message:'导入成功'
        })
        console.log(response.data)
        this.getSetList(response)
           
      }else{
         this.tableLoading = false;
         this.$message.error(response.message);
      }
    },
    rowspan(idx, prop) {
      this.spanArr[idx] = [];
      this.position = 0;
      this.dataList.forEach((item, index) => {
        if (index === 0) {
          this.spanArr[idx].push(1);
          this.position = 0;
        } else {
          if (this.dataList[index][prop] === this.dataList[index - 1][prop]) {
            this.spanArr[idx][this.position] += 1;//有相同项
            this.spanArr[idx].push(0); // 名称相同后往数组里面加一项0
          } else {
            this.spanArr[idx].push(1);//同列的前后两行单元格不相同
            this.position = index;
          }
        }
      })
    },
     async getSetList(list) {
      this.tableLoading = true
      this.dataListData = list.data;
      this.dataList = list.data.map(item => {
        item.targetIntor = this.util.htmlDecode(item.targetIntor)
        item.targetInfo = this.util.htmlDecode(item.targetInfo)
        item.actionInfo = this.util.htmlDecode(item.actionInfo)
        item.taskInfo = this.util.htmlDecode(item.taskInfo)
        item.taskJcInfo = this.util.htmlDecode(item.taskJcInfo)
        return {
          intorAndInfoAndDate: item.targetIntor + item.targetInfo + item.finishDataStr,
          targetIntorAndInfo: item.targetIntor + item.targetInfo,
          ...item
        }
      });

      this.rowspan(0, 'lineType');
      this.rowspan(1, 'targetIntorAndInfo');
      this.rowspan(2, 'intorAndInfoAndDate');
      this.rowspan(3, 'actionInfo');
      this.rowspan(4, 'taskInfo');
      this.rowspan(5, 'taskJcInfo');
      this.rowspan(6, 'companyName')
      this.rowspan(7, 'companyTrueName')
      this.tableLoading = false
    },
     // 合并行
    arraySpanMethod({row, column, rowIndex, columnIndex}) {
      for (let i = 0; i < 8; i++) {
        if (columnIndex === i) {
          const _row = this.spanArr[i][rowIndex];
          const _col = _row > 0 ? 1 : 0;
          // console.log('第'+rowIndex+'行','第'+i+'列','rowspan:'+_row,'colspan:'+_col)
          return {
            rowspan: _row,
            colspan: _col
          }
        }
      }
    },
    getUpload(res){
      console.log(res)

    },
    handleDown(){
      downloadTemplate()
        .then((res) => {
          this.util.blobDownload(res.data, res.filename);
        })
        .catch((error) => {});

    },
     handleUp(op) {
          if (op == "yes") {
            if(this.dataListData.length == 0){
              this.$message.warning('请导入数据')
            }else{
              this.$emit('event', this.dataListData);
              this.$emit("closeshowDialog");
              this.dataList = []

            }
              
          } else {
              this.dataList = []
              this.$emit("closeshowDialog");
          }
      },
    getList(id,listQuery){
      this.table.loading = true;
      geRecordtQueryPage({
        mainId:id,
        size:this.table.listQuery.size,
        page:this.table.listQuery.page
      }).then((res) => {
          this.table.loading = false;
          this.table.data = res.data.content;
          this.table.total = res.data.totalElements;
        }).catch((err) => {
          this.table.loading = false;
        });


    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }
}
</script>