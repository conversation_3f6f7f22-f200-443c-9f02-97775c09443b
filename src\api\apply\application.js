import request from "@/assets/js/request";

// 获取流程相关数据
export function getProcessInfo(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/apply/getProcessInfo`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}

// 获取派发工单详情
export function getFormDetail(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/application/getDisDetailForm?source=PC&pmInsId=${params.pmInsId}&location=${params.location}&type=${params.type}&taskId=${params.taskId}`,
        contentType: "application/json; charset=utf-8",
    });
}

// 获取派发工单详情
export function getFeedFormDetail(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/collection/getDetailForm?source=PC&pmInsId=${params.pmInsId}&location=${params.location}`,
        contentType: "application/json; charset=utf-8",
    });
}

// 保存草稿
export function saveDraft(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/saveDraft`,
        contentType: "application/json; charset=utf-8",
        loading: true,
        data: params
    });
}

// 流转
export function startProcess(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/startProcessAndTaskSubmitCommon`,
        contentType: "application/json; charset=utf-8",
        loading: true,
        data: params
    });
}
// 查询三个模块
// 工单查询
export function getWorkQuery(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/getWorkQuery?source=PC&page=${params.page}&size=${params.size}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 时长查询
export function getWorkTime(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/applicationForm/callExpertDurationPage?source=PC&page=${params.page}&size=${params.size}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 时长查看详情查询列表
export function getdetailWorkTime(pageData) {
    console.log(pageData);
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/applicationForm/callExpertDurationDetailPage?source=PC&page=${pageData.page}&size=${pageData.size}`,
        contentType: "application/json; charset=utf-8",
        data: pageData
    });
}
// 专家调用分页查询
export function getExpert(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/applicationForm/callExpertPage?source=PC&page=${params.page}&size=${params.size}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}

// 导出
export function exportParameter(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/exportMCExcel`,
        contentType: "application/json; charset=utf-8",
        data: params,
        responseType: "blob"
    });
}

// 废除归档
export function deleteProcess(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/deleteProcess?processInstId=${params.processInstId}&pmInsId=${params.pmInsId}`,
        contentType: "application/json; charset=utf-8",
    });
}

// 终止
export function stopProcess(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/stopProcess?processInstId=${params.processInstId}&pmInsId=${params.pmInsId}`,
        contentType: "application/json; charset=utf-8",
    });
}

/**
 * 查询远程数据列表
 */
 export function getDataByApi(dialogData,params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/getDataByApi?apiType=` + dialogData.apiType + "&url=" + dialogData.url,
        contentType: 'application/json;charset=UTF-8',
        data:params
    })
}

// 表单初始化数据-工单编号
export function initForm(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/rule/createOrderNo`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}

// 获取AI周报详情
export function AiDetail(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/distribute/distributeDetail?source=PC&pmInsId=${params.pmInsId}&taskId=${params.taskId}&location=${params.location}`,
        contentType: "application/json; charset=utf-8",
    });
}

// AI周报流转
export function AiNext(params, data) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/distribute/handle?source=PC&pmInsId=${params.pmInsId}&taskId=${params.taskId}&location=${params.location}`,
        contentType: "application/json; charset=utf-8",
        data
    });
}

// 荣誉
export function selectAll(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/UsDeptHonor/selectAll?source=PC&page=${params.page}&size=${params.size}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 荣誉添加
export function UsDeptHonorAdd(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/UsDeptHonor/add?source=PC`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 荣誉编辑
export function UsDeptHonorEdit(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/UsDeptHonor/edit?source=PC`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 荣誉删除
export function UsDeptHonorDelete(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/UsDeptHonor/deleteNew?source=PC&id=${params.id}`,
        contentType: "application/json; charset=utf-8",
    });
}
// 任务进度
export function selectAllNoPage(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/UsTaskProgress/selectAllNoPage?source=PC`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 任务编辑
export function batchEdit(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/UsTaskProgress/batchEdit?source=PC`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
