<template>
  <div :class="gps.location ? 'w99' : 'p10'" style="padding-bottom: 10px">
    <div class="pageInfo" v-if="!gps.action">
			<span v-show="nextBtn" class="btn nextBtn" @click="handleNextBtn()"><svg-icon icon-class="random"></svg-icon>
				<font>{{ (gps.location == 'person_read' || gps.location == 'assist_person_read') ? '确认' : '派发' }}</font>
			</span>
      <span class="btn flowTrack" @click="handleFlowTrack()">
        <svg-icon icon-class="liuchengtu"></svg-icon>
        <font>流程跟踪</font>
      </span>
      <span v-show="processImg" class="btn processImg" @click="handleProcessImg()"><svg-icon icon-class="liuchengtu"></svg-icon>
				<font>流程图</font>
			</span>
      <span class="btn optClose" @click="handleOptClose()"><svg-icon icon-class="close"></svg-icon>
				<font>关闭</font>
			</span>
      <span class="btn optClose" @click="handleExpert()"><svg-icon icon-class="baocun"></svg-icon>
				<font>导出</font>
			</span>
    </div>

    <!-- 业务表单 -->
    <div class="message tableForm">
      <div class="orderTitle" style="">豫起奋发 更加出彩</div>
      <sb-el-form ref="appForm" :form="appForm" v-model="appFormValue" :disabled="appForm.formDisabled" :on-ok="handleDoFun">
        <template #interfaceAdminCode="{obj}">
          <span class="red-box" v-if="showBoss && gps.type == 'task'"
                @click="selectUser(appFormValue, 4, 999)">【{{ !appFormValue.interfaceAdminName ? '请选择' : appFormValue.interfaceAdminName }}】</span>
          <span class="red-box" v-else>【{{ appFormValue.interfaceAdminName }}】</span>
        </template>
      </sb-el-form>
      <el-table class="tableCustom" :data="dataList" style="width: 100%;" border :span-method="arraySpanMethod" :cell-style="{background: '#ffffff'}"
      >
        <!--        <el-table-column label="序号" type="index" align="center">-->
        <!--        </el-table-column>-->
        <!--        <el-table-column prop="lineType" label="条线" width="60">-->
        <!--        </el-table-column>-->
        <el-table-column prop="targetIntorAndInfo" label="目标" width="140">
          <template v-slot:default="scope">
            <span style="font-weight: bold" v-html="scope.row.targetIntor"></span>
            <br/>
            <span v-html="scope.row.targetInfo"></span>
          </template>
        </el-table-column>
        <el-table-column prop="intorAndInfoAndDate" label="时间要求" width="110">
          <template v-slot:default="scope">
            <span v-html="scope.row.finishDataStr"></span>
          </template>
        </el-table-column>
        <el-table-column prop="actionInfo" label="举措" width="140">
          <template v-slot:default="scope">
            <span v-html="scope.row.actionInfo"></span>
          </template>
        </el-table-column>
        <el-table-column prop="taskInfo" label="任务">
          <template v-slot:default="scope">
            <div v-if="scope.row.isEdit">
              <el-input :ref="'textareaRef'+scope.$index" type="textarea" placeholder="请输入任务内容" v-model="scope.row.taskInfo"
                        :autosize="{ minRows: 2, maxRows: 999}" @blur="taskConfirm(scope.row)"></el-input>
            </div>
            <span v-html="scope.row.taskInfo" v-else></span>
          </template>
        </el-table-column>
        <el-table-column label="责任部门" width="120">
          <template v-slot:default="scope">
            <span v-if="scope.row.hostUnitCode == scope.row.taskHostUnitCode">{{ scope.row.hostUnitName }}</span>
            <span v-else>{{ scope.row.taskHostUnitName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="beforeTrueNames" label="上一环节办理人" width="120" align="center"
                         v-if="gps.location != 'yqff.start' && gps.location != 'assist_manager_dis'">
        </el-table-column>
        <el-table-column :label="gps.location == 'yqff.start' ? '办理人' : '责任人'" width="180" align="center" v-if="showBoss">
          <template v-slot:default="scope">
            <div class="inlineC">
              <template v-if="gps.location == 'yqff.start' && gps.type == 'task'">
                <el-button v-if="scope.row.hostUnitCode == scope.row.taskHostUnitCode" size="mini" type="primary"
                           @click="selectUser(scope.row, 1, scope.$index)">
                  【{{ !scope.row.dutyTrueName ? '请选择' : scope.row.dutyTrueName }}】
                </el-button>
                <span v-else>待{{ scope.row.managerTrueName }}确认</span>
              </template>
              <template v-else-if="gps.location == 'assist_manager_dis' && gps.type == 'task'">
                <el-button size="mini" type="primary" @click="selectUser(scope.row, 1, scope.$index)">
                  【{{ !scope.row.dutyTrueName ? '请选择' : scope.row.dutyTrueName }}】
                </el-button>
              </template>
              <template v-else>
                <span>{{ scope.row.dutyTrueName }}</span>
              </template>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="责任主管" width="200" align="center" v-if="showVice">
          <template v-slot:default="scope">
            <div class="inlineC">
              <el-button v-if="gps.type == 'task'" size="mini" type="primary" @click="selectUser(scope.row, 2, scope.$index)">
                【{{ !scope.row.dutyChargeTrueName ? '请选择' : scope.row.dutyChargeTrueName }}】
              </el-button>
              <span v-else>{{ scope.row.dutyChargeTrueName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="任务负责人" width="200" align="center" v-if="showDuty">
          <template v-slot:default="scope">
            <div class="inlineC">
              <el-button v-if="gps.type == 'task'" size="mini" type="primary" @click="selectUser(scope.row, 3, scope.$index)">
                【{{ !scope.row.operatorTrueName ? '请选择' : scope.row.operatorTrueName }}】
              </el-button>
              <span v-else>{{ scope.row.operatorTrueName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="当前环节办理人" prop="currentDealTrueNames" width="200" align="center" v-if="(showBoss || showVice || showDuty) && gps.type == 'join'">
        </el-table-column>
        <el-table-column label="操作" width="190" v-if="showBoss && gps.type == 'task'">
          <template v-slot="scope">
            <div class="inlineC" v-show="!scope.row.isEdit">
              <el-button size="mini" type="primary" @click.stop="taskHandle(scope.row, scope.$index, 'add')">【新增】</el-button>
              <el-button size="mini" type="primary" @click.stop="taskHandle(scope.row, scope.$index, 'edit')">【编辑】</el-button>
              <el-button v-show="!scope.row.addType" size="mini" type="primary" @click="taskDel(scope.row.id)">【删除】</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 流程图 -->
    <el-dialog title="流程图" :visible.sync="diagramD" v-dialogDrag :close-on-click-modal="false" append-to-body width="500px">
      <img src="@/assets/liucheng.png" class="w100"/>
    </el-dialog>

    <!-- 选择人员、部门 -->
    <el-dialog title="选择人员" :visible.sync="showUserDialog" v-dialogDrag :close-on-click-modal="false" append-to-body top="10vh" width="50%">
      <chooseUser v-if="showUserDialog" :item="itemUser" :key="itemUser.key" @closeshowDialogFun="showUserDialog=false" :cd="cd" @flowdata="flowdata"/>
    </el-dialog>

    <!-- 流程跟踪 -->
    <el-dialog title="流程跟踪" :visible.sync="trackD" v-dialogDrag :close-on-click-modal="false" append-to-body width="1200px">
      <process-track :key="trackKey" :gps="gps"></process-track>
    </el-dialog>
  </div>
</template>
<script>

import {
  deleteDraft,
  saveUser, editTask, delTask, toNextProcess, saveInterface, daochu
} from "@/api/process";
import {
  getFormDetail,
  saveDraft,
  startProcess
} from "@/api/apply/application";

let defaultAppFormValue = {
  pmInsId: "",
  id: "",
  blank: "blank",
  applyNumber: '',
  title: '',
  interfaceAdminName: '', // 部门接口人
};

import ProcessNext from '@/components/Process/ProcessNext.vue'
import ProcessDiagram from "@/components/Process/ProcessDiagram";
import chooseUser from "@/components/chooseUser";
import ProcessTrack from "@/components/Process/ProcessTrack.vue";

export default {
  name: "application",
  props: {
    href: {
      type: Object,
      default() {
        return {};
      }
    },
    showBtn: {
      type: Object,
      // default(){
      // 	return {};
      // }
    },
    // 流程跟踪
    doFlowTrack: {
      type: Function
    },
    // 查看意见
    doViewComments: {
      type: Function
    },
    // 关闭
    dialogClose: {
      type: Function
    },
    types: {
      type: String,
      default: ''
    },
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  components: {
    ProcessNext,
    ProcessDiagram,
    chooseUser,
    ProcessTrack
  },
  data() {
    return {
      gps: this.href,
      pmInsId: '',
      orderId: '', // 保存草稿 重置功能用

      trackD: false,
      trackKey: 0,

      processD: false,
      pnKey: 0,
      clickFlag: true, //防止多次点击

      nowTime: this.util.getNow("yyyy-MM-dd hh:mm:ss"),

      dataList: [],
      spanArr: [],//二维数组，用于存放单元格合并规则
      position: 0,//用于存储相同项的开始index

      showUserDialog: false,
      itemUser: {key: 0, nodeKey: 'id', mulitple: true, type: ''}, // type 1 责任人 2 责任主管 3 经办人 4 部门接口人（懒加载）
      cd: [],
      selectIndex: 0, // 选人点击后的table下标
      taskType: '',

      // 业务表单
      initValue: {},
      appFormValue: Object.assign({}, defaultAppFormValue),
      appForm: {
        formDisabled: false,
        labelWidth: "200px",
        inline: true,
        formItemList: [
          {class: "c12", label: "工单标题", key: "title", type: "input", disabled: true},
          {class: "c4", label: "工单编号", key: "applyNumber", type: "input", disabled: true},
          {class: "c8", label: "部门接口人", key: "interfaceAdminCode", type: "template", template: 'interfaceAdminCode'}
        ],
      },

      formData: {},

      // 流程图
      diagramKey: 0,
      diagramD: false,

      nextBtnDisable: false, // 流转下一步确认按钮
      interfaceClickNum: 0,
      banliPersonClickNum: 0
    }
  },
  computed: {
    processImg() {
      return !this.gps.modify && (this.gps.type == "task" || this.gps.type == "join" || this.gps.type == "toRead" || this.gps.type == "doRead")
    },
    nextBtn() {
      return (!this.gps.modify && (this.gps.type == "task" || this.gps.type == "toRead"))
    },
    // 是否显示责任人列
    showBoss() {
      return this.gps.location == 'yqff.start' || this.gps.location == 'assist_manager_dis'
    },
    // 是否显示责任主管列
    showVice() {
      return this.gps.location == 'vice_dis' || this.gps.location == 'assist_vice_dis'
    },
    // 是否显示经办人列
    showDuty() {
      return this.gps.location == 'duty_dis' || this.gps.location == 'assist_duty_dis'
    }
  },
  created() {
    var query = this.util.getQueryString();
    this.gps = Object.assign(this.gps, query);
    // console.log('gps', JSON.parse(JSON.stringify(this.gps)));

    setTimeout(() => {
      let height
      if (this.gps.myFrom && this.$router.currentRoute.path == "/workOrder") {
        height = window.innerHeight - 230
      } else {
        height = window.innerHeight - 290
      }
      let dom = document.querySelector('.tableCustom .el-table__body-wrapper')
      dom.setAttribute('style', `max-height: ${height}px;`)
    }, 0)

    this.initValue = {
      applyUser: this.$store.getters.user.truename,
      applyUserName: this.$store.getters.user.username,
      belongCompanyName: this.$store.getters.user.belongCompanyName,
      belongDepartmentName: this.$store.getters.user.belongDepartmentName,
      applyPhone: this.$store.getters.user.preferredMobile,
      applyTime: this.nowTime
    };
    this.appFormValue = Object.assign({}, defaultAppFormValue, this.initValue);

    this.initFun(); //初始化
  },
  methods: {
    // 流程跟踪
    handleFlowTrack() {
      this.trackKey++;
      this.trackD = true;
    },
    async handleExpert() {
      let res = await daochu({
        location: this.gps.location,
        pmInsId: this.gps.pmInsId,
        taskId: this.gps.taskId,
        type: this.gps.type
      })

      if (res.data) {
        this.util.blobDownload(res.data, `豫起奋发 更加出彩--${this.util.getNow()}.xls`);
      } else {
        this.$message({
          message: '导出失败',
          type: 'warning',
          duration: 1500
        });
      }
    },
    selectUser(item, type, index) {
      this.selectIndex = index
      this.itemUser.mulitple = true
      this.itemUser.type = type
      let usernameKey,
          truenameKey
      if (type == 1) {
        if (!this.banliPersonClickNum) {
          this.$alert('您可选择部门分管领导或主管，由部门分管领导或主管继续派发任务。', '提示', {
            confirmButtonText: '我知道了',
            callback: action => {
              this.banliPersonClickNum++
              this.selectUser(item, type, index)
            }
          });
          return
        }
        usernameKey = 'dutyUsername'
        truenameKey = 'dutyTrueName'
      } else if (type == 2) {
        usernameKey = 'dutyChargeUsername'
        truenameKey = 'dutyChargeTrueName'
      } else if (type == 3) {
        usernameKey = 'operatorUsernames'
        truenameKey = 'operatorTrueName'
      } else if (type == 4) {
        if (!this.interfaceClickNum) {
          this.$alert('部门接口人负责整个部门（牵头部门接口人负责整个条线）任务落实情况的统筹工作，后续需收集任务进度并填写通报。', '提示', {
            confirmButtonText: '我知道了',
            callback: action => {
              this.interfaceClickNum++
              this.selectUser(item, type, index)
            }
          });
          return
        }
        this.itemUser.mulitple = false
        usernameKey = 'interfaceAdminCode'
        truenameKey = 'interfaceAdminName'
      }
      if (item[truenameKey]) {
        let trueArr = item[truenameKey].split(',')
        let userArr = item[usernameKey].split(',')
        let arr = []
        for (let i = 0; i < trueArr.length; i++) {
          arr.push({id: userArr[i], name: trueArr[i], treeType: 'user'})
        }
        this.cd = arr
      } else this.cd = []

      this.$nextTick(() => {
        this.showUserDialog = true
      })
    },
    async taskConfirm(item) {
      if (!item.taskInfo.trim()) {
        this.loadForm()
        return
      }
      let obj = Object.assign({}, item)
      if (this.taskType == 'add') {
        delete obj.id
      }
      let res = await editTask(this.gps.location, obj)
      item.isEdit = false
      this.loadForm()
    },
    taskHandle(item, index, type) {
      this.taskType = type
      if (type == 'add') {
        let obj = Object.assign({}, item)
        obj.isEdit = true
        obj.addType = 0
        obj.taskInfo = ''
        obj.dutyTrueName = ''
        this.dataList.splice(index + 1, 0, obj)
        this.$nextTick(() => {
          this.$refs['textareaRef' + (index + 1)].resizeTextarea()
          this.$refs['textareaRef' + (index + 1)].focus()
        })
      } else {
        item.isEdit = true
        this.$nextTick(() => {
          this.$refs['textareaRef' + index].resizeTextarea()
          this.$refs['textareaRef' + index].focus()
        })
      }
      // this.rowspan(1, 'lineType');
      this.rowspan(0, 'targetIntorAndInfo');
      this.rowspan(1, 'intorAndInfoAndDate');
      this.rowspan(2, 'actionInfo');
    },
    async flowdata(arr) {
      let truename,
          username
      if (arr.length == 1) {
        truename = arr[0].name
        username = arr[0].id
      } else {
        truename = arr.map(item => item.name)?.join(',')
        username = arr.map(item => item.id)?.join(',')
      }

      // 选择部门接口人
      if (this.itemUser.type == 4) {
        this.appFormValue.interfaceAdminName = truename
        this.appFormValue.interfaceAdminCode = username
        this.$forceUpdate()
        await saveInterface({
          pmInsId: this.gps.pmInsId,
          taskId: this.gps.taskId,
          interfaceAdminName: truename,
          interfaceAdminCode: username
        })
        return;
      }

      // 主办部门正职 协办部门正职
      if (this.showBoss) {
        this.dataList[this.selectIndex].dutyUsername = username
        this.dataList[this.selectIndex].dutyTrueName = truename
      } else if (this.showVice) { // 副职选主管 协办部门副职选择主管
        this.dataList[this.selectIndex].dutyChargeUsername = username
        this.dataList[this.selectIndex].dutyChargeTrueName = truename
      } else if (this.showDuty) { // 主管选择经办人
        this.dataList[this.selectIndex].operatorUsernames = username
        this.dataList[this.selectIndex].operatorTrueName = truename
      }
      this.$forceUpdate()
      await saveUser(this.gps.location, this.dataList[this.selectIndex], this.gps.taskId)
    },
    rowspan(idx, prop) {
      this.spanArr[idx] = [];
      this.position = 0;
      this.dataList.forEach((item, index) => {
        if (index === 0) {
          this.spanArr[idx].push(1);
          this.position = 0;
        } else {
          if (this.dataList[index][prop] === this.dataList[index - 1][prop]) {
            this.spanArr[idx][this.position] += 1;//有相同项
            this.spanArr[idx].push(0); // 名称相同后往数组里面加一项0
          } else {
            this.spanArr[idx].push(1);//同列的前后两行单元格不相同
            this.position = index;
          }
        }
      })
    },
    // 合并行
    arraySpanMethod({row, column, rowIndex, columnIndex}) {
      for (let i = 0; i < 3; i++) {
        if (columnIndex === i) {
          const _row = this.spanArr[i][rowIndex];
          const _col = _row > 0 ? 1 : 0;
          // console.log('第'+rowIndex+'行','第'+i+'列','rowspan:'+_row,'colspan:'+_col)
          return {
            rowspan: _row,
            colspan: _col
          }
        }
      }
    },
    taskDel(id) {
      this.$confirm("确定要删除此任务吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        delTask(this.gps.location, id).then(res => {
          // this.$message({type: "success", message: "删除成功!"});
          this.loadForm()
        })
      }).catch(() => {
      });
    },

    // 流程图
    handleProcessImg() {
      this.diagramKey++;
      this.diagramD = true;
    },
    // 初始化
    async initFun() {
      this.loadForm();
    },
    // 获取工单详情
    loadForm() {
      var data = {
        pmInsId: this.gps.pmInsId,
        location: this.gps.location,
        type: this.gps.type,
        taskId: this.gps.taskId
      };
      getFormDetail(data).then((res) => {
        Object.assign(this.appFormValue, res.data)
        this.dataList = res.data.applicationSubInfoList.map(item => {
          item.targetIntor = this.util.htmlDecode(item.targetIntor)
          item.targetInfo = this.util.htmlDecode(item.targetInfo)
          item.actionInfo = this.util.htmlDecode(item.actionInfo)
          return {
            isEdit: false, // 是否是输入框状态
            targetIntorAndInfo: item.targetIntor + item.targetInfo,
            intorAndInfoAndDate: item.targetIntor + item.targetInfo + item.finishDataStr,
            ...item
          }
        });
        // this.rowspan(1, 'lineType');
        this.rowspan(0, 'targetIntorAndInfo');
        this.rowspan(1, 'intorAndInfoAndDate');
        this.rowspan(2, 'actionInfo');
      });
    },
    // 重置表单
    handleFormReset() {
      this.appFormValue = Object.assign({}, defaultAppFormValue, this.initValue);
      this.appFormValue.pmInsId = this.gps.pmInsId
    },

    // 保存草稿
    handleSaveDraft() {
      this.$refs['appForm'].$children[0].validate((valid) => {

        if (this.clickFlag) {
          this.clickFlag = false;

          this.formData = JSON.parse(JSON.stringify(this.appFormValue))
          this.formData.id = this.orderId

          saveDraft({
            "processDefKey": this.gps.processDefKey,
            "title": this.appFormValue.title,
            "formData": this.formData
          }).then((res) => {
            this.clickFlag = true;
            if (!this.gps.location) {
              this.$router.push({
                name: "processDraft"
              });
            } else {
              this.dialogClose();
            }
          }).catch((err) => {
            this.clickFlag = true;
          });

        }
      });
    },

    // 废除草稿
    handleAbolish() {
      if (this.clickFlag) {
        this.clickFlag = false;
        deleteDraft({
          pmInsId: this.appFormValue.pmInsId,
          processDefKey: this.gps.processDefKey
        }).then((res) => {
          this.clickFlag = false;
          this.dialogClose();
        }).catch((err) => {
          this.clickFlag = true;
        });
      }
    },
    //封装验证函数
    submitForm(formUser) {
      return new Promise((resolve, reject) => {
        if (this.$refs[formUser]) {
          this.$refs[formUser].$children[0].validate((valid) => {
            if (valid) {
              resolve()
            } else {
              reject(new Error('错误'))
            }
          })
        } else {
          resolve()
        }
      })
    },
    // 流转下一步
    handleNextBtn() {
      let arr
      if (this.showBoss) {

        if (!this.appFormValue.interfaceAdminName) {
          this.$message.warning('请选择部门接口人')
          return;
        }

        if (this.gps.location == 'yqff.start') {
          arr = this.dataList.filter(item => item.hostUnitCode == item.taskHostUnitCode && !item.dutyTrueName)
        } else {
          arr = this.dataList.filter(item => !item.dutyTrueName)
        }

        if (arr.length > 0) {
          this.$message({
            type: "warning",
            message: '请选择' + this.gps.location == 'yqff.start' ? '办理人' : '责任人'
          });
          return
        }
      }

      if (this.showVice) {

        arr = this.dataList.filter(item => !item.dutyChargeTrueName)

        if (arr.length > 0) {
          this.$message({
            type: "warning",
            message: '请选择责任主管'
          });
          return
        }
      }

      if (this.showDuty) {

        arr = this.dataList.filter(item => !item.operatorTrueName)

        if (arr.length > 0) {
          this.$message({
            type: "warning",
            message: '请选择经办人'
          });
          return
        }
      }

      let str = (this.gps.location == 'person_read' || this.gps.location == 'assist_person_read') ? '请确认是否归档' : this.showDuty ? '任务负责人对任务的落实执行负责，后续需反馈任务进展情况，提交后不可再修改，请提前做好沟通工作。请确认是否提交' : '是否确认派发'
      this.$confirm(`${str}?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        toNextProcess({location: this.gps.location, pmInsId: this.gps.pmInsId, taskId: this.gps.taskId}).then(res => {
          if (this.gps.myFrom && this.$router.currentRoute.path == "/workOrder") { //单点
             if(this.gps.flushPortalUrl){
              // 集团单点流转
              var flushPortalUrl = decodeURIComponent(this.gps.flushPortalUrl);
              var params = {
                  "appcode": this.gps.appcode,
                  "uniqueId": this.gps.uniqueId,
                  "itemId": this.gps.itemId,
              }
              var pageUrlNew = this.util.toUrl(flushPortalUrl,params);
              window.location.replace(pageUrlNew)
            }else{
              window.opener = null;
              window.open("", "_self");
              window.close();

            }
          } else {
            if (!this.gps.location) {
              this.$router.push({
                name: "processTask"
              });
            } else {
              this.dialogClose();
            }
          }
        })
      }).catch(() => {
      });
    },
    // 确认
    handleConfirm() {
      // 判断流程下一步页面数据是否加载完
      var isLoad = this.$refs["processNext"].isLoad;
      // console.log(isLoad);

      if (!isLoad) {
        return false;
      }

      var decisionUser = this.$refs["processNext"].decisionUser;
      var decisionData = this.$refs["processNext"].decisionData;
      var choosedUser = this.$refs["processNext"].choosedUser;
      // console.log('已选决策项',JSON.parse(JSON.stringify(decisionData)))
      // console.log('人员',JSON.parse(JSON.stringify(decisionUser)))
      // console.log('已选人员',JSON.parse(JSON.stringify(choosedUser)))

      var flag = true;
      var gName = "";
      for (var i in decisionUser) {
        for (var j in decisionUser[i]) {
          if ((decisionUser[i][j].requSel === true || decisionUser[i][j].requSel === "true") && choosedUser[i][j].length == 0) {
            flag = false;
            if (i == "copy" && decisionUser[i][j].group != "normalGrouping") {
              gName = decisionData[i].decisionName.split("#")[1] + decisionUser[i][j].group;
            }
            break;
          }
        }
      }

      if ((!flag && decisionData["main"].decisionId.indexOf("_end") == -1) || !decisionUser.main.length || !decisionUser.main[0].user?.lengrh) {
        this.$message({
          message: "清选择" + gName + "审批人",
          type: "warning",
          duration: 1500
        });
        return false;
      }

      var nextUser = [],
          nextUserName = [],
          nextUserOrgCode = [],
          nextUserPostId = [];
      for (var i in choosedUser["main"]) {
        for (var j in choosedUser["main"][i]) {
          nextUser.push(choosedUser["main"][i][j].id);
          nextUserName.push(choosedUser["main"][i][j].name);
          nextUserOrgCode.push(choosedUser["main"][i][j].parentId);
          nextUserPostId.push("123");
        }
      }

      var data = {
        appCode: process.env.VUE_APP_APPCODE,
        type: !this.gps.location || (this.gps.type == "draft" && this.gps.location == process.env.VUE_APP_APPCODE + ".start") ? "START" : "FLOW",
        title: this.appFormValue.title || "",
        processDefKey: this.gps.processDefKey || "",
        processDefId: this.gps.processDefKey || "",
        pmInsType: this.gps.pmInsType || "",
        outcome: decisionData["main"].decisionId,
        taskDefinitionKey: decisionData["main"].targetActivityDefId,
        message: this.$refs["processNext"].opinion,
        nextUser: nextUser.join(","),
        nextUserName: nextUserName.join(","),
        nextUserOrgCode: nextUserOrgCode.join(","),
        nextUserPostId: nextUserPostId.join(","),
        nextActivityParam: decisionData["main"].nextActivityParam,
        formData: this.formData
      };
      data.activityDefId = this.gps.location ? this.gps.location : process.env.VUE_APP_APPCODE + ".start";
      if (this.gps.taskId) data.taskId = this.gps.taskId;
      if (this.gps.processInstId) data.processInstId = this.gps.processInstId;
      // if(this.gps.taskDefinitionKey) data.taskDefinitionKey = this.gps.taskDefinitionKey;
      if (this.gps.processDefinitionId) data.processDefinitionId = this.gps.processDefinitionId;

      this.processD = false;
      startProcess(data).then((res) => {
        if (this.gps.myFrom && this.$router.currentRoute.path == "/workOrder") { //单点
           if(this.gps.flushPortalUrl){
            // 集团单点流转
            var flushPortalUrl = decodeURIComponent(this.gps.flushPortalUrl);
            var params = {
                "appcode": this.gps.appcode,
                "uniqueId": this.gps.uniqueId,
                "itemId": this.gps.itemId,
            }
            var pageUrlNew = this.util.toUrl(flushPortalUrl,params);
            window.location.replace(pageUrlNew)
          }else{
             window.opener = null;
            window.open("", "_self");
            window.close();

          }
        } else {
          if (!this.gps.location) {
            this.$router.push({
              name: "processTask"
            });
          } else {
            this.dialogClose();
          }
        }
      });
    },
    // 关闭
    handleOptClose() {
      if (this.dialogClose) {
        //待办打开
        this.dialogClose();
      } else if (this.gps.myFrom && this.$router.currentRoute.path == "/workOrder") {//单点
        //单点
        window.opener = null;
        window.open("", "_self");
        window.close();
      } else {
        // let item = this.tabnav.find(item => item.path === this.$route.path);
        // this.$store.dispatch("CloseTabnav", item).then(res => {
        //   if (item.path === this.$route.path) {
        //     const lastTag = res.slice(-1)[0];
        //     // 前一个 tab-view 页面存在，就跳；不存在就到首页
        //     if (lastTag) {
        //       this.$router.push({ path: lastTag.path });
        //     } else {
        //       this.$router.push({ path: "/mywork/processTask" });
        //     }
        //   }
        // });
      }
    },
    // 查看意见
    handleViewComments() {
      this.doViewComments();
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }

};
</script>
<style scoped>
.red-box {
  width: 100%;
  cursor: pointer;
  color: rgba(192, 0, 0, 1);
}

::v-deep .tableCustom .el-table__body-wrapper {
  //max-height: 350px;
  overflow-y: auto;
}

::v-deep .tableCustom .el-textarea__inner {
  overflow: hidden !important;
  padding: 0;
  font-size: 13px;
  min-height: 23px !important;
}

::v-deep .tableCustom .el-textarea {
  //border: 1px solid #ebebeb;
  margin: 5px 0;
}

::v-deep .tableCustom .cell {
  white-space: normal;
  overflow: visible;
  text-overflow: inherit;
}

::v-deep .el-table .cell > .inlineC > .el-button {
  background: transparent;
  border: none;
  margin: 0;
  font-size: 14px;
  color: rgba(192, 0, 0, 1);
  padding: 7px 0px;
  white-space: normal;
}

::v-deep .el-input-group__append {
  background-color: #fff3f3;
  border-color: #fff3f3;
}

::v-deep .upload_D {
  min-width: 100%;
}

::v-deep .upload_Btn .uploadB {
  right: 0;
  left: auto;
}

.m-title {
  width: 100%;
  padding-left: 10px;
  border-left: 4px solid #39aef5;
  padding: 10px;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  border-bottom: 1px solid #ebebeb;
  border-right: 1px solid #ebebeb;
}

.m-4 {
  height: 32px;
  display: flex;
  align-items: center;
  padding: 0;
  border-right: 1px solid #ebebeb;
  border-bottom: 1px solid #ebebeb;
}

.m-4:first-child {
  border-top: 1px solid #ebebeb;
}

::v-deep .m-4 .el-input__inner {
  height: 30px;
  line-height: 30px;
  border: none;
}

::v-deep .m-4 .el-input__icon {
  line-height: 30px;
}

.m-2 {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100px;
  height: 100%;
  background: #DDF1FE;
  padding: 0 10px;
  font-size: 14px;
  color: #606266;
  font-weight: bold;
}

.m-5 {
  flex: 1;
}
</style>