﻿/**全局公共样式**/
.flex .el-form-item {
    display: flex;
}

.flex .el-form-item__content {
    width: 93%;
}

.flex .upload_D {
    width: 100%;
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
}

* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

html, body, h1, h2, h3, h4, h5, h6, div, dl, dt, dd, ul, ol, li, p, blockquote, pre, hr, table, th, td, form, fieldset, input, button, textarea, menu, i {
    margin: 0;
    padding: 0;
    font-family: "Microsoft Yahei", "微软雅黑", arial, "宋体", sans-serif;
}

img {
    vertical-align: middle;
    border: 0;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 500;
    color: inherit;
}

h1, h2, h3, h4, h5 {
    padding: 10px 0;
}

h1 {
    font-size: 36px;
}

h2 {
    font-size: 30px;
}

h3 {
    font-size: 25px;
}

h4 {
    font-size: 18px;
}

h5 {
    font-size: 14px;
}

h6 {
    font-size: 14px;
}

p {
    margin: 0px;
    line-height: 25px;
}

li {
    list-style: none;
}

ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

ol li {
    list-style: decimal;
}

li ul {
    padding-left: 10px;
}

div {
    word-wrap: break-word;
    word-break: break-all;
}

/*基础行高100 h数字 表示倍数 如h6表示600高*/
.hs {
    height: 80px;
}

.hsm {
    height: 60px;
}

.hsmm {
    height: 35px;
}

.h1 {
    height: 100px;
}

.h2 {
    height: 200px;
}

.h3 {
    height: 300px;
}

.h4 {
    min-height: 400px;
    _height: 400px;
}

.h5 {
    min-height: 500px;
    _height: 500px;
}

.h6 {
    min-height: 600px;
    _height: 600px;
}

.h32 {
    height: 32px;
}

.lh32 {
    line-height: 32px;
}

.lh65 {
    line-height: 65px;
}

.h16 {
    height: 16px;
}

/*基础宽度100 同上*/
.ws {
    width: 70px;
}

.wsm {
    width: 60px;
}

.wsmm {
    width: 35px;
}

.w1 {
    width: 100px;
}

.w2 {
    width: 200px;
}

.w3 {
    width: 300px;
}

.w4 {
    width: 400px;
}

.w5 {
    width: 500px;
}

.w6 {
    width: 600px;
}

.w7 {
    width: 700px;
}

.w8 {
    width: 800px;
}

.w9 {
    width: 900px;
}

.w100 {
    width: 100%;
}

.w50 {
    width: 50%;
}

.w40 {
    width: 40%;
}

.w30 {
    width: 30%;
}

.w25 {
    width: 25%;
}

.w60 {
    width: 60%;
}

.w75 {
    width: 75%;
}

.w20 {
    width: 20%;
}

.wauto {
    width: auto;
}

/*常用padding margin*/
.p0 {
    padding: 0;
}

.p5 {
    padding: 5px;
}

.p10 {
    padding: 10px;
}

.p15 {
    padding: 15px;
}

.p20 {
    padding: 20px;
}

.p30 {
    padding: 30px;
}

.plr15 {
    padding: 0px 15px;
}

.plr18 {
    padding: 0 18px;
}

.plr10 {
    padding: 0 10px;
}

.pt10lr18 {
    padding: 10px 18px 0;
}

.m0 {
    margin: 0;
}

.m5 {
    margin: 5px;
}

.m10 {
    margin: 10px;
}

.m15 {
    margin: 15px;
}

.m20 {
    margin: 20px;
}

.m30 {
    margin: 30px;
}

.m50 {
    margin: 50px;
}

.m60 {
    margin: 30px 60px;
}

.pr12 {
    padding-right: 12px;
}

.pr18 {
    padding-right: 18px;
}

.pt0 {
    padding-top: 0px;
}

.pb10 {
    padding-bottom: 10px;
}

.pb20 {
    padding-bottom: 20px;
}

.plf15 {
    padding: 0 15px;
}

.plr20 {
    padding: 0 20px;
}

.plr10 {
    padding: 0 10px;
}

.plr5 {
    padding: 0 5px;
}

.pr8 {
    padding-right: 8px;
}

.pt20 {
    padding-top: 20px;
}

.mr10 {
    margin-right: 10px;
}

.mb10 {
    margin-bottom: 10px;
}

.mr15 {
    margin-right: 15px;
}

.ml10 {
    margin-left: 10px;
}

.mr5 {
    margin-right: 5px;
}

.ml15 {
    margin-left: 15px;
}

.mt10 {
    margin-top: 10px;
}

.fright, .fr, a.fr {
    float: right;
}

.fleft, .fl {
    float: left;
}

.hide, .none {
    display: none;
}

.hide.show {
    display: table-cell;
}

.lh16 {
    line-height: 16px;
}

.lh20 {
    line-height: 20px;
}

.lh26 {
    line-height: 26px;
}


.bor_b {
    border-bottom: 1px solid #e6e6e6;
}

.text_c {
    text-align: center;
}

.c9a {
    color: #9a9a9a;
}

.fbold {
    font-weight: bold;
}

.inlineB {
    display: inline-block;
}

/*清除浮动*/
.txtl {
    text-align: left
}

.txtc {
    text-align: center
}

.txtr {
    text-align: right
}

.f10 {
    font-size: 10px;
    color: #999;
}

.f20 {
    font-size: 20px;
}

.f18 {
    font-size: 18px;
}

.f50 {
    font-size: 50px;
}

.f12 {
    font-size: 12px;
}

.f14 {
    font-size: 14px;
}

.f16 {
    font-size: 16px;
}

.line {
    clear: both;
    height: 1px;
    margin: 10px 0;
    width: 100%;
    overflow: hidden;
    background: #eee
}

.clearB {
    clear: both;
}

/*字体颜色*/
.col_r {
    color: #d9534f;
}

.col_b {
    color: #00b4f1;
}

.col_h {
    color: #9a9a9a;
}

.of_hidden {
    overflow: hidden;
}

@import './sidebar.css';

body {
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
    height: 100%;
    font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

html {
    box-sizing: border-box;
    height: 100%;
}

*,
*:before,
*:after {
    box-sizing: inherit;
}

div:focus {
    outline: none;
}

a:focus,
a:active {
    outline: none;
}

a,
a:focus,
a:hover {
    cursor: pointer;
    color: inherit;
    text-decoration: none;
}

.clearfix :after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
}

/**main-container全局样式**/
.app-main {
    min-height: 100%
}

.app-container {
    /* padding: 20px; */
    /* padding-top:40px; */
    /* padding-left: 0;
    padding-top: 0; */
}

/**搜索栏样式**/
.filter-container {

}

/**操作栏样式**/
.operate-container {
    margin-top: 20px;
}

.operate-container .btn-add {
    float: right;
}

/**表格栏样式**/
.table-container {
    margin-top: 20px;
    padding-right: 20px;
}

/**批量操作栏样式**/
.batch-operate-container {
    display: inline-block;
    margin-top: 20px;
}

/**分页栏样式**/
.pagination-container {
    display: inline-block;
    float: right;
    margin-top: 20px;
    padding-right: 20px;
}

/**添加、更新表单样式**/
.form-container {
    position: absolute;
    left: 0;
    right: 0;
    width: 720px;
    padding: 35px 35px 15px 35px;
    margin: 20px auto;
}

/**主标题**/
.font-extra-large {
    font-size: 20px;
    color: #303133;
}

/**标题**/
.font-title-large {
    font-size: 18px;
    color: #303133;
}

/**小标题**/
.font-title-medium {
    font-size: 16px;
    color: #303133;
}

/**正文**/
.font-medium {
    font-size: 16px;
    color: #606266;
}

/**正文**/
.font-small {
    font-size: 14px;
    color: #606266;
}

/**正文（小）**/
.font-extra-small {
    font-size: 13px;
    color: #606266;
}

.color-main {
    color: #409EFF;
}

.color-success {
    color: #67C23A;
}

.color-warning {
    color: #E6A23C;
}

.color-danger {
    color: #F56C6C;
}

.color-info {
    color: #909399;
}

.el-date-editor.el-input {
    width: 100%;
}

.el-select {
    width: 100%;
}

/* .el-form-item.is-success .el-input__inner{border-color:#409EFF;} */

.batch-operate-container .el-select {
    width: 120px;
}

.el-input.is-disabled .el-input__inner, .el-textarea.is-disabled .el-textarea__inner, .el-cascader.is-disabled .el-cascader__label {
    background-color: transparent;
    color: #606266;
}

.el-radio__input.is-disabled + span.el-radio__label, .el-checkbox__input.is-disabled + span.el-checkbox__label {
    color: #606266;
}

.el-table td {
    padding: 5px 0;
}

.el-table, .el-submenu__title, .el-tree-node__label, .app-breadcrumb.el-breadcrumb, .el-menu-item {
    font-size: 13px;
}

/* .el-submenu__title{border-bottom: solid 1px #f5f5f5 !important;} */
/*form布局宽度*/
.c12 {
    width: 100%;
}

.c11 {
    width: 91.6667%;
}

.c10 {
    width: 83.3333%;
}

.c9 {
    width: 75%;
}

.c8 {
    width: 66.6667%;
}

.c7 {
    width: 58.3333%;
}

.c6 {
    width: 50%;
}

.c5 {
    width: 41.6667%;
}

.c4 {
    width: 33.3333%;
}

.c3 {
    width: 25%;
}

.c2 {
    width: 16.6667%;
}

.c1 {
    width: 8.33333%;
}

.flexNowrap {
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: flex-start;
}

.el-button {
    border-radius: 0;
}

.el-button--primary {
    background: rgba(192, 0, 0, 1);
    border: solid 1px rgba(192, 0, 0, 1);
}

.el-button--success {
    background: #8DC21F;
    border: solid 1px #8DC21F;
}

.el-button--danger {
    background: #F56C6C;
    border: solid 1px #F56C6C;
}

.el-button--primary:focus, .el-button--primary:hover {
    background: rgba(192, 0, 0, 1);
}

.el-button--success:focus, .el-button--success:hover {
    background: #8DC21F;
}

.el-button--danger:focus, .el-button--danger:hover {
    background: #F56C6C;
}

.el-table .cell > .inlineB > .el-button {
    background: transparent;
    border: none;
    margin: 0;
    font-size: 14px;
    color: rgba(192, 0, 0, 1);
    padding: 7px 5px
}

.el-table .cell > .inlineB > .el-button--danger {
    color: #F56C6C;
}

/* .el-table .cell>.inlineB>.el-button--primary{color:rgba(192,0,0,1);}
.el-table .cell>.inlineB>.el-button--success{color:#8DC21F;}
.el-table .cell>.inlineB>.el-button--danger{color:#F56C6C;} */
/* .el-button:focus, .el-button:hover {
  color: rgba(192,0,0,1);
  border-color: #c6e2ff;
  background-color: #ecf5ff;
} */
.el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: rgba(192, 0, 0, 1);
}

/* .el-dialog__header{background: linear-gradient(to right,var(--el-color-primary),#4CC071);padding: 10px;color:#fff;height: 40px;
  border-radius: 5px 5px 0px 0px;} */
/* .el-dialog__header{background: linear-gradient(270deg, #2F9FF5 0%, var(--el-color-primary) 100%);padding: 10px;color:#fff;height: 40px;
  border-radius: 5px 5px 0px 0px;} */
.el-dialog__header {
    background: rgba(192, 0, 0, 1);
    padding: 10px;
    color: #fff;
    height: 40px;
    border-radius: 5px 5px 0px 0px;
}

.el-dialog__headerbtn .el-dialog__close, .el-dialog__headerbtn .el-dialog__close:hover {
    color: #fff;
}

.el-dialog__headerbtn {
    top: 10px;
}

.el-dialog {
    border-radius: 5px;
}

.el-dialog__title {
    color: #fff;
    font-size: 16px;
}

.el-switch.is-checked .el-switch__core {
    background: rgba(192, 0, 0, 1);
    border-color: rgba(192, 0, 0, 1);
}

.el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner, .el-radio__input.is-checked .el-radio__inner {
    background: rgba(192, 0, 0, 1);
    border-color: rgba(192, 0, 0, 1);
}

.el-checkbox__input.is-checked + .el-checkbox__label, .el-radio__input.is-checked + .el-radio__label {
    color: rgba(192, 0, 0, 1);
}

.filter-container {
    border-bottom: 1px solid #ccc;
    padding: 10px 20px 10px 5px;
    margin-bottom: 20px;
}

.el-main {
    padding: 0 !important;
}

.el-aside {
    border-right: 1px solid #ccc;
    padding-top: 15px;
    padding-left: 10px;
}

.table-container, .batch-operate-container {
    padding-left: 20px;
}

.menuLeft .el-submenu__title i {
    color: #ccc;
}

/* 选下一步流转人 单选 */
.el-tree-node__content > label.is-disabled {
    display: none !important;
}

.el-form > .tishi > .el-form-item > .el-form-item__content > div {
    color: rgb(175, 173, 173);
    margin-left: 60px;
    line-height: 20px;
    margin-top: -3px;
}

/* .el-tree .is-current{background: #e4e3e3;} */
/* 点击表格内标题进入详情页或二级 */
.toDetail {
    color: rgba(192, 0, 0, 1) !important;
    cursor: pointer;
}

.toDetail:hover {
    text-decoration: underline;
}

/* 只读表单行高设置 */
.readForm > .inlineB {
    height: 40px;
}

.el-input.is-disabled .el-input__inner, .el-textarea.is-disabled .el-textarea__inner, .el-cascader.is-disabled .el-cascader__label {
    background-color: #F5F7FA;
    color: #C0C4CC;
}

/* .el-input.is-disabled .el-input__inner, .el-textarea.is-disabled .el-textarea__inner, .el-cascader.is-disabled .el-cascader__label{background-color: #fff;border:none;}
.el-input__suffix,.el-input__prefix{display: none;} */
/* 没有圆角的input */
.noRadius .el-input__inner {
    border-radius: 0;
}

.noRightRadius .el-input__inner {
    border-radius: 4px 0 0 4px;
}

.el-dialog__wrapper > div {
    margin-top: 6vh;
}

/* .el-dialog__body{max-height:600px;overflow-y: auto;} */
/* .el-dialog__body div.dialog-footer{position: sticky;bottom: 20px;right:20px;} */
.el-table .warning-row {
    background: oldlace;
}

.el-table .success-row {
    background: #f0f9eb !important;
}

.el-table .danger-row {
    background: rgb(253, 226, 226) !important;
}

/* 去除表格默认hover样式，不影响自己设置的颜色 */
.el-table .hover-row > td {
    background: rgba(0, 0, 0, 0) !important;
}

.el-table .current-row:hover {
    background: #ecf5ff !important;
}

/* 选中行高亮颜色 */
.el-table__body tr.current-row > td {
    background-color: #cbebf8 !important;
}

/* 表格表头高度 */
.el-table__header-wrapper {
    height: 32px;
}

.el-table .has-gutter, .el-table .thClassName > th {
    height: 32px !important;
}

.el-table th {
    background-color: #f7eae9 !important;
    padding: 0;
}

.el-table td, .el-table .el-table__cell {
    padding: 0;
}

.el-table tr.el-table__row, .el-table tr.el-table__row td {
    height: 40px !important;
}

.el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
    border-bottom-color: #dddddd;
}
.el-table--border .el-table__cell, .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
    border-right-color: #dddddd;
}
.el-table--border, .el-table--group {
    border-color: #dddddd;
}

/* 树图标颜色设置 */
#iconsanwenjian {
    color: #ABB9C5;
}

#iconxiangmu {
    color: #1176E9;
}

#iconhe {
    color: #45637E;
}

.wenjian > .icon {
    color: #ABB9C5 !important;
}

/* 按钮padding */
.el-button--small, .el-button--small.is-round {
    padding: 7px 12px;
}

.tabnav > .scroll-container {
    padding-left: 30px;
}

.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    background-color: #d7e7fa;
}

/* .menuL1 .el-submenu .el-submenu__icon-arrow{display: none;} */

/* 把表格合计行放到第一行 */
/* .el-table {
  display: flex;
  flex-direction: column;
} */
/* order默认值为0，只需将表体order置为1即可移到最后，这样合计行就上移到表体上方 */
/* .el-table__body-wrapper {
  order: 1;
} */
/* 上传按钮高度 */
.uploadB {
    height: 29px;
    top: 1px;
}

.el-form-item__content > div {
    overflow: hidden;
}

.tableQueryForm .el-form-item {
    flex-wrap: nowrap !important;
}


/* zengli */
.tableForm {
    margin-top: 10px;
    border-top: 1px solid #ebebeb;
    border-left: 1px solid #ebebeb;
}

.tableForm .orderTitle {
    width: 100%;
    font-size: 18px;
    color: rgba(192, 0, 0, 1);
    font-weight: bold;
    text-align: center;
    padding: 10px 0;
    border-bottom: 1px solid #ebebeb;
    border-right: 1px solid #ebebeb;
}

.tableForm .inlineB {
    vertical-align: top;
    border-right: 1px solid #ebebeb;
    border-bottom: 1px solid #ebebeb;
}

.tableForm .el-form-item {
    display: flex;
    margin-right: 0;
    margin-bottom: 0px;
}

.tableForm .el-form--label-top .el-form-item {
    display: inline-block;
}

.tableForm .el-form-item .el-form-item__label {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    background: rgba(247, 234, 233, 1);
    padding: 0 10px;
}

.tableForm .el-form--label-right .el-form-item .el-form-item__label {
    justify-content: flex-end;
}

.tableForm .el-form-item .el-form-item__content {
    flex: 1;
    width: calc(100% - 130px);
    height: 100%;
    padding: 0;
}

.tableForm .el-input--mini .el-input__inner {
    height: 32px;
}

.tableForm .el-input__inner,
.tableForm .el-textarea__inner {
    border: none;
}

/* .tableForm .upload_D.next{
  left: calc(100% - 80px);
} */

.tableForm .upload_Btn {
    right: 0;
}

.tableForm .upload_Btn input {
    width: 100%;
}

.tableForm .upload_Btn button span {
    display: inline-block;
    /* width: 15px; */
    height: 15px;
    /* color: rgba(192,0,0,1);
    background: #fff;
    border-radius: 50%; */
}

.tableForm .files .table-container {
    padding-left: 0;
}

.tableForm .el-button--primary {
    background: rgba(192, 0, 0, 1);
    border: 1px solid rgba(192, 0, 0, 1);
}

.tableForm .el-textarea__inner {
    min-height: 96px !important;
}

.tableForm .el-input.is-disabled .el-input__inner,
.tableForm .el-textarea.is-disabled .el-textarea__inner,
.tableForm .el-cascader.is-disabled .el-cascader__label {
    color: #606266;
    background-color: #fff;
}

.tableForm .el-form-item__error {
    top: calc(100% - 18px);
    left: calc(100% - 130px);
    display: none;
}

.tableForm .el-form-item.is-error .el-input__inner,
.tableForm .el-form-item.is-error .el-input__inner:focus,
.tableForm .el-form-item.is-error .el-textarea__inner,
.tableForm .el-form-item.is-error .el-textarea__inner:focus,
.tableForm .el-message-box__input input.invalid,
.tableForm .el-message-box__input input.invalid:focus {
    background-color: #fff3f3;
}

/* 流程按钮 */
.pageInfo {
    display: flex;
    align-items: center;
    height: 60px;
    padding: 15px;
    background: #F2F2F2;
    border-left: 6px solid rgba(192, 0, 0, 1);
}

.pageInfo span.btn {
    height: 30px;
    padding: 5px 10px;
    font-size: 12px;
    color: #fff;
    text-align: center;
    background: rgba(192, 0, 0, 1);
    margin-right: 15px;
    cursor: pointer;
    border-radius: 2px;
}

.pageInfo span.btn:last-child {
    margin-right: 0;
}

.pageInfo span.btn .icon {
    color: #fff;
    margin-right: 4px;
}

.pageInfo span.close,
.pageInfo span.del {
    background: #cbf1fe;
    color: rgba(192, 0, 0, 1);
}

.pageInfo span.a_danger {
    background-color: #d9534f;
    border-color: #d43f3a;
    color: #fff;
}

.flex {
    display: flex;
}

.flex1 {
    flex: 1;
}

.column {
    flex-direction: column;
}

.wrap {
    flex-wrap: wrap;
}

.a-c {
    align-items: center;
}

.j-c {
    justify-content: center;
}

.j-s {
    justify-content: space-between;
}

.center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.white {
    color: #fff;
}

.single {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.text_l {
    text-align: left;
}

