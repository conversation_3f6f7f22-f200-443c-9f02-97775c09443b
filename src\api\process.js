import request from "@/assets/js/request";
import store from "../store";
import util from "@/assets/js/public";

// 获取决策项
export function getDecision(params, pmInsId) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/process/findDecisions?source=PC&pmInsId=${pmInsId}`,
        contentType: "application/json; charset=utf-8",
        data: params ? params : {}
    });
}

// 获取版本号
export function getLastVersion(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/process/getLastVersionByProDefId?key=${params.key}&tenantId=${params.tenantId}`,
        contentType: "application/json; charset=utf-8"
    });
}

// 获取人员组织树
export function getOrgAndUser(params, pmInsId) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/getOrgAndUser?source=PC&pmInsId=${pmInsId}`,
        contentType: "application/json; charset=utf-8",
        data: params ? params : {}
    });
}

// 我的待办
export function findProcessTask(params) {
    let data = params ? params : {};
    data.databaseType = "oracle";
    data.participant = "OA";
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/task/myTaskToDo?source=PC&page=${params.page}&rows=${params.size}&loginuser=${store.state.user.user.username}`,
        contentType: "application/json;charset=UTF-8",
        data: data
    });
}

// 我的草稿
export function findProcessDraft(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/myDraftToDo?source=PC&page=${params.page}&size=${params.size}&loginuser=${store.state.user.user.username}`,
        contentType: "application/json;charset=UTF-8",
        data: params
    });
}

// 我的已办
export function findProcessJoin(params) {
    let data = params ? params : {};
    data.databaseType = "oracle";
    data.participant = "OA";
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/task/myJoin?source=PC&page=${params.page}&rows=${params.size}&loginuser=${store.state.user.user.username}`,
        contentType: "application/json;charset=UTF-8",
        data: data
    });
}

// 待阅列表
export function findProcessRead(params) {
    let data = params ? params : {};
    data.databaseType = "oracle";
    data.participant = "OA";
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/process/findMyPending?source=PC&page=${params.page}&size=${params.size}&loginuser=${store.state.user.user.username}`,
        contentType: "application/json;charset=UTF-8",
        data: data
    });
}

// 已阅列表
export function findProcessDoRead(params) {
    let data = params ? params : {};
    data.databaseType = "oracle";
    data.participant = "OA";
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/process/findMyRead?source=PC&page=${params.page}&size=${params.size}&loginuser=${store.state.user.user.username}`,
        contentType: "application/json;charset=UTF-8",
        data: data
    });
}

// 流程跟踪
export function findFlowTracking(pmInsId) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/task/findTaskOptMsg?pmInsId=${pmInsId}&source=PC`,
        contentType: "application/json;charset=UTF-8"
    });
}

// 流程图
export function getDiagram(processInstanceId) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/process/getDiagram?processInstanceId=${processInstanceId}`,
        method: 'get',
        contentType: "application/json;charset=UTF-8",
        responseType: "blob"
    });
}

// 查看意见
export function getWfOptMags(processInstanceId) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/process/getWfOptMags?processInstanceId=${processInstanceId}`,
        contentType: "application/json;charset=UTF-8"
    });
}

// 废除草稿
export function deleteDraft(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/deleteDraft`,
        contentType: "application/json;charset=UTF-8",
        data: params
    });
}


// 查询待阅的流程跟踪和意见
export function flowTodoReTracking(processInstanceId) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/process/flowTodoReTracking?processInstanceId=${processInstanceId}`,
        contentType: "application/json;charset=UTF-8"
    });
}

// 获取pmInsId
export function getPmInsId(type) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/usExpert/getPmInsId?source=PC&type=${type}`,
        contentType: "application/json;charset=UTF-8"
    });
}

// 获取要选择的人
export function getUserByType(type) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/findDutyInfo?source=PC&type=${type}`,
        contentType: "application/json;charset=UTF-8"
    });
}

// 保存部门接口人
export function saveInterface(data) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/application/saveInterfaceAdmin?source=PC`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}

// 保存选择的人
export function saveUser(location, data, taskId) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/sub/updateTaskOrDuty?source=PC&location=${location}&taskId=${taskId}`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}

// 新增 编辑 任务
export function editTask(location, data) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/sub/createOrUpdateTask?source=PC&location=${location}`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}

// 删除 任务
export function delTask(location, id) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/sub/deleteSubById?source=PC&location=${location}&id=${id}`,
        contentType: "application/json;charset=UTF-8"
    });
}

// 派发流转下一步
export function toNextProcess(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/processSubmit?source=PC&location=${params.location}&pmInsId=${params.pmInsId}&taskId=${params.taskId}`,
        contentType: "application/json;charset=UTF-8"
    });
}

// 反馈获取Ai润色内容
export function getAIContent(params, data) {
    let url = `/${process.env.VUE_APP_APPCODE}/action/collection/polish?source=PC&location=${params.location}&pmInsId=${params.pmInsId}&taskId=${params.taskId}`;
    return request({
        url,
        contentType: "application/json;charset=UTF-8",
        data
    });
}

// 反馈流转下一步
export function toNextProcessFeed(params, data) {
    let url = `/${process.env.VUE_APP_APPCODE}/action/collection/handle?source=PC&location=${params.location}&pmInsId=${params.pmInsId}&taskId=${params.taskId}`;
    return request({
        url,
        contentType: "application/json;charset=UTF-8",
        data
    });
}

// 反馈保存反馈信息
export function saveFeedInfo(data) {
    let url = `/${process.env.VUE_APP_APPCODE}/action/collection/saveCollection`;
    return request({
        url,
        contentType: "application/json;charset=UTF-8",
        data
    });
}

// 催办
export function cuiban(data) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/collection/sendMessage`,
        contentType: "application/json;charset=UTF-8",
        loading: true,
        data
    });
}

// 导出
export function daochu(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/sub/exportSendSubInfo?location=${params.location}&pmInsId=${params.pmInsId}&taskId=${params.taskId}&type=${params.type}`,
        contentType: "application/json;charset=UTF-8",
        responseType: "blob"
    });
}

// 通报起草页面回显数据接口
export function queryReportInfo() {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/report/queryReportInfo`,
        contentType: "application/json;charset=UTF-8"
    });
}

// 通报流程起草流转接口 （除了退回都是这个接口）
export function reportNext(params, data) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/report/submit?source=PC&location=${params.location}&pmInsId=${params.pmInsId}&taskId=${params.taskId}`,
        contentType: "application/json;charset=UTF-8",
        data,
        loading: true
    });
}

// 发布通报详情
export function reportDetail(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/report/queryDetail?source=PC&location=${params.location}&pmInsId=${params.pmInsId}&taskId=${params.taskId}&type=${params.type}`,
        contentType: "application/json;charset=UTF-8"
    });
}

// 通报流程导出
export function reportDaochu(type, pmInsId) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/report/${type}?pmInsId=${pmInsId}`,
        contentType: "application/json;charset=UTF-8",
        responseType: "blob",
        loading: true
    });
}

// 通报流程退回
export function reportBack(params, data) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/report/giveBack?source=PC&location=${params.location}&pmInsId=${params.pmInsId}&targetId=${params.targetId}&taskId=${params.taskId}&nextUser=${params.nextUser}`,
        contentType: "application/json;charset=UTF-8",
        data,
    });
}

// 退回意见列表
export function backlist(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/report/returnMsg?source=PC&pmInsId=${params.pmInsId}&targetId=${params.targetId}&departmentCode=${params.departmentCode}`,
        contentType: "application/json;charset=UTF-8"
    });
}

// 通报流程详情的历史反馈
export function historylist(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/report/history?taskHostUnitCode=${params.taskHostUnitCode || ''}&pmInsId=${params.pmInsId}&targetId=${params.targetId}`,
        contentType: "application/json;charset=UTF-8"
    });
}

// 历史反馈下拉数据源
export function hisoptions(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/report/historySelect?pmInsId=${params.pmInsId}&targetId=${params.targetId}`,
        contentType: "application/json;charset=UTF-8"
    });
}

// 任务管理获取反馈周期
export function getfeeddate(type) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/collection/queryCycle?type=${type}`,
        contentType: "application/json;charset=UTF-8"
    });
}

// 任务管理获取反馈台账
export function queryCollection(id, type) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/collection/queryCollection?id=${id}&type=${type}`,
        contentType: "application/json;charset=UTF-8"
    });
}

// 反馈台账导出
export function feedexport(id, type) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/collection/export?id=${id}&type=${type}`,
        contentType: "application/json;charset=UTF-8",
        loading: true,
        responseType: "blob"
    });
}

// 历史台账通报导出
export function sendexport(latitudeId) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/collection/exportQuery?latitudeId=${latitudeId}`,
        contentType: "application/json;charset=UTF-8",
        loading: true,
        responseType: "blob"
    });
}

// 任务管理获取结项列表
export function queryConclusion(data) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/check/queryContent?source=PC`,
        contentType: "application/json;charset=UTF-8",
        data,
    });
}

// 任务管理结项获取人员
export function conclusionPerson(data) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/check/findNextUser?source=PC`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}

// 任务管理结项详情
export function conclusionDetail(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/check/queryDetail?source=PC&pmInsId=${params.pmInsId}&id=${params.id}`,
        contentType: "application/json;charset=UTF-8"
    });
}

// 任务管理结项提交 退回
export function conclusionSubmit(params, data) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/check/submit?source=PC&location=${params.location}&pmInsId=${params.pmInsId}&taskId=${params.taskId}&type=${params.type}&nextUser=${params.nextUser}`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}

// AI周报
export function AIdate(id) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/collection/exportPolish?id=${id}`,
        contentType: "application/json;charset=UTF-8",
        loading: true,
        responseType: "blob"
    });
}

// 获取管理员
export function adminlist(params, data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/admin/queryAdmin?page=${params.page}&size=${params.size}`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}
// 新增管理员
export function adminadd(data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/admin/addAdmin`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}
// 修改管理员
export function adminupdate(data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/admin/updateAdmin`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}
// 删除管理员
export function adminremove(id){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/admin/delAdmin?id=${id}`,
        contentType: "application/json;charset=UTF-8",
    });
}

// 获取接口人
export function interfacelist(params, data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/interface/queryAdmin`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}
// 修改接口人
export function interfaceupdate(data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/interface/updateAdmin`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}
// 删除接口人
export function interfaceremove(id){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/interface/deleteById?id=${id}`,
        contentType: "application/json;charset=UTF-8",
    });
}
// 导出接口人
export function interfaceexport(data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/interface/export`,
        contentType: "application/json;charset=UTF-8",
        data,
        responseType: "blob"
    });
}
// 获取变更记录
export function changelist(id){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/admin/queryUpdateLog?id=${id}`,
        contentType: "application/json;charset=UTF-8"
    });
}

// 获取任务负责人
export function queryTaskUser (data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/admin/queryTaskUser`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}
// 导出任务负责人
export function taskUserexport(data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/admin/export`,
        contentType: "application/json;charset=UTF-8",
        data,
        responseType: "blob"
    });
}
// 修改任务负责人
export function updateTaskUser (data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/admin/updateTaskUser`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}

// 公共封装
export function mzlRequest(requestBody) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}${requestBody.url}`,
        contentType: requestBody.contentType || "application/json;charset=UTF-8",
        data: requestBody.data || {},
        loading: requestBody.loading || false,
        responseType: requestBody.responseType
    });
}

// 周报维护获取
export function queryAIReport(latitudeId, taskInfo) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/collection/aiDetail`,
        contentType: "application/json;charset=UTF-8",
        data: {
            latitudeId,
            taskInfo
        }
    });
}

// 获取用户权限
export function userrolelist(data){
    // 确保分页参数正确传递
    const page = data.page || 1;
    const size = data.size || 10;
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/user/queryAll?source=PC&page=${page}&size=${size}`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}
// 新增用户权限
export function userroleadd(data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/user/addUser`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}
// 修改用户权限
export function userroleupdate(data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/user/updateUser`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}
// 删除用户权限
export function userroleremove(id){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/user/deleteUser`,
        contentType: "application/json;charset=UTF-8",
        data: { id }
    });
}
// 获取条线列表
export function queryLineType(){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/document/queryLineType`,
        contentType: "application/json;charset=UTF-8"
    });
}

// 获取月度通报
export function monthreportlist(params, data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/document/queryDocument?page=${params.page}&size=${params.size}&source=PC`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}
// 新增月度通报
export function monthreportadd(data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/document/addDocument`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}
// 修改月度通报
export function monthreportupdate(data){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/document/updateDocument`,
        contentType: "application/json;charset=UTF-8",
        data
    });
}
// 删除月度通报
export function monthreportremove(id){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/document/deleteDocument`,
        contentType: "application/json;charset=UTF-8",
        data: { id }
    });
}
// 推送
export function pushAi(id){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/user/pushAi?id=${id}`,
        contentType: "application/json;charset=UTF-8",
        loading: true,
    });
}

// 获取推送记录
export function getPushRecords(distributeId, page = 0, size = 10){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/distribute/findAllByDistributeId?distributeId=${distributeId}&page=${page}&size=${size}`,
        contentType: "application/json;charset=UTF-8"
    });
}

// 获取数据字典
export function queryDictByType(dictType){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/queryDictValue/queryByType?dictType=${dictType}`,
        contentType: "application/json;charset=UTF-8",
    });
}
// 大屏
export function findArchivedReportsByPage(params){
    return request({
        url: `/gjcc/shine/ShineReport/findArchivedReportsByPage/sso?page=1&size=3&source=PC&appcode=yqff&loginuser=${util.encrypt(store.state.user.user.username)}`,
        contentType: "application/json;charset=UTF-8",
        data:params

    });
}
export function findKnowledgeBase(params){
    return request({
        url: `/gjcc/apply/UsApplyInfo/findKnowledgeBase/sso?page=1&size=3&source=PC&appcode=yqff&loginuser=${util.encrypt(store.state.user.user.username)}`,
        contentType: "application/json;charset=UTF-8",
        data:params
    });
}
// 荣誉
export function rongyuList(dictType){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/UsDeptHonor/selectAllNoPage`,
        contentType: "application/json;charset=UTF-8",
    });
}
// 荣誉
export function centerList(dictType){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/UsTaskProgress/selectAllNoPage`,
        contentType: "application/json;charset=UTF-8",
    });
}
// 荣誉平均数
export function getLineInfo(dictType){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/UsTaskProgress/getLineInfo`,
        contentType: "application/json;charset=UTF-8",
    });
}