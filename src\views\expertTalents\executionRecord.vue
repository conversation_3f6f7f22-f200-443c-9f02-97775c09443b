<template>
  <div class="app-container">
    <sb-el-table :table="table" @getList="getList" @updateTableData="updateTableData" :on-ok="handleDoFun">
      <template v-slot:file="{obj}">
        <div class="flex a-c">
          <span class="file-1" @click="viewHandle(obj.row.file)">{{ obj.row.file.fileName }}</span>
          <i class="el-icon-download" @click="downloadHandle(obj.row.file)"></i>
        </div>
      </template>
      <template v-slot:uploadContent="{obj}">
        <div class="flex a-c" v-if="obj.row.type == 0">
          <span class="file-1" @click="viewHandle(obj.row.contentFile)">{{ obj.row.contentFile.fileName }}</span>
          <i class="el-icon-download" @click="downloadHandle(obj.row.contentFile)"></i>
        </div>
        <span v-if="obj.row.type == 1">{{ obj.row.content }}</span>
      </template>
    </sb-el-table>
  </div>
</template>

<script>
import {mzlRequest} from "@/api/process";

export default {
  data() {
    return {
      title: "",
      showDialog: false,
      curComponent: "",
      pageLabel: "taskInfo",
      table: {
        modulName: "列表查询", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: true, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "110px",
          labelPosition: 'right',
          formItemList: [
            {label: "数字员工名称", key: "modelId", type: "select", options: []},
            {
              label: "执行日期",
              key: "timeList",
              type: "date",
              subtype: 'datetimerange',
              valueFormat: 'yyyy-MM-dd HH:mm:ss'
            }
          ],
        },
        tr: [
          {
            id: "applyTrueName",
            label: "执行人",
            prop: 'applyTrueName',
            width: 120
          },
          {
            id: "createdTime",
            label: "执行时间",
            prop: 'createdTime',
            width: 200
          },
          {
            id: "modelName",
            label: "数字员工名称",
            prop: 'modelName',
          },
          {
            id: "uploadContent",
            label: "上传内容",
            prop: '',
            show: 'template',
            template: 'uploadContent'
          },
          {
            id: "file",
            label: "执行结果",
            prop: 'file',
            show: 'template',
            template: 'file'
          }
        ],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "400px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: false, //是否有操作列表
        operation: {
          width: "160",
          fixed: "right",
          data: [
            // {id: "lookHandle", name: "【查看执行结果】", fun: "lookHandle"},
          ],
        },
        hasOtherQueryBtn: false, //是否有其他操作
        otherQueryBtn: {
          data: [
            {id: "export", type: "success", name: "导出", fun: "handleExport"}
          ]
        },
        hasPagination: true,
        listQuery: {page: 1, size: 10},
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      }
    }
  },
  created() {
    this.getList()
    mzlRequest({
      url: `/action/numerical/queryModel?source=PC&page=1&size=999`
    }).then((res) => {
      this.table.queryForm.formItemList[0].options = res.data.map(item => {
        return {name: item.title, value: item.id}
      });
    })
  },
  methods: {
    viewHandle(file) {
      this.util.fileOpen(file.id)
    },
    downloadHandle(file) {
      this.util.fileDownload(file.id)
    },
    // 查询列表
    getList(listQuery) {
      this.table.loading = true;
      let params = listQuery || this.table.listQuery
      mzlRequest({
        url: `/action/numerical/queryAll?source=PC&page=${params.page}&size=${params.size}`,
        data: {
          modelId: listQuery?.modelId || '',
          startTime: listQuery?.timeList?.length ? listQuery.timeList[0] : '',
          endTime: listQuery?.timeList?.length ? listQuery.timeList[1] : ''
        }
      }).then((res) => {
        this.table.loading = false;
        res.data.content.forEach(item => {
          item.createdTime = this.util.getNow('yyyy-MM-dd hh:mm:ss', item.createdTime)
        })
        this.table.data = res.data.content;
        this.table.total = res.data.totalElements
      }).catch((err) => {
        this.table.loading = false;
      });
    },
    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    },
  }
}
</script>

<style scoped>
.el-icon-download {
  color: rgba(192, 0, 0, 1);
  cursor: pointer;
  margin-left: 10px;
  font-size: 18px;
  font-weight: bold;
}

.file-1 {
  color: rgba(192, 0, 0, 1);
  cursor: pointer;
  text-decoration: underline;
}

.title {
  width: 99%;
  height: 30px;
  line-height: 30px;
  padding-left: 10px;
  border-left: #39aef5 4px solid;
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.titleName {
  font-size: 15px;
  color: #333;
  font-weight: 700;
}

</style>