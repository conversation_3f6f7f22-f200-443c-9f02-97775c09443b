<template>
	<div>
		<component :class="isDialog?'w99':'p10'" v-if="dynamicComponent" :is="dynamicComponent" :key="cKey" :href="gps" :dialogClose="dialogClose"></component>
	</div>
</template>
<script>
	export default {
		name: "WorkOrder",
		props: {
			gps: {
				type: Object,
				default () {
					return this.$route.query;
				}
			},
			cKey: {
				type: Number,
			},
			dialogClose: {
				type: Function
			},
			isDialog: {
				type: <PERSON><PERSON><PERSON>,
				default () {
					return true;
				}
			}
		},
		computed: {
			dynamicComponent() {
				// 根据流程类型判断引入文件
				var th = this.util.appNameTH(this.gps.pmInsType);
				if (th.path) {
					return () => import(`@/${th.path}`); // import不能接收纯动态参数
				} else {
					return null;
				}
			}
		},
		data() {
			return {

			}
		},
		created() {

		},
		mounted() {

		},
		methods: {
			handleDoFun(obj, fun, data) {
				//若一个beforeFun可直接在这个函数里面写
				let n = this[obj[fun]].call(this, obj, data);
				return n;
			}
		}
	};
</script>
<style scope>
	.w99 {
		width: 99%;
		margin: 0 auto;
	}

	.p10 {
		padding: 10px;
	}

	.el-dialog__body {
		padding: 20px 20px 0;
	}
</style>