<template>
	<div class="app-container" style="display:flex;justify-content: space-between;">
		<div class="container-left">
			<el-card shadow="never" style="overflow: auto; width: 100%;height: 100%;">
				<template #header>
				<div class="card-header">
					<!-- <el-input v-model="dbName" placeholder="请输入数据源名称" size="small" style="width:70%" clearable /> -->
                    <!-- <span style="color:red">修改组织信息后需手动点击当前父级组织更新数据</span> -->
				</div>
				</template>
				<div class="head-container">
					<el-tree class="tree1" :props="defaultProps" ref="chooseOrgTree" :default-expanded-keys="treeExpandData" :load="loadNodeOrg" lazy :node-key="'id'" :highlight-current="true" :check-on-click-node="true">
						<!-- :expand-on-click-node="false" -->
						<template #default="{ node, data }">
							<div @click="handleNodeClick(node, data)" style="width:100%;height: 100%;line-height: 26px;">{{ data.orgName }}</div>
						</template>
					</el-tree>
				</div>
			</el-card>
		</div>
		<div class="container-right">
			<sb-el-table :table="table" @getList="getList" @handleAddData="handleAddData" @handleUpData="handleUpData" @handleDelete="handleDelete" 
						@handleUpDataGetRow="handleUpDataGetRow" @handleAddBef="handleAddBef">
                <template v-slot:rateRule="{ obj }">
                    <div slot="content" class="rateRuleContent">
                        <img
                            src="../../../assets/images/search.png"
                            @click="chooseOrg"
                            alt=""
                            style="
                                width: 32px;
                                height: 27px;
                                margin-left: 6px;
                                cursor: pointer;
                                margin-top: -3px;
                            "
                        />
                    </div>
                </template>
                <template v-slot:rateRuleZW="{ obj }">
                    <div slot="content" class="rateRuleContent">
                        <img
                            src="../../../assets/images/search.png"
                            @click="chooseOrgZW"
                            alt=""
                            style="
                                width: 32px;
                                height: 27px;
                                margin-left: 6px;
                                cursor: pointer;
                                margin-top: -3px;
                            "
                        />
                    </div>
                </template>
                <template v-slot:enabled="{ obj }">
                    <div>{{ obj.row.enabled==true?'是':'否' }}</div>
                </template>
			</sb-el-table>
		</div>
        <!-- 选择所属组织 -->
        <ConfigDialog :item="orgAllocatData" @chooseData="chooseData"/>
        <!-- 选择职务 -->
        <ConfigDialog :item="zwAllocatData" @chooseData="chooseData" ref="zwAllocatData"/>
	</div>
</template>
<script>
// import { findAll, deleteById, changeStatus, getTableField, getSyncField, batchUpdateField, create, update, findAllDb, saveGroupDataAdd, saveGroupDataUpdate, saveGroupData, delectGroupData, queryGroupList, addGroupJY, deleteGroupById } from '@/api/dataCollect/dataSet';
// import { ListByDataSetId } from '@/api/dataCollect/datasetForm';
import { getUserList,findRootAndNextRoot,findSonByParentOrgId,addUser,updateUserCustom,deleteUserCustom,findByIdUser } from '@/api/system/yhgl.js';
import ConfigDialog from "../component/configDialog";

export default {
	name: 'dataSet',
	props: ["tableType", "tableTitle"],
	components: {
		ConfigDialog
	},
	data() {
		return {
			fieldTableDialog: false, //字段管理弹框
			fieldTableData: [], //字段列表表数据
			quotaChecked: false,
			quotaIndeterminate: false,
			datasetId: '', //数据集id
			table: {
				border: true, // 是否带纵向边框
				loading: false, // 加载中动画
				modulName: 'yhgl-用户信息', // 列表中文名称
				stripe: true, // 是否为斑马条样式
				hasSelect: true, // 是否有复选框
				showIndex: true, // 序号
				data: [], // 数据
				addAndUpdateType: 'dialog',
				total: null,
				hasQueryForm: true, // 是否有查询条件
				queryForm: {
					inline: true,
					labelWidth: '80px',
					labelPosition: 'right',
					formItemList: [
						{ label: '用户账号', key: 'username', type: 'input', clearable: true },
						{ label: '用户姓名', key: 'truename', type: 'input', clearable: true },
						{ label: '移动电话', key: 'preferredMobile', type: 'input', clearable: true },
						{ label: '员工编号', key: 'employeeNumber', type: 'input', clearable: true },
						// { label: '邮箱', key: 'email', type: 'input', clearable: true },
					],
				},
				tr: [
					{ id: 'username', label: 'OA账号', prop: 'username', width:'100' },
					{ id: 'truename', label: '用户姓名', prop: 'truename' },
					{ id: 'preferredMobile', label: '移动电话', prop: 'preferredMobile',  },
					{ id: 'belongDepartmentName', label: '所属部门', prop: 'belongDepartmentName',  },
					{ id: 'belongOrgName', label: '所属组织', prop: 'belongOrgName',   },
					{ id: 'enabled', label: '是否可用', prop: 'enabled',   show: 'template',width:'80' },
					{ id: 'employeeNumber', label: '员工编号', prop: 'employeeNumber',   },
					{ id: 'email', label: '邮箱', prop: 'email',   },
				],
				multipleSelection: [], //多选选中数据存放变量
				dialogVisible: false, //默认对话框关闭
				sex: [], //
				nation: [],
				userType: [],
				staffType: [],
				form: {
					width: '900px',
					labelWidth: '200px',
					inline: true,
					labelPosition: 'right',
					formItemList: [
						{ class: 'c5', label: '所属组织', key: 'belongOrgName', type: 'input', rule: { required: true }, clearable: true },
                        { class: 'c0-5',label: '', key: 'rateRule',type: 'template', template: 'rateRule', clearable: true},
						{ class: 'c6', label: 'OA帐号', key: 'username', type: 'input', clearable: true },
						{ class: 'c6', label: '用户姓名', key: 'truename', type: 'input', rule: { required: true }, clearable: true},
						{ class: 'c6', label: '用户昵称', key: 'nickname', type: 'input',rule: { required: true }, clearable: true},
						{ class: 'c6', label: '性别', key: 'genderDictValue', type: 'select', dictType: "sex" , from:true, clearable: true},
						{ class: 'c6', label: '民族', key: 'nationDictValue', type: 'select',  dictType: "nation" ,from:true, clearable: true},
						{ class: 'c6', label: '出生日期', key: 'birthday', type: 'input', clearable: true  },
						{ class: 'c6', label: '国籍', key: 'country', type: 'input', clearable: true  },
						{ class: 'c6', label: '身份证号', key: 'idCardNumber', type: 'input', clearable: true  },
						{ class: 'c6', label: '用户类型', key: 'userType', type: 'select',rule: { required: true }, dictType: "userType", from:true, clearable: true },
						{ class: 'c5', label: '职务', key: 'positionIdName', type: 'input', rule: { required: true }, clearable: true },
                        { class: 'c0-5',label: '', key: 'rateRuleZW',type: 'template', template: 'rateRuleZW', clearable: true},
						{ class: 'c6', label: '员工类型', key: 'employeeTypeDictValue', type: 'select', rule: { required: true } ,dictType: "staffType", from:true, clearable: true},
						{ class: 'c6', label: '移动电话', key: 'preferredMobile', type: 'input', rule: { required: true }, clearable: true },
						{ class: 'c6', label: '电子邮箱', key: 'email', type: 'input', clearable: true  },
                        { class: 'c6', label: '账号生效时间', key: 'startTime', type: 'date', clearable: true },
						{ class: 'c6', label: '账号失效时间', key: 'endTime', type: 'date', clearable: true },
						{ class: 'c6', label: '账号状态', key: 'status', type: 'select', options:[{name:'正常',value:"0"},{name:'注销',value:"03"}], defaultValue: '0', clearable: true },
						{ class: 'c6', label: '是否内部用户', key: 'isCmcc', type: 'select', options:[{name:'是',value:true},{name:'否',value:false}], defaultValue: false, clearable: true},
						{ class: 'c6', label: '是否显示', key: 'isDisplay', type: 'select', options:[{name:'是',value:true},{name:'否',value:false}], defaultValue: true, clearable: true},
						{ class: 'c6', label: '显示顺序', key: 'displayOrder', type: 'input', clearable: true},
                        { class: 'c6', label: '入职日期', key: 'entryTime', type: 'date', clearable: true },
						{ class: 'c6', label: '是否是特殊账号', key: 'isSpecial', type: 'select', options:[{name:'是',value:true},{name:'否',value:false}], defaultValue: true, clearable: true},
						{ class: 'c6', label: '定义用户的职务', key: 'duty', type: 'input', clearable: true },
						{ class: 'c6', label: '密码是否过期', key: 'credentialsNonExpired', type: 'select', options:[{name:'是',value:true},{name:'否',value:false}], defaultValue: false, clearable: true},
						{ class: 'c6', label: '微信关联ID', key: 'openId', type: 'input', clearable: true },
						{ class: 'c6', label: '定义用户的业务职责编码', key: 'functionName', type: 'input', clearable: true },
						{ class: 'c6', label: '是否接入综合信息网', key: 'isJoinUpNet', type: 'select', options:[{name:'是',value:'是'},{name:'否',value:'否'}], defaultValue: '是', clearable: true},
						{ class: 'c6', label: '接入系统', key: 'joinSystem', type: 'input', clearable: true },
						{ class: 'c6', label: '政治面貌', key: 'religionDictValue', type: 'input', clearable: true },
						{ class: 'c6', label: '所属地市名称', key: 'l', type: 'input', clearable: true },
						{ class: 'c6', label: '通讯地址', key: 'postalAddress', type: 'input', clearable: true },
						{ class: 'c6', label: '邮政编码', key: 'postalCode', type: 'input', clearable: true },
						{ class: 'c6', label: '岗位名称', key: 'levelNamel', type: 'input', clearable: true },
						{ class: 'c6', label: '岗位职级', key: 'levelNumber', type: 'input', clearable: true },
                        { class: 'c6', label: '借调时间', key: 'secondTime', type: 'date', clearable: true },
						{ class: 'c6', label: '办公电话', key: 'telephoneNumber', type: 'input', clearable: true },
						{ class: 'c12', label: '预留扩展字段4', key: 'reserve4', type: 'input', clearable: true },
						{ class: 'c12', label: '备注', key: 'description', type: 'input',inputType: 'textarea', clearable: true },
					],
				},
				listFormModul: {},
				hasOperation: true, //是否有操作列表
				operation: {
					width: '160',
					data: [
						{ id: 'add', name: '新增',beforeFun: "handleAddBef", fun: 'handleAddData' },
                        { id: 'read', name: '查看', fun: 'handleUpDataGetRow' },
						{ id: 'update', name: '编辑', fun: 'handleUpData', beforeFun: "handleUpDataGetRow" },
						{ id: 'delete', name: '删除', fun: 'handleDelete'},
					],
				},
				hasPagination: true,
				listQuery: { size: 10, page: 1, },
				hasBatchOperate: false, //有无批量操作
				batchOperate: {},
				hasOtherQueryBtn: true,
				otherQueryBtn: {
					data: [
                        {
                            id: 'export',
                            name: '导出 ',
                            fun: 'exportSelect',
                        },
                    ],
				},
				hasGroupTabs: true,
				tabsList: [],
				tabsPosition: '全部',
			},
			dbName:"",
			dbArr: [],
			idS: [], //当前选中分组ids
			selectIndex:null,  //搜索结果,被选中的li index
            data: [],
			treeExpandData: [],
            defaultProps: {
                children: 'children',
                label: 'label'
            },
			orgAllocatData: {
				type:'zuzhi',
				inputType: 'text',
				title: '选择组织',
				appendShow: true,
				rows: 12,
				btnText: '搜索',
				mulitple: false,
				dialogVisible: false,
				defaultProps: {
					children: "children",
					label: "displayName",
					isLeaf: 'leaf',
				},
			},
			zwAllocatData: {
				type:'zhiwu',
				inputType: 'text',
				title: '选择职位',
				appendShow: true,
				rows: 12,
				btnText: '搜索',
				mulitple: true,
				dialogVisible: false,
				defaultProps: {
					children: "children",
					label: "name",
					isLeaf: 'leaf',
				},
			},
		};
	},
	activated() {
		
	},
	created(){
		this.table.listQuery.orgCode = '9999000100000000000'
		this.getList();
	},
	methods: {
		//选择配置信息回显
		chooseData(array,type) {
			let org = ''
			let ids = ''
			let authPositions = []
			if (type == 'zuzhi') {
				org = array[0].orgName
				this.table.listFormModul.belongOrgName = org;
				this.table.listFormModul.belongOrgCode = array[0].orgCode;
			}
			if (type == 'zhiwu') {
				for(var i=0;i<array.length;i++) {
					org = (org + ',' +array[i].name);
					ids = (ids + ',' +array[i].id);
				}
				authPositions.push(
					{
						id: ids.slice(1),
						positionName: org.slice(1)
					}
				)
				this.table.listFormModul.authPositions = authPositions
				this.table.listFormModul.positionIdName = org.slice(1);
				this.table.listFormModul.positionId = ids.slice(1);
				// this.table.listFormModul.userPosition = array;
			}
		},
		handleNodeClick(node,data) {
			var params = Object.assign(this.table.listQuery,{orgCode: node.data.orgCode}) 
			this.getList(params)
		},
		loadNodeOrg(node, resolve) {
			if (node.level === 0) {
				this.treeExpandData = ["1"]
				resolve([{
					"id": "1",
					"orgCode": "9999000100000000000",
					"orgName": "北京晟壁",
				}])
			} else {
				var params = {
					orgCode: node.data.orgCode
				}
				findSonByParentOrgId(params).then(({ data }) => {
				data.forEach((target) => {
					target.treeType == 'user' && Reflect.set(target, 'leaf', true)
				})
				resolve(data)
				})
			}
		},
        // 点击组织树节点
        
        //获取tree树列表
        chooseOrg() {
            this.orgAllocatData.dialogVisible = true;
        },
        //获取tree树列表
        chooseOrgZW() {
            this.zwAllocatData.dialogVisible = true;
			this.$refs.zwAllocatData.searchNames()
        },
		
		onTreeNodeClick(data,index){
			this.selectIndex = index;
			this.table.listQuery.dataSourceId = data.id;
			this.getList();
		},
		// 查询列表
		getList(params) {
			this.table.loading = true;
			getUserList(params || this.table.listQuery).then((res) => {
				this.table.loading = false;
				this.table.data = res.data&&res.data.content?res.data.content:[];
				this.table.total = res.data.totalElements;
			}).catch((err) => {
				this.table.loading = false;
			});
		},
		handleAddBef(){
			this.table.operation.nowBtn = true;
			this.table.form.formItemList.forEach((item)=>{
				item.disabled = false;
			})
			return true
		},
		// 新增
		handleAddData() {
			this.table.listFormModul.type = this.tableType
			this.table.listFormModul.accountNonExpired = true
			this.table.listFormModul.accountNonLocked = true
			addUser(this.table.listFormModul).then(res => {
				this.table.dialogVisible = false
				if (res.status == 200){
					this.getList();
				} 
			})
		},

		// 根据id查询行数据
		handleUpDataGetRow(row) {
			this.table.form.formItemList.forEach((item)=>{
				item.disabled = row.read;
			})
			this.table.listFormModul = row
			this.table.listFormModul.genderDictValue = this.table.listFormModul.genderDictValue+'';
			this.table.listFormModul.employeeTypeDictValue = this.table.listFormModul.employeeTypeDictValue+'';
		},
		// 编辑
		handleUpData() {
			updateUserCustom(this.table.listFormModul).then(res => {
				this.table.dialogVisible = false
				if (res.status == 200) this.getList();
			})
		},
		// 删除
		handleDelete(row) {
			deleteUserCustom(row.id).then((res) => {
				this.getList();
			});
		},
		
		// 修改是否启用
		handleChangeEnable(index, row) {
			// changeStatus({ id: row.id, enableStatus: row.enableStatus }).then((res) => { }).catch((error) => {
			// 	this.table.data[index].enableStatus = this.table.data[index].enableStatus;
			// });
		},
		
	},
};
</script>
<style scoped>
.table-name {
    font-weight: bold;
}
.container-left {
    width: 300px;
    height: 100%;
    overflow: auto;
    background-color: var(--el-bg-color-overlay);
    border: 1px solid var(--el-border-color-light);
    border-radius: 4px;
    box-shadow: var(--el-box-shadow-light);
}
.container-right {
    width: calc(100% - 300px);
    height: 100%;
	position: relative;
}
::-webkit-scrollbar {
    display: none;
}
::v-deep .el-card__header{
	padding: 8px 15px;
}
.treeData{
	width:100%;
	display:flex;
	justify-content: space-between;
	font-size: 15px;
	height: 30px;
	line-height: 30px;
	color: #444;
	padding-left: 8px;
}
.treeData .text{
	width: 215px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	/* padding: 5px; */
	cursor: pointer;
}
.treeData .text:hover{
	color: rgba(192,0,0,1);
}
.treeData .execute{
	width: 70px;
	color: rgba(192,0,0,1);
	cursor: pointer;
	text-align: right;
}
.treeData .execute:hover{
	text-decoration: underline;
}
::v-deep .el-card__body{
	padding: 0 15px;
	padding-bottom: 15px;
}
.head-container{
	height: calc(100vh - 180px);
	overflow-y: auto;
    padding-top: 20px;
}
::v-deep .el-table__body-wrapper{
	max-height: calc(100vh - 320px);
	overflow-y: auto;
}
.acceptanceBox{
    position: relative;
	/* height: 250px; */
}
.footer{
    width: 100%;
    /* position: absolute; */
    left: 0;
    bottom: 0;
    text-align: center;
    margin-top: 20px;
	margin-bottom: 20px;
}
.selectedColor {
	background-color: #eeeeee;
}
.rateRuleContent {
	margin-right: 22px;
}
</style>
