<template>
  <div class="dataScreen">
    <div class="screen" id="screen">
      <div class="top_b">
        <img src="./images/top_bg.png" alt="" />
      </div>
      <div class="content flexb">
        <div class="left_b"></div>
        <div class="mid_b"></div>
        <div class="right_b"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "dataScreen",
  data() {
    return {};
  },

  mounted() {
    // 初始化自适应  ----在刚显示的时候就开始适配一次
    this.handleScreenAuto();
    // 绑定自适应函数   ---防止浏览器栏变化后不再适配
    window.onresize = () => this.handleScreenAuto();
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;

    console.log(`浏览器屏幕宽度: ${screenWidth}px`);
    console.log(`浏览器屏幕高度: ${screenHeight}px`);
  },
  created() {},
  methods: {
    handleScreenAuto() {
      const designDraftWidth = 1980; //设计稿的宽度
      const designDraftHeight = 945; //设计稿的高度
      //根据屏幕的变化适配的比例
      const scale =
        document.documentElement.clientWidth /
          document.documentElement.clientHeight <
        designDraftWidth / designDraftHeight
          ? document.documentElement.clientWidth / designDraftWidth
          : document.documentElement.clientHeight / designDraftHeight;
      //缩放比例
      document.querySelector(
        "#screen"
      ).style.transform = `scale(${scale}) translate(-50%)`;
    },
  },
};
</script>

<style scoped>
.screen {
  display: inline-block;
  width: 1980px;
  /* height: 945px; */
  height: 963px;
  transform-origin: 0 0;
  background-image: url("./images/bg.png");
  position: absolute;
  left: 50%;
}
.top_b {
  width: 100%;
  margin-top: 10px;
  /* background-color: #fff; */
  position: relative;
}
.top_b img {
  width: 100%;
}
.flexb {
  display: flex;
  justify-content: space-between;
}
.content {
  width: 100%;
  height: calc(100% - 70px);
  /* height: 100%; */
  padding: 10px;
  padding: 0 50px;
}
.content .left_b,
.content .mid_b,
.content .right_b {
  /* width: 33%;
  height: 100%;
  padding: 10px; */
  border-radius: 5px;
}
.left_b {
  width: 28%;
  height: 85%;
  background-image: url("./images/left_bg.png");
  background-repeat: no-repeat;
  background-size: cover; /* 背景图像覆盖容器 */
  background-position: center;
}
.mid_b {
  width: 47%;
  height: 85%;
}
.right_b {
  width: 25%;
  height: 85%;

  background-image: url("./images/right_bj.png");
  background-repeat: no-repeat;
  background-size: cover; /* 背景图像覆盖容器 */
  background-position: center;
}
</style>