import Vue from "vue";
import Router from "vue-router";
import Layout from "@/views/layout/Layout";

Vue.use(Router);

// const originalPush = Router.prototype.push
// Router.prototype.push = function push(location, onResolve, onReject) {
//   if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject)
//   return originalPush.call(this, location).catch(err => err)
// }
function dynamicPropsFn(route) {
	return {
		dialogClose: function () {
			window.opener = null;
			window.open("", "self");
			window.close();
		}
	}
}
const Login = () => import('@/views/login/Login');
const Login2 = () => import('@/views/login/Login2');
export const constantRouterMap = [
	{
		path: "/login",
		name: "login",
		component: process.env.VUE_APP_Type == 1 ? Login : Login2,
		meta: { title: "登录", icon: "login" },
		hidden: true,
	},
	{
		path: "/redirect",
		name: "redirect",
		component: () => import("@/views/redirect"),
		hidden: true,
	},
	{
		path: "",
		component: Layout,
		name: "processTask",
		redirect: "/mywork/processTask",
		children: [
			{
				path: "/mywork/processTask",
				name: "processTask",
				component: () => import("@/views/mywork/processTask"),
				meta: { title: "我的待办", icon: "wodegongzuo" },
			},
			{
				path: "/welcome",
				name: "welcome",
				component: () => import("@/views/welcome"),
				meta: { title: "首页", icon: "welcome" },
				hidden: true,
			},
		],
	},
	{
		path: "/workOrder",
		name: "workOrder",
		component: () => import("@/components/WorkOrder"),
		meta: { title: "工单详情", icon: 'home' },
		// props: dynamicPropsFn
	},
	{
		path: "/dataScreen",
		name: "dataScreen",
		component: () => import("@/views/home/<USER>"),
		hidden: true,
	},
	{
		path: "/Screen",
		name: "Screen",
		component: () => import("@/views/index"),
		hidden: true,
	},
	{
		path: "/djptScreen",
		name: "djptScreen",
		component: () => import("@/views/screen"),
		hidden: true,
	},
	{
		path: "/djxxpt",
		name: "djxxpt",
		component: () => import("@/views/djxxpt"),
		hidden: true,
	},
];
export default new Router({
	routes: constantRouterMap
});
