<template>
  <div :class="gps.location ? 'w99' : 'p10'">
    <div class="pageInfo" v-if="!gps.action">
			<span v-show="nextBtn" class="btn nextBtn" @click="handleNextBtn()"><svg-icon icon-class="random"></svg-icon>
				<font>{{ gps.pmInsType == 'E' ? '提交' : '已阅' }}</font>
			</span>
      <span class="btn optClose" @click="handleOptClose()"><svg-icon icon-class="close"></svg-icon>
				<font>关闭</font>
			</span>
    </div>

    <!-- 业务表单 -->
    <div class="message tableForm">
      <div class="orderTitle" style="">豫起奋发 更加出彩</div>
      <div class="m-title">
        <span>请您审阅AI周报</span>
      </div>
      <sb-el-form ref="appForm" :form="appForm" v-model="appFormValue" :disabled="appForm.formDisabled" :on-ok="handleDoFun" @uploadFileList="uploadFileList">
      </sb-el-form>
      <div class="m-title flex a-c j-s">
        <span>{{ fileNameTitle }} 预览</span>
        <el-button type="primary" size="small" @click="downLoadHandle">下载</el-button>
      </div>
      <iframe-com v-if="previewUrl" :url="previewUrl" :fileType="fileType"></iframe-com>
    </div>

    <!-- 流程图 -->
    <el-dialog title="流程图" :visible.sync="diagramD" v-dialogDrag :close-on-click-modal="false" append-to-body width="1000px">
      <process-diagram :key="diagramKey" :gps="gps"></process-diagram>
    </el-dialog>

    <!-- 选择人员、部门 -->
    <el-dialog title="选择人员" :visible.sync="showUserDialog" v-dialogDrag :close-on-click-modal="false" append-to-body top="10vh" width="50%">
      <chooseUser v-if="showUserDialog" pmInsType="B" :item="itemUser" :key="itemUser.key" @closeshowDialogFun="showUserDialog=false" :cd="cd"
                  @flowdata="flowdata"/>
    </el-dialog>
  </div>
</template>
<script>
import {
  uploadProcessFiles,
} from "@/api/public";

import {
  startProcess
} from "@/api/apply/application";
import {
   AiDetail,AiNext
} from "@/api/branchCompany";

let defaultAppFormValue = {
  pmInsId: "",
  id: "",
  blank: "blank"
};

import ProcessNext from '@/components/Process/ProcessNext.vue'
import ProcessDiagram from "@/components/Process/ProcessDiagram";
import chooseUser from "@/components/chooseUser";
import iframeCom from "@/components/iframeCom.vue";


export default {
  name: "application",
  props: {
    href: {
      type: Object,
      default() {
        return {};
      }
    },
    showBtn: {
      type: Object,
      // default(){
      // 	return {};
      // }
    },
    // 流程跟踪
    doFlowTrack: {
      type: Function
    },
    // 查看意见
    doViewComments: {
      type: Function
    },
    // 关闭
    dialogClose: {
      type: Function
    },
    types: {
      type: String,
      default: ''
    },
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  components: {
    ProcessNext,
    ProcessDiagram,
    chooseUser,
    iframeCom
  },
  data() {
    return {
      gps: this.href,
      pmInsId: '',

      processD: false,
      pnKey: 0,
      clickFlag: true, //防止多次点击

      nowTime: this.util.getNow("yyyy-MM-dd hh:mm:ss"),

      showUserDialog: false,
      itemUser: {key: 0, nodeKey: 'id', mulitple: true, type: '3'}, // type 1 责任人 2 责任主管 3 经办人
      cd: [],

      // 业务表单
      initValue: {},
      appFormValue: Object.assign({}, defaultAppFormValue),
      appForm: {
        formDisabled: false,
        labelWidth: "200px",
        inline: true,
        formItemList: [
          {class: "c4", label: "办理人", key: "applyTrueName", type: "input", disabled: true},
          {class: "c4", label: "周报统计开始时间", key: "startDate", type: "input", disabled: true},
          {class: "c4", label: "周报统计截止时间", key: "endDate", type: "input", disabled: true},
          {class: "c12", label: "本次填报情况概览", key: "situation", type: "input", inputType: 'textarea', disabled: true, autosize: {minRows: 2}},
          {
            class: "c12",
            label: "是否需要修改",
            key: "isUpdate",
            type: "radio",
            options: [{name: '是', value: 1}, {name: '否', value: 0}],
            changeFun: 'radioChange',
            show: true
          },
          {
            class: "c12",
            label: "AI周报附件",
            key: "sysFiles",
            type: "sbUpload",
            btnText: "修改并上传",
            fun: "uploadFileList",
            listType: "text",
            multiple: false,
            rule: {required: true},
            show: false,
            isfile: true,
            accept: '.doc, .docx'
          }
        ],
      },

      formData: {},

      // 流程图
      diagramKey: 0,
      diagramD: false,
      isUpload: false, // 是否修改过附件
      fileNameTitle: '',
      fileType:'',
      orderId: '', // 工单id
      previewUrl: '' // 预览地址
    }
  },
  computed: {
    processImg() {
      return !this.gps.modify && (this.gps.type == "task" || this.gps.type == "join" || this.gps.type == "toRead" || this.gps.type == "doRead")
    },
    nextBtn() {
      return (!this.gps.modify && (this.gps.type == "task" || this.gps.type == "toRead"))
    },
  },
  created() {
    var query = this.util.getQueryString();
    this.gps = Object.assign(this.gps, query);
    // console.log('gps', JSON.parse(JSON.stringify(this.gps)));

    setTimeout(() => {
      let height
      if (this.gps.myFrom && this.$router.currentRoute.path == "/workOrder") {
        height = window.innerHeight - 70
      } else {
        height = window.innerHeight - 130
      }
      let dom = document.querySelector('.tableForm')
      dom.setAttribute('style', `max-height: ${height}px;overflow: auto;`)
    }, 0)

    if (this.gps.pmInsType == 'F') {
      this.appForm.formItemList[4].show = false
    }

    // this.initValue = {
    //   applyUser: this.$store.getters.user.truename,
    //   applyUserName: this.$store.getters.user.username,
    //   belongCompanyName: this.$store.getters.user.belongCompanyName,
    //   belongDepartmentName: this.$store.getters.user.belongDepartmentName,
    //   applyPhone: this.$store.getters.user.preferredMobile,
    //   applyTime: this.nowTime
    // };
    // this.appFormValue = Object.assign({}, defaultAppFormValue, this.initValue);

    this.initFun(); //初始化
  },
  methods: {
    downLoadHandle() {
      window.open(process.env.VUE_APP_PROBASEURL + this.appFormValue.sysFiles?.[0].downLoadUrl, "_blank")
    },
    radioChange(item, val) {
      if (!val) {
        this.appForm.formItemList[5].show = false
      } else {
        this.appForm.formItemList[5].show = true
      }
    },
    uploadFileList(obj) {
      this.previewUrl = ''
      uploadProcessFiles(obj.formData).then(async res => {
        obj.content.onSuccess(res, obj.content.file, []);
        this.isUpload = true
        this.fileNameTitle = res.data.sysFiles[0].fileName
        this.fileType=res.data.sysFiles[0].fileType
        this.previewUrl = this.util.getApiUrl() + "/sys/file/open?id=" + res.data.sysFiles[0].id
      }).catch(error => {
        obj.content.onError();
      });
    },
    // 初始化
    async initFun() {
      this.loadForm();
    },
    // 获取工单详情
    loadForm() {
      var data = {
        pmInsId: this.gps.pmInsId,
        taskId: this.gps.taskId,
        location: this.gps.location
      };
      AiDetail(data).then((res) => {
        if (!res.data.distributeInfo.sysFiles) {
          res.data.distributeInfo.sysFiles = JSON.parse(JSON.stringify(res.data.distributeInfo.oldSysFiles))
          this.fileNameTitle = res.data.distributeInfo.oldSysFiles?.[0].fileName
          this.fileType=res.data.distributeInfo.oldSysFiles?.[0].fileType
          this.previewUrl = this.util.getApiUrl() + "/sys/file/open?id=" + res.data.distributeInfo.oldSysFiles?.[0]?.id
        } else {
          this.fileNameTitle = res.data.distributeInfo.sysFiles?.[0].fileName
          this.fileType= res.data.distributeInfo.sysFiles?.[0].fileType
          this.previewUrl = this.util.getApiUrl() + "/sys/file/open?id=" + res.data.distributeInfo.sysFiles?.[0]?.id
        }

        this.orderId = res.data.distributeInfo.id
        Object.assign(this.appFormValue, res.data?.distributeInfo)

        if (this.gps.type == 'join') {
          this.appForm.formDisabled = true
          //单独处理上传附件
          for (var i = 0; i < this.appForm.formItemList.length; i++) {
            if (this.appForm.formItemList[i].type == "sbUpload") {
              this.appForm.formItemList[i].disabled = true;
            }
          }
        }
      });
    },
    //封装验证函数
    submitForm(formUser) {
      return new Promise((resolve, reject) => {
        if (this.$refs[formUser]) {
          this.$refs[formUser].$children[0].validate((valid) => {
            if (valid) {
              resolve()
            } else {
              reject(new Error('错误'))
            }
          })
        } else {
          resolve()
        }
      })
    },
    // 流转下一步
    handleNextBtn() {
      Promise.all([this.submitForm('appForm')])
          .then(() => {
            let data = JSON.parse(JSON.stringify(this.appFormValue))
            let formData = {
              id: this.orderId,
              pmInsId: this.gps.pmInsId,
              distributeInfo: Object.assign(data, !data.isUpdate ? {sysFiles: null} : {})
            }
            let params = {location: this.gps.location, pmInsId: this.gps.pmInsId, taskId: this.gps.taskId}

            this.$confirm(this.gps.pmInsType == 'E' ? '请您确认周报内容无误，点击确认按钮完成提交' : '请确认是否归档', '提示', {
              cancelButtonText: '取消'
            }).then(async () => {
              await AiNext(params, formData)
              this.afterClick()
            }).catch(async () => {

            })
          })
          .catch(() => {
            this.$message.warning('表单数据校验不通过')
          })
    },
    afterClick() {
      if (this.gps.myFrom && this.$router.currentRoute.path == "/workOrder") { //单点
       if(this.gps.flushPortalUrl){
            // 集团单点流转
            var flushPortalUrl = decodeURIComponent(this.gps.flushPortalUrl);
            var params = {
                "appcode": this.gps.appcode,
                "uniqueId": this.gps.uniqueId,
                "itemId": this.gps.itemId,
            }
            var pageUrlNew = this.util.toUrl(flushPortalUrl,params);
            window.location.replace(pageUrlNew)
          }else{
             window.opener = null;
            window.open("", "_self");
            window.close();

          }
      } else {
        if (!this.gps.location) {
          this.$router.push({
            name: "processTask"
          });
        } else {
          this.dialogClose();
        }
      }
    },
    // 确认
    handleConfirm() {
      // 判断流程下一步页面数据是否加载完
      var isLoad = this.$refs["processNext"].isLoad;
      // console.log(isLoad);

      if (!isLoad) {
        return false;
      }

      var decisionUser = this.$refs["processNext"].decisionUser;
      var decisionData = this.$refs["processNext"].decisionData;
      var choosedUser = this.$refs["processNext"].choosedUser;
      // console.log('已选决策项',JSON.parse(JSON.stringify(decisionData)))
      // console.log('人员',JSON.parse(JSON.stringify(decisionUser)))
      // console.log('已选人员',JSON.parse(JSON.stringify(choosedUser)))

      var flag = true;
      var gName = "";
      for (var i in decisionUser) {
        for (var j in decisionUser[i]) {
          if ((decisionUser[i][j].requSel === true || decisionUser[i][j].requSel === "true") && choosedUser[i][j].length == 0) {
            flag = false;
            if (i == "copy" && decisionUser[i][j].group != "normalGrouping") {
              gName = decisionData[i].decisionName.split("#")[1] + decisionUser[i][j].group;
            }
            break;
          }
        }
      }

      if ((!flag && decisionData["main"].decisionId.indexOf("_end") == -1) || !decisionUser.main.length) {
        this.$message({
          message: "清选择" + gName + "审批人",
          type: "warning",
          duration: 1500
        });
        return false;
      }

      var nextUser = [],
          nextUserName = [],
          nextUserOrgCode = [],
          nextUserPostId = [];
      for (var i in choosedUser["main"]) {
        for (var j in choosedUser["main"][i]) {
          nextUser.push(choosedUser["main"][i][j].id);
          nextUserName.push(choosedUser["main"][i][j].name);
          nextUserOrgCode.push(choosedUser["main"][i][j].parentId);
          nextUserPostId.push("123");
        }
      }

      var data = {
        appCode: process.env.VUE_APP_APPCODE,
        type: !this.gps.location || (this.gps.type == "draft" && this.gps.location == process.env.VUE_APP_APPCODE + ".start") ? "START" : "FLOW",
        title: this.appFormValue.title || "",
        processDefKey: this.gps.processDefKey || "",
        processDefId: this.gps.processDefKey || "",
        pmInsType: this.gps.pmInsType || "",
        outcome: decisionData["main"].decisionId,
        taskDefinitionKey: decisionData["main"].targetActivityDefId,
        message: this.$refs["processNext"].opinion,
        nextUser: nextUser.join(","),
        nextUserName: nextUserName.join(","),
        nextUserOrgCode: nextUserOrgCode.join(","),
        nextUserPostId: nextUserPostId.join(","),
        nextActivityParam: decisionData["main"].nextActivityParam,
        formData: this.formData
      };
      data.activityDefId = this.gps.location ? this.gps.location : process.env.VUE_APP_APPCODE + ".start";
      if (this.gps.taskId) data.taskId = this.gps.taskId;
      if (this.gps.processInstId) data.processInstId = this.gps.processInstId;
      // if(this.gps.taskDefinitionKey) data.taskDefinitionKey = this.gps.taskDefinitionKey;
      if (this.gps.processDefinitionId) data.processDefinitionId = this.gps.processDefinitionId;

      this.processD = false;
      startProcess(data).then((res) => {
        if (this.gps.myFrom && this.$router.currentRoute.path == "/workOrder") { //单点
          if(this.gps.flushPortalUrl){
            // 集团单点流转
            var flushPortalUrl = decodeURIComponent(this.gps.flushPortalUrl);
            var params = {
                "appcode": this.gps.appcode,
                "uniqueId": this.gps.uniqueId,
                "itemId": this.gps.itemId,
            }
            var pageUrlNew = this.util.toUrl(flushPortalUrl,params);
            window.location.replace(pageUrlNew)
          }else{
             window.opener = null;
            window.open("", "_self");
            window.close();

          }
        } else {
          if (!this.gps.location) {
            this.$router.push({
              name: "processTask"
            });
          } else {
            this.dialogClose();
          }
        }
      });
    },
    // 关闭
    handleOptClose() {
      if (this.dialogClose) {
        //待办打开
        this.dialogClose();
      } else if (this.gps.myFrom && this.$router.currentRoute.path == "/workOrder") {//单点
        //单点
        window.opener = null;
        window.open("", "_self");
        window.close();
      } else {
        // let item = this.tabnav.find(item => item.path === this.$route.path);
        // this.$store.dispatch("CloseTabnav", item).then(res => {
        //   if (item.path === this.$route.path) {
        //     const lastTag = res.slice(-1)[0];
        //     // 前一个 tab-view 页面存在，就跳；不存在就到首页
        //     if (lastTag) {
        //       this.$router.push({ path: lastTag.path });
        //     } else {
        //       this.$router.push({ path: "/mywork/processTask" });
        //     }
        //   }
        // });
      }
    },
    // 流程跟踪
    handleFlowTrack() {
      this.doFlowTrack();
    },
    // 查看意见
    handleViewComments() {
      this.doViewComments();
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }

};
</script>
<style scoped>

::v-deep .el-radio-group {
  padding-left: 15px;
}

::v-deep .el-input__count {
  background: rgba(247, 234, 233, 0);
}

.red {
  color: rgba(192, 0, 0, 1);
  cursor: pointer;
}


::v-deep .el-textarea__inner {
  min-height: 48px !important;
}

::v-deep .el-input-group__append {
  background-color: #fff3f3;
  border-color: #fff3f3;
}

::v-deep .upload_D {
  min-width: 100%;
}

::v-deep .upload_Btn .uploadB {
  right: 0;
  left: auto;
}

.m-title {
  width: 100%;
  padding-left: 10px;
  border-left: 4px solid rgba(192, 0, 0, 1);
  padding: 10px;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  border-bottom: 1px solid #ebebeb;
  border-right: 1px solid #ebebeb;
}
</style>