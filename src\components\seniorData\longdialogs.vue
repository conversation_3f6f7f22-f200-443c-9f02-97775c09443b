<template>
  <div class="w100 inputBtn">
    <el-input ref="elInput" :type="item.inputType || 'text'" v-bind="$attrs" v-on="$listeners" :size="item.size || 'small'" :placeholder="item.placeholder || item.label || '请输入'" :disabled="item.disabled || false" :readonly="item.readonly || false" :autosize="item.autosize || false">
      <el-button slot="append" :size="item.size || 'small'" type="primary" :disabled="item.disabled || false" @click="openDialog">{{ item.btnText }}
        <svg-icon v-if="!item.btnText" iconClass="sousuo"></svg-icon>
      </el-button>
    </el-input>

    <el-dialog title="选择数据" :visible.sync="dialogVisible" v-dialogDrag append-to-body destroy-on-close width="60%">
      <sb-el-table :table="table" @getList="getList" @updateTableData="updateTableData" :on-ok="handleDoFun"></sb-el-table>
      <div style="display: flex;
    justify-content: flex-end;
    padding: 10px 0px 20px 0px;">
        <el-button @click="cancelFilter">取 消</el-button>
        <el-button type="primary" @click="saveFilter">确 定</el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import { getDataByApi } from "@/api/apply/application";
import util from "@/assets/js/public";
export default {
  name: "longdialogs",
  props: {
    item: {
      type: Object,
      required: true
    },
    appFormValue: {
      type: Object,
    },
    dialogData: {
      type: Object,
    }
  },
  data() {
    return {
      dialogVisible: false,
      table: {
        modulName: "dialogs-选择数据", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: true, // 是否有复选框
        showIndex: false, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: true, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "90px",
          formItemList: [
            { label: "关键词", key: "appName", type: "input" },
            // {label: "流程类型",key: "pmInstType",type: "select",dictType: "processType"}
          ],
        },
        tr: [],
        // hasSetup:true,
        // setup:[],
        processType: [],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "600px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: false, //是否有操作列表
        operation: {
          width: "100",
          fixed: "right",
          data: [
          ],
        },
        hasPagination: true,
        listQuery: {},
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
    };
  },
  created() {

  },
  methods: {
    openDialog(data) {
      this.getTableInfo()
      this.dialogVisible = true
    },
    getTableInfo() {
      if (this.dialogData.options.length > 0) {
        this.dialogData.options.forEach((item) => {
          item.id = item.value
          item.label = item.name
          item.prop = item.value
        })
        this.table.tr = this.dialogData.options
      } else {
        this.table.tr = []
      }

      if (this.dialogData.paramsArr.length > 0) {
        this.dialogData.paramsArr.forEach((item) => {
          item.type = "input"
          item.label = item.CHINESE_NAME ? item.CHINESE_NAME : item.API_PARAM_NAME
          item.key = item.API_PARAM_NAME
        })
        this.table.queryForm.formItemList = this.dialogData.paramsArr
      } else {
        this.table.queryForm.formItemList = []
      }
      this.getList();
    },
    // 查询列表
    getList(listQuery) {
      let params = this.dialogData.dialogData
      // let params = Object.assign(this.dialogData.dialogData, this.table.listQuery)
      this.dialogData.parameter.forEach((item) => {
        item.DEFAULT_VALUE = item.formsId ? (this.appFormValue[item.formsId] || this.table.listQuery[item.API_PARAM_NAME]) : this.table.listQuery[item.API_PARAM_NAME]
      })
      // params.params.forEach((item) => {
      //   item.DEFAULT_VALUE = this.table.listQuery[item.API_PARAM_NAME]
      // })
      this.table.loading = true;
      getDataByApi(this.dialogData.dialogData, this.dialogData.parameter).then((res) => {
        this.table.loading = false;
        this.table.data = res.data;
      }).catch((err) => {
        this.table.loading = false;
      });
    },

    saveFilter() {
      let revealss = util.getallValue(this.table.multipleSelection, this.dialogData.reveals)
      let storess = util.getallValue(this.table.multipleSelection, this.dialogData.stores)
      this.$emit('saveFilter', revealss, storess)
    },
    cancelFilter() {
      this.$emit('cancelFilter')
    },


    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },

    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }
};
</script>
<style scoped>
/* 修改公共样式弹框样式 */
::v-deep .el-dialog__header {
  text-align: center !important;
  background: white !important;
  color: black;
  font-size: 14px;
  font-weight: bold;
  border-bottom: 1px solid #f2f2f2 !important;
}
::v-deep .el-dialog__title {
  color: black !important;
  font-size: 15.5px;
}
::v-deep .el-dialog__headerbtn .el-dialog__close {
  color: black;
}
</style>