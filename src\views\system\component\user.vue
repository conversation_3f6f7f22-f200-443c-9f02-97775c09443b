<template>
  <div class="w100 inputBtn">
    <el-button
        v-if="item.appendShow"
        slot="append"
        :size="item.size || 'small'"
        type="primary"
        plain
        :disabled="item.disabled || false"
        style="position:absolute;right:2.5%;top:10%"
        @click="openDialog"
        >{{ item.btnText }}
        <svg-icon v-if="!item.btnText" iconClass="sousuo"></svg-icon>
    </el-button>
    <el-aside width="320px" class="asideR">
        <h5 class="fbold">选择新增用户</h5>
        <div class="choose-department-checked">
          <div class="choose-department-item" v-for="(citem, index) in multipleSelection" :key="citem[item.nodeKey || 'id']">
              <div class="choose-department-item-text ellipsis-line-1">
              {{ citem[item.defaultProps.label || "name"] }}
              </div>
              <span class="choose-department-item-close" @click="delChoose(citem)">x</span>
          </div>
        </div>
        <div class="choose-department-bottom">
          <el-button @click="multipleSelection = []" size="small">清空用户</el-button>
          <el-button type="primary" @click="handleConfirm" size="small"
            >添加用户</el-button
          >
        </div>
    </el-aside>
    <el-dialog
      title="选择人员" 
      v-dialogDrag
      :visible.sync="dialogVisible"
      width="50%"
      append-to-body
      :center="true"
    >
      <el-container class="contwarp">
        <el-main class="warpLeft">
            <div class="card-header">
              <el-input v-model="searchName" placeholder="请输入姓名进行搜索" size="small" style="width:200px" clearable/>
              <el-button class="button" type="primary" style="margin-left:10px" size="small" @click="searchNames()">搜索</el-button>
            </div>
            <el-tree v-if="!trueBtn" class="tree1" :key="clickKey" style="height: 56vh;overflow: auto;padding-bottom: 32px;" :props="item.defaultProps || defaultProps" ref="chooseOrgTree" :default-expanded-keys="treeExpandData" :load="loadNode" lazy :node-key="item.nodeKey || 'id'"  :check-on-click-node="true">
              <!-- :expand-on-click-node="false" -->
              <template #default="{ node, data }">
                <div @click="onTreeNodeClick(node, data)" style="width:100%;height: 100%;line-height: 26px;">{{ data.name }}</div>
              </template>
            </el-tree>
             <el-tree class="tree2" v-if="trueBtn" :key="(clickKey + 1)" style="height: 56vh;overflow: auto;padding-bottom: 32px;" :props="item.defaultProps || defaultProps" ref="chooseOrgTree" :data="filteredTreeData" default-expand-all :highlight-current="true" :node-key="item.nodeKey || 'id'"  :check-on-click-node="true">
              <template #default="{ node, data }">
                <div @click="onTreeNodeClick(node, data)" style="width:100%;height: 100%;line-height: 26px;">{{ data.name }}</div>
              </template>
            </el-tree>
        </el-main>
        <el-aside width="320px" class="asideR">
          <h5 class="fbold">选择新增用户</h5>
          <div class="choose-department-checked">
            <div class="choose-department-item" v-for="(citem, index) in multipleSelection" :key="citem[item.nodeKey || 'id']">
              <div class="choose-department-item-text ellipsis-line-1">
                {{ citem[item.defaultProps.label || "name"] }}
              </div>
              <span class="choose-department-item-close" @click="delChoose(citem)">x</span>
            </div>
          </div>
        </el-aside>
      </el-container>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="small">取消</el-button>
        <el-button type="primary" @click="dialogVisible = false" size="small"
          >确定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template> 
<script>
// import { findOneStep,findDimUserTree } from "@/api/senior";
import { findUserByRole,findOneStep,findDimUserTree } from '@/api/system/jsgl.js';

export default {
  name: "SbChooseOrg",
  props: {
    item: {
      type: Object,
      required: true
    },
    onOk: {
      type: Function
    },
  },
  data() {
    return {
      dialogVisible: false,
      defaultProps: {
        children: "children",
        label: "orgName",
        isLeaf: 'leaf',
      },
      treeData: [],
      multipleSelection: [],
      defaultCheckedKs: [],
      reqs: [],
      treeExpandData: [],
      searchName:"",
      trueBtn:false,
      filteredTreeData:[],
      clickKey: 0,
    };
  },
  methods: {
    searchNames(){
        if(this.searchName.trim()){
          this.trueBtn = true
          this.$nextTick(()=>{
            this.clickKey++;
            var params = {
              truename: this.searchName.trim()
            }
            findDimUserTree(params).then(({ data }) => {
              let newData = this.util.toTreeData(
                  data,
                  'id',
                  'parentId',
                  'id,parentId,name,treeLevel,treeType,orgDisplayName'
              );
              this.filteredTreeData = newData
            })
          })
          
        }else{
          this.trueBtn = false
          this.$nextTick(()=>{
            this.clickKey++;
          })
        }
    },
    loadNode(node, resolve) {
      if (node.level === 0) {
        this.treeExpandData = ['9999000100000000000']
        resolve([{
          "id": "9999000100000000000",
          "name": "北京晟壁",
        }])
      } else {
        findOneStep(node.data.id).then(({ data }) => {
          data.forEach((target) => {
            target.treeType == 'user' && Reflect.set(target, 'leaf', true)
          })
          resolve(data)
        })
      }
    },
    onTreeNodeClick(node, data) {
      if (data.treeType == 'user') {
        if(this.trueBtn){
          data.orgDisplayName = node.parent.data.orgDisplayName
        }
        if (
            (!this.item.mulitple && this.item.mulitple !== false) ||
            this.item.mulitple === true
        ) {
            //多选
            this.writeNodeToLocalChecked(data);
        }else{
            this.multipleSelection = [data]
        }
      }
    },
    writeNodeToLocalChecked(node) {
      if (!this.arrayIsIncludeTheObject(this.multipleSelection, node)) {
        this.multipleSelection.push({ ...node })
      }
    },
    arrayIsIncludeTheObject(arr, obj, key = 'id') {
      return arr.some((every) => Object.is(every[key], obj[key]))
    },


    openDialog(e) {
      // this.trueBtn = false
      this.multipleSelection = [];
      this.dialogVisible = true;
    },
    handleConfirm() {
      this.dialogVisible = false;
      this.$emit("chooseData", this.multipleSelection);
      if (this.onOk && this.item.handleUser) {
        this.onOk(this.item,"handleUser",this.multipleSelection);
      }
    },
    delChoose(row) {
      if (
        (!this.item.mulitple && this.item.mulitple !== false) ||
        this.item.mulitple === true
      ) {
        //多选
        let arry = JSON.parse(JSON.stringify(this.multipleSelection));
        for (let i in arry) {
          if (arry[i][this.item.nodeKey || "id"] === row[this.item.nodeKey || "id"])arry.splice(i, 1);
        }
        this.multipleSelection = arry;
      } else {
        //单选
        this.multipleSelection = [];
        this.$refs.chooseOrgTree.setCheckedKeys([]);
      }
    }
  },
  created() {
    // this.getTreeData();
  }
};
</script>
<style scoped>
.icon {
  margin: 0;
}
.el-dialog__body {
  padding: 0px 20px 30px;
}
.el-main {
  padding: 0px;
  border-left: 0px solid #e0e0e0;
}
.asideR {
  border-left: 1px solid #e0e0e0;
  padding:0px 15px 0px;
}
.chooseD a {
  display: block;
  padding: 5px 0;
}

.choose-department-checked {
  width: 100%;
  box-sizing: border-box;
  padding: 10px;
  height: 500px;
  overflow-y: auto;
  border: 1px solid #e6ebf5;
}

.choose-department-item {
  height: 30px;
  -js-display: flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.choose-department-item:hover {
  background-color:rgb(183, 217, 245)
}

.choose-department-item-text {
  width: 80%;
  user-select: none;
}

.choose-department-item-close {
  cursor: pointer;
  font-size: 16px;
}
.contwarp{
  height: 58vh;
  overflow: hidden;
}
.warpLeft{
  height: 100%;
  overflow: hidden;
}

.warpLeft ::v-deep .el-input__validateIcon{
    display: none;
}
.choose-department-bottom {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
}
</style>
