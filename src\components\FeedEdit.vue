<template>
	<div class="p10">
		<div class="message tableForm">
		<sb-el-form
          :form="queryForm"
          v-model="queryFormValue"
         @handleStartTime="handleStartTime"
		 @handleEndTime="handleEndTime"
		 :on-ok="handleDoFun"
      ></sb-el-form>
	</div>
</div>
</template>
<script>
	export default {
		name: "feedEdit",
		props: {
			content: {
				type: Object,
      			default() {
        			return {};
      			}
			},
		},
		computed: {
			queryFormValue(){
				return this.content
			}
		},
		data() {
			return {
				queryForm: {
        		inline: true,
       			labelWidth: "200px",
        		labelPosition: 'left',
        		formItemList: [
          		{label: "周期", key: "cycle", type: "input", class: 'c12', disabled: true, rule: { required: true }},
				//   {class: "c4",label: "开始时间",key: "startTime",type: "date",subtype: "date",valueFormat: "yyyy-MM-dd",disabledDate: "afterDate",changeFun: "handleStartTime",rule: {required: true}},
				//   {class: "c4",label: "结束时间",key: "endTime",type: "date",subtype: "date",valueFormat: "yyyy-MM-dd",rule: {required: true}},
          		{label: "填报周期开始时间", key: "startDate", type: "date", class: 'c12',subtype: "date",valueFormat: "yyyy-MM-dd", disabled: false, rule: { required: true },changeFun: "handleStartTime"},
          		{label: "填报周期结束时间", key: "endDate", type: "date", class: 'c12',subtype: "date",valueFormat: "yyyy-MM-dd", disabled: false, rule: { required: true },changeFun: "handleEndTime"},
          		{label: "填报截止时间", key: "deadline", type: "input", class: 'c12', disabled: false, rule: { required: true }},
          		{label: "任务负责人短信模板", key: "msg", type: "input", class: 'c12', disabled: false, rule: { required: true }},
          		{label: "接口人短信模板", key: "interfaceMsg", type: "input", class: 'c12', disabled: false, rule: { required: true }},
        		],
      },
			}
		},
		created() {
console.log(this.content)

		},
		mounted() {

		},
		methods: {
			handleStartTime(obj,value){
				console.log(obj,value)
				
			},
			handleEndTime(obj,value){
				console.log(obj,value)
				
			},
			handleDoFun(obj, fun, data) {
			//若一个beforeFun可直接在这个函数里面写
			let n;
			if(obj){
				n = this[obj[fun]].call(this, obj, data);
			}else{
				n = this[fun].call(this, data);
			}
			return n;
		}
		}
	};
</script>
<style scope>
	.w99 {
		width: 99%;
		margin: 0 auto;
	}

	.p10 {
		padding: 10px;
	}

	.el-dialog__body {
		padding: 20px 20px 0;
	}
</style>