<template>
	<div class="app-container" style="display:flex;justify-content: space-between;">
		<div class="container-left">
			<el-card shadow="never" style="overflow: auto; width: 100%;height: 100%;">
				<template #header>
				<div class="card-header">
                    <span style="color:red">修改组织信息后需手动点击当前父级组织更新数据</span>
				</div>
				</template>
				<div class="head-container">
					<!-- <el-tree class="tree2" :props="defaultProps" ref="chooseOrgTree" :data="filteredTreeData" default-expand-all :highlight-current="true" :node-key="'id'"  :check-on-click-node="true">
						<template #default="{ node, data }">
							<div @click="handleNodeClick(node, data)" style="width:100%;height: 100%;line-height: 26px;">{{ data.orgName }}</div>
						</template>
					</el-tree> -->
					<el-tree class="tree1" :props="defaultProps" ref="chooseOrgTree" :load="loadNodeOrg" lazy :default-expanded-keys="treeExpandData" :node-key="'id'" :highlight-current="true" :check-on-click-node="true">
						<template #default="{ node, data }">
							<div @click="handleNodeClick(node, data)" style="width:100%;height: 100%;line-height: 26px;">{{ data.orgName }}</div>
						</template>
					</el-tree>
				</div>
			</el-card>
		</div>
		<div class="container-right">
			<sb-el-table :table="table" @getList="getList" @handleAddData="handleAddData" @handleUpData="handleUpData" @handleDelete="handleDelete" 
						@handleUpDataGetRow="handleUpDataGetRow" @handleAddBef="handleAddBef">
				<template v-slot:enableStatus="{ obj }">
					<el-switch v-model="obj.row.enableStatus" :active-value="1" :inactive-value="0" @change="handleChangeEnable(obj.$index, obj.row)"></el-switch>
				</template>
				<template v-slot:enabled="{ obj }">
					<div>{{ obj.row.enabled==true?'是':'否' }}</div>
				</template>
				<template v-slot:dataSourceId="{ obj }">
					<el-select v-model="obj.formValue.dataSourceId" filterable clearable placeholder="请选择">
						<el-option v-for="item in dbArr" :key="item.id" :label="item.name" :value="item.id"></el-option>
					</el-select>
				</template>
				<template v-slot:rateRule="{ obj }">
                    <div slot="content" class="rateRuleContent">
                        <img
                            src="../../../assets/images/search.png"
                            @click="chooseOrg"
                            alt=""
                            style="
                                width: 32px;
                                height: 27px;
                                margin-left: 6px;
                                cursor: pointer;
                                margin-top: -3px;
                            "
                        />
                    </div>
                </template>
			</sb-el-table>
		</div>
		<!-- 选择所属组织 -->
        <ConfigDialog :item="orgAllocatData" @chooseData="chooseData"/>
	</div>
</template>
<script>
import { findDictValue,getOrgList,findRootAndNextRoot,findSonByParentOrgId,addOrg,updateOrgCustom,deleteOrgCustom,findByIdOrg } from '@/api/system/zzgl.js';
import ConfigDialog from "../component/configDialog";

export default {
	name: 'dataSet',
	props: ["tableType", "tableTitle"],
	components: {
		ConfigDialog
	},
	data() {
		return {
			table: {
				filteredTreeData:[],
				border: true, // 是否带纵向边框
				loading: false, // 加载中动画
				modulName: 'zzgl-组织信息', // 列表中文名称
				stripe: true, // 是否为斑马条样式
				hasSelect: true, // 是否有复选框
				showIndex: true, // 序号
				data: [], // 数据
				addAndUpdateType: 'dialog',
				total: null,
				hasQueryForm: true, // 是否有查询条件
				queryForm: {
					inline: true,
					labelWidth: '110px',
					labelPosition: 'right',
					formItemList: [
						{ label: '组织名称', key: 'orgName', type: 'input', clearable: true },
						{ label: '公司类型', key: 'companyTypeDictValue', type: 'select',dictType:'companyType',from:true, clearable: true },
						{ label: '组织编码', key: 'orgCode', type: 'input', clearable: true },
						// { label: '上级组织编码', key: 'parentOrgCode', type: 'input', clearable: true },
					],
				},
				tr: [
					{ id: 'displayName', label: '组织全称', prop: 'displayName', },
					{ id: 'orgName', label: '组织名称', prop: 'orgName' },
					{ id: 'orgCode', label: '组织编码', prop: 'orgCode'},
					{ id: 'parentOrgCode', label: '上级组织编码', prop: 'parentOrgCode',  },
					{ id: 'parentOrgName', label: '上级组织名称', prop: 'parentOrgName',   },
					{ id: 'enabled', label: '是否可用', prop: 'enabled', width: '100', show: 'template' },
				],
				multipleSelection: [], //多选选中数据存放变量
				dialogVisible: false, //默认对话框关闭
				companyType:[],
				orgStyle:[],
				orgLevel:[],
				form: {
					width: '900px',
					labelWidth: '200px',
					inline: true,
					labelPosition: 'right',
					formItemList: [
						{ class: 'c11', label: '父级组织', key: 'parentOrgName', type: 'input', rule: { required: true }, clearable: true },
						{ class: 'c0-5',label: '', key: 'rateRule',type: 'template', template: 'rateRule', clearable: true},
						{ class: 'c12', label: '组织名称', key: 'orgName', type: 'input', inputType: 'textarea', autosize: true, clearable: true,rule: { required: true } },
						{ class: 'c6', label: '组织全称', key: 'displayName', type: 'input', rule: { required: true }, clearable: true },
						// { class: 'c6', label: '组织编码', key: 'orgCode', type: 'input', rule: { required: true }, clearable: true },
						{ class: 'c6', label: '公司类型', key: 'companyTypeDictValue', type: 'select',dictType: "companyType",from:true, clearable: true,rule: { required: true },},
						{ class: 'c6', label: '组织类型', key: 'styleDictValue', type: 'select', dictType:'orgStyle', from:true, rule: { required: true }, clearable: true },
						{ class: 'c6', label: '组织级别', key: 'levelDictValue', type: 'select', dictType:'orgLevel',from:true, rule: { required: true }, clearable: true },
						{ class: 'c6', label: '组织状态', key: 'status', type: 'select', options:[{name:'正常',value:0},{name:'锁定',value:1}], defaultValue:0, rule: { required: true }, clearable: true },
						{ class: 'c6', label: '是否是内部组织', key: 'isCmcc', type: 'select', options:[{name:'是',value:true},{name:'否',value:false}], defaultValue:true, rule: { required: true }, clearable: true },
						{ class: 'c6', label: '是否可用', key: 'enabled', type: 'select', options:[{name:'是',value:true},{name:'否',value:false}],defaultValue:true, rule: { required: true }, clearable: true },
						{ class: 'c6', label: '是否显示', key: 'isDisplay', type: 'select', options:[{name:'是',value:true},{name:'否',value:false}],defaultValue:true, rule: { required: true }, clearable: true },
						{ class: 'c6', label: '排序', key: 'displayOrder', type: 'input', rule: { required: true }, clearable: true },
						{ class: 'c12', label: '组织描述', key: 'description', type: 'input',inputType: 'textarea', rule: { required: false }, clearable: true },
						{ class: 'c6', label: '预留字段1', key: 'reserve1', type: 'input', rule: { required: false }, clearable: true },
						{ class: 'c6', label: '预留字段2', key: 'reserve2', type: 'input', rule: { required: false }, clearable: true },
						{ class: 'c6', label: '组织的主负责人的UID', key: 'orgManager', type: 'input', rule: { required: false }, clearable: true },
						{ class: 'c6', label: '组织的地址', key: 'postalAddress', type: 'input', rule: { required: false } , clearable: true},
						{ class: 'c6', label: '组织的邮政编码', key: 'postalCode', type: 'input', rule: { required: false } , clearable: true},
						{ class: 'c6', label: '传真号码', key: 'facsimileTelephoneNumber', type: 'input', rule: { required: false }, clearable: true },
						{ class: 'c6', label: '开始生效时间', key: 'startTime', type: 'date', rule: { required: false }, clearable: true },
						{ class: 'c6', label: '结束生效时间', key: 'endTime', type: 'date', rule: { required: false }, clearable: true },
						{ class: 'c6', label: '组织的上级主管领导的UID', key: 'supervisor', type: 'input', rule: { required: false }, clearable: true },
						{ class: 'c6', label: '组织的电话号码', key: 'telephoneNumber', type: 'input', rule: { required: false }, clearable: true },
						{ class: 'c6', label: '组织的辅负责人的UID', key: 'viceManager', type: 'input', rule: { required: false }, clearable: true },
						{ class: 'c6', label: '组织所属地市名称', key: 'publicStatus', type: 'input', rule: { required: false }, clearable: true },
						{ class: 'c6', label: '组织所在的公司Id', key: 'corpId', type: 'input', rule: { required: false }, clearable: true },
						// { class: 'c6', label: '是否显示', key: 'isDisplayValue', type: 'input', rule: { required: false } },
					],
				},
				listFormModul: {},
				hasOperation: true, //是否有操作列表
				operation: {
					width: '150',
					data: [
						{ id: 'add', name: '新增',beforeFun: "handleAddBef", fun: 'handleAddData', hasPermi: 'qybzxt:zzgl:add' },
                        { id: 'read', name: '查看', fun: 'handleUpDataGetRow',hasPermi: 'qybzxt:zzgl:read' },
						{ id: 'update', name: '编辑', fun: 'handleUpData', beforeFun: "handleUpDataGetRow",hasPermi: 'qybzxt:zzgl:edit' },
						{ id: 'delete', name: '删除', fun: 'handleDelete',hasPermi: 'qybzxt:zzgl:del'},
					],
				},
				hasPagination: true,
				listQuery: { size: 10, page: 1 },
				hasBatchOperate: false, //有无批量操作
				batchOperate: {},
				hasOtherQueryBtn: true,
				otherQueryBtn: {
					data: [
                        {
                            id: 'export',
                            name: '导出 ',
                            fun: 'exportSelect',
							hasPermi: 'qybzxt:zzgl:export' 
                        },
                    ],
				},
				hasGroupTabs: true,
				tabsList: [],
				tabsPosition: '全部',
			},
			treeExpandData: [],
            data: [],
            defaultProps: {
                children: 'children',
                label: 'label'
            },
			orgAllocatData: {
				type:'zuzhi',
				inputType: 'text',
				title: '选择组织',
				appendShow: true,
				rows: 12,
				btnText: '搜索',
				mulitple: false,
				dialogVisible: false,
				defaultProps: {
					children: "children",
					label: "displayName",
					isLeaf: 'leaf',
				},
			},
		};
	},
	activated() {
		
	},
	created(){
		this.table.listQuery.parentOrgCode = '9999000100000000000';
		this.getList()
	},
	mounted() {
		
	},
	methods: {
		handleNodeClick(node,data) {
			this.table.listQuery.parentOrgCode = node.data.orgCode;
			this.getList()
		},
		//获取tree树列表
        chooseOrg() {
            this.orgAllocatData.dialogVisible = true;
        },
		//选择配置信息回显
		chooseData(array,type) {
			let org = ''
			if (type == 'zuzhi') {
				org = array[0].displayName
				this.table.listFormModul.parentOrgName = org;
				this.table.listFormModul.parentOrgCode = array[0].orgCode;
			}
		},
		// 查询列表
		getList() {
			this.table.loading = true;
			getOrgList(this.table.listQuery).then((res) => {
				this.table.loading = false;
				this.table.data = res.data&&res.data.content?res.data.content:[];
				this.table.data.forEach(item=>{
					// console.log(item.displayName.split('\\'))
					let num = (item.displayName.split('\\').length)-2
					item.parentOrgName  = item.displayName.split('\\')[num]
				})
				this.table.total = res.data.totalElements?res.data.totalElements:0;
			}).catch((err) => {
				this.table.loading = false;
			});
		},
		handleAddBef(){
			this.table.operation.nowBtn = true;
			this.table.form.formItemList.forEach((item)=>{
				item.disabled = false;
			})
			return true
		},
		// 新增
		handleAddData() {
			addOrg(this.table.listFormModul).then(res => {
				this.table.dialogVisible = false
				if (res.status == 200){
					// this.$message({
					// 	message: '操作成功',
					// 	type: 'success'
					// });
					this.getList();
				} 
			})
		},

		// 编辑
		handleUpData() {
			updateOrgCustom(this.table.listFormModul).then(res => {
				this.table.dialogVisible = false
				if (res.status == 200) {
					this.$message({
						message: '操作成功',
						type: 'success'
					});
					this.getList();
				} 
			})
		},
		// 删除
		handleDelete(row) {
			deleteOrgCustom(row.id).then((res) => {
				if (res.status == 200) {
					this.$message({
						message: '操作成功',
						type: 'success'
					});
					this.getList();
				} 
			});
		},
		// 根据id查询行数据
		handleUpDataGetRow(row) {
			this.table.form.formItemList.forEach((item)=>{
				item.disabled = row.read;
			})
			this.table.listFormModul = row
		},
		
		handleDoFun(obj, fun, data) {
			let n = this[obj[fun]].call(this, obj, data);
			return n;
		},
		
		// 修改是否启用
		handleChangeEnable(index, row) {
			// changeStatus({ id: row.id, enableStatus: row.enableStatus }).then((res) => { }).catch((error) => {
			// 	this.table.data[index].enableStatus = this.table.data[index].enableStatus;
			// });
		},
		loadNodeOrg(node, resolve) {
			if (node.level === 0) {
				this.treeExpandData = ["1"]
				resolve([{
					"id": "1",
					"orgCode": "9999000100000000000",
					"orgName": "北京晟壁",
				}])
			} else {
				var params = {
					orgCode: node.data.orgCode
				}
				findSonByParentOrgId(params).then(({ data }) => {
				data.forEach((target) => {
					target.treeType == 'user' && Reflect.set(target, 'leaf', true)
				})
				resolve(data)
				})
			}
		},
		
	},
};
</script>
<style scoped>
.table-name {
    font-weight: bold;
}
.container-left {
    width: 300px;
    height: 100%;
    overflow: auto;
    background-color: var(--el-bg-color-overlay);
    border: 1px solid var(--el-border-color-light);
    border-radius: 4px;
    box-shadow: var(--el-box-shadow-light);
}
.container-right {
    width: calc(100% - 300px);
    height: 100%;
	position: relative;
}
::-webkit-scrollbar {
    display: none;
}
::v-deep .el-card__header{
	padding: 8px 15px;
}
.treeData{
	width:100%;
	display:flex;
	justify-content: space-between;
	font-size: 15px;
	height: 30px;
	line-height: 30px;
	color: #444;
	padding-left: 8px;
}
.treeData .text{
	width: 215px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	/* padding: 5px; */
	cursor: pointer;
}
.treeData .text:hover{
	color: rgba(192,0,0,1);
}
.treeData .execute{
	width: 70px;
	color: rgba(192,0,0,1);
	cursor: pointer;
	text-align: right;
}
.treeData .execute:hover{
	text-decoration: underline;
}
::v-deep .el-card__body{
	padding: 0 15px;
	padding-bottom: 15px;
}
.head-container{
	height: calc(100vh - 180px);
	overflow-y: auto;
    padding-top: 20px;
}
::v-deep .el-table__body-wrapper{
	max-height: calc(100vh - 320px);
	overflow-y: auto;
}
.acceptanceBox{
    position: relative;
	/* height: 250px; */
}
.footer{
    width: 100%;
    /* position: absolute; */
    left: 0;
    bottom: 0;
    text-align: center;
    margin-top: 20px;
	margin-bottom: 20px;
}
.selectedColor {
	background-color: #eeeeee;
}
</style>
