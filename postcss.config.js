module.exports = {
  plugins: {
    autoprefixer: {},
    'autoprefixer': {},
    'postcss-pxtorem': {
      rootValue: 16, // 设计稿根字体大小
      unitPrecision: 5, // 转换后保留小数点位数
      propList: ['*'], // 将所有属性的px值都转换为rem
      selectorBlackList: [], // 不需要转换的类名
      replace: true,
      mediaQuery: false,
      minPixelValue: 0, // 小于等于0时不转换
      exclude: (e) => { // 排除规则：返回true表示排除，返回false表示包含
        // 只在指定的两个页面生效：views/index.vue 和 views/screen/index.vue
        if (/src(\\|\/)views(\\|\/)index\.vue/.test(e)) {
          return false; // 包含 views/index.vue
        }
        if (/src(\\|\/)views(\\|\/)screen(\\|\/)index\.vue/.test(e)) {
          return false; // 包含 views/screen/index.vue
        }
        return true; // 排除其他所有文件
      },
    }
  }
}
