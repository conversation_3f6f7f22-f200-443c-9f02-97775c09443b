<template>
  <sb-el-table :table="table" @getList="getList" @updateTableData="updateTableData" :on-ok="handleDoFun">
    <template v-slot:title="{obj}">
      <span class="file-1" @click="viewHandle(obj.row.file)">{{ obj.row.title }}</span>
    </template>
  </sb-el-table>
</template>

<script>
import {mzlRequest} from "@/api/process";

export default {
  props: {
    moreType: Number
  },
  data() {
    return {
      table: {
        modulName: "列表查询", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: false, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "110px",
          labelPosition: 'right',
          formItemList: [],
        },
        tr: [
          {
            id: "title",
            label: this.moreType == 1 ? '周期' : '名称',
            prop: 'title',
            show: 'template',
            template: 'title',
            align: 'center'
          },
        ],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "400px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: false, //是否有操作列表
        operation: {
          width: "160",
          fixed: "right",
          data: [
            // {id: "lookHandle", name: "【查看执行结果】", fun: "lookHandle"},
          ],
        },
        hasOtherQueryBtn: false, //是否有其他操作
        otherQueryBtn: {
          data: [
            // {id: "export", type: "success", name: "导出", fun: "handleExport"}
          ]
        },
        hasPagination: true,
        listQuery: {page: 1, size: 10},
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    viewHandle(file) {
      if (this.moreType == 1) {
        this.util.fileOpen(file.id)
      } else {
        this.util.fileOpen(file?.[0]?.id)
      }
    },
    downloadHandle(file) {
      this.util.fileDownload(file.id)
    },
    // 查询列表
    getList(listQuery) {
      this.table.loading = true;
      let params = listQuery || this.table.listQuery
      mzlRequest({
        url: `${this.moreType == 1 ? '/action/largeScreen/queryAi' : '/action/document/queryDocument'}?source=PC&page=${params.page}&size=${params.size}`,
        data: this.moreType == 1 ? {} : this.moreType == 2 ? { state: '0'} : { state: '1'}
      }).then((res) => {
        this.table.loading = false;
        this.table.data = res.data.content;
        this.table.total = res.data.totalElements
      }).catch((err) => {
        this.table.loading = false;
      });
    },
    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    },
  }
}
</script>

<style scoped>
.el-icon-download {
  color: rgba(192, 0, 0, 1);
  cursor: pointer;
  margin-left: 10px;
  font-size: 18px;
  font-weight: bold;
}

.file-1 {
  color: rgba(192, 0, 0, 1);
  cursor: pointer;
  text-decoration: underline;
}

.title {
  width: 99%;
  height: 30px;
  line-height: 30px;
  padding-left: 10px;
  border-left: #39aef5 4px solid;
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.titleName {
  font-size: 15px;
  color: #333;
  font-weight: 700;
}

</style>