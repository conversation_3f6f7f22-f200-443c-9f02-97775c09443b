<template>
  <div class="app-container" style="padding-bottom: 80px;">
    <sb-el-table :table="table" @getList="getList" @updateTableData="updateTableData" :on-ok="handleDoFun" @handleAdd="handleAdd" @editHandle="editHandle"
                 @editHandleGetRow="editHandleGetRow" @handleExport="handleExport" @lookHandle="lookHandle" @removeHandle="removeHandle">
      <template v-slot:type="{obj}">
        {{obj.row.type == 1 ? '牵头部门' : '协办部门'}}
      </template>
    </sb-el-table>
    <!-- 变更记录 -->
    <el-dialog title="查看变更记录" :visible.sync="lookD" v-dialogDrag :close-on-click-modal="false" append-to-body width="1200px">
      <el-table :data="dataList" style="width: 100%;" border :cell-style="{background: '#ffffff'}">
        <el-table-column label="序号" type="index" align="center">
        </el-table-column>
        <el-table-column prop="beforeTrueName" label="变更前姓名">
        </el-table-column>
        <el-table-column prop="beforeUserName" label="变更前OA账号">
        </el-table-column>
        <el-table-column prop="afterTrueName" label="变更后姓名">
        </el-table-column>
        <el-table-column prop="afterUserName" label="变更后OA账号">
        </el-table-column>
      </el-table>
      <div style="height: 20px;"></div>
    </el-dialog>
  </div>
</template>

<script>
import {changelist, interfaceexport, interfacelist, interfaceremove, interfaceupdate} from "@/api/process";
import ProcessTrack from "@/components/Process/ProcessTrack.vue";
let optionsList = [{name: '经营条线', value: '经营条线'},{name: 'IT条线', value: 'IT条线'},{name: '综合条线', value: '综合条线'},{name: '网络条线', value: '网络条线'},{name: '监督条线', value: '监督条线'}]
export default {
  components: {ProcessTrack},
  data() {
    return {
      title: "",
      showDialog: false,
      curComponent: "",
      pageLabel: "taskInfo",
      table: {
        modulName: "接口人查询-接口人", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: false, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: true, // 是否有查询条件
        queryForm: {
          inline: true,
          labelPosition: 'right',
          labelWidth: "120px",
          formItemList: [
            {label: "条线名称", key: "lineType", type: "select", class: 'c4', options: optionsList},
            {label: "部门名称", key: "departmentName", type: "input", class: 'c4'},
            {label: "部门类型", key: "type", type: "select", class: 'c4', options: [{name: '牵头部门', value: 1}, {name: '协办部门', value: 0}]},
            {label: "接口人姓名", key: "interfaceAdminName", type: "input", class: 'c4'},
            {label: "接口人OA账号", key: "interfaceAdminCode", type: "input", class: 'c4'},
          ],
        },
        tr: [
          {id: "lineType", label: "条线名称", prop: 'lineType'},
          {id: "departmentName", label: "部门名称", prop: 'departmentName'},
          {id: "type", label: "部门类型", prop: 'type', template: 'type', show: 'template'},
          {id: "interfaceAdminName", label: "接口人姓名", prop: 'interfaceAdminName'},
          {id: "interfaceAdminCode", label: "接口人OA账号", prop: 'interfaceAdminCode'},
          {id: "modifiedTime", label: "最后修改时间", prop: 'modifiedTime'}
        ],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "700px",
          labelWidth: "120px",
          inline: true,
          formItemList: [
            {
              class: 'c12', label: '接口人姓名', key: 'interfaceAdminName', type: 'user',
              readonly: true, mulitple: false, stepLoad: true, appendShow: true, rule: {required: true},
              relevancy: "interfaceAdminName-name,interfaceAdminCode-id", defaultProps: {
                children: "children",
                label: "name",
                isLeaf: 'leaf',
              }, handleUser: 'chooseUser'
            },
            {class: "c12", label: "接口人OA账号", key: "interfaceAdminCode", type: "input", disabled: true}
          ],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: "220",
          fixed: "right",
          data: [
            {id: "update", name: "【变更】", fun: "editHandle"},
            {id: "readchange", name: "【查看变更记录】", fun: "lookHandle"},
            // {id: "delete", name: "【删除】", fun: "removeHandle"},
          ],
        },
        hasOtherQueryBtn: true, //是否有其他操作
        otherQueryBtn: {
          data: [
            {id: "export", type: "success", name: "导出", fun: "handleExport"}
          ]
        },
        hasPagination: false,
        listQuery: {},
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
      lookD: false,
      dataList: []
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async handleExport() {
      let res = await interfaceexport(this.table.listQuery)

      if (res.data) {
        this.util.blobDownload(res.data, res.filename);
      } else {
        this.$message({
          message: '导出失败',
          type: 'warning',
          duration: 1500
        });
      }
    },
    async lookHandle(data) {
      let res = await changelist(data.row.id)
      this.dataList = res.data
      this.lookD = true
    },
    editHandleGetRow(row) {
      Object.assign(this.table.listFormModul, row)
    },
    async removeHandle(row, index) {
      await interfaceremove(row.id)
      // this.$message.success('删除成功')
      this.table.data.splice(index, 1)
    },
    async editHandle() {
      await interfaceupdate(this.table.listFormModul)
      // this.$message.success('修改接口人成功')
      this.table.dialogVisible = false
      this.getList()
    },
    async handleAdd() {
      await interfaceadd(this.table.listFormModul)
      // this.$message.success('新增接口人成功')
      this.table.dialogVisible = false
      this.getList()
    },
    chooseUser(item, arr) {
      let orgDisplayName = arr[0].orgDisplayName
      let list = orgDisplayName.split('\\')
      let obj = {
        departmentName: list.slice(1).join('/')
      }
      Object.assign(this.table.listFormModul, obj)
    },
    // 查询列表
    getList(listQuery) {
      this.table.loading = true;
      interfacelist(listQuery || this.table.listQuery, listQuery).then((res) => {
        this.table.loading = false;
        res.data.forEach(item => {
          item.modifiedTime = this.util.getNow('yyyy-MM-dd hh:mm:ss', item.modifiedTime)
        })
        this.table.data = res.data
      }).catch((err) => {
        this.table.loading = false;
      });
    },
    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    },
  }
}
</script>

<style scoped>

::v-deep .inlineB .el-form-item__content {
  flex: 1;
}

.title {
  width: 99%;
  height: 30px;
  line-height: 30px;
  padding-left: 10px;
  border-left: #39aef5 4px solid;
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.titleName {
  font-size: 15px;
  color: #333;
  font-weight: 700;
}

::v-deep .table-container {
  /* 去除项目经历左右边距 */
  padding: 0;
}
</style>