<template>
  <div class="app-container" style="padding-bottom: 80px;">
    <sb-el-table :table="table" @getList="getList" @updateTableData="updateTableData" :on-ok="handleDoFun" @handleAdd="handleAdd" @editHandle="editHandle"
                 @editHandleGetRow="editHandleGetRow" @lookHandle="lookHandle" @removeHandle="removeHandle" @uploadFileList="uploadFileList">
      <template v-slot:isIndex="{obj}">
        {{obj.row.isIndex == 1 ? '是' : '否'}}
      </template>
      <template v-slot:latitudeId="{obj}">
        {{ latitudeList.filter(a => a.value == obj.row.latitudeId)?.[0].name }}
      </template>
      <!-- <template v-slot:lineTypeId="{obj}">
        {{ lineTypeList.filter(a => a.value == obj.row.lineTypeId)?.[0].name }}
      </template> -->
    </sb-el-table>
  </div>
</template>

<script>
import {
  userroleexport,
} from "@/api/process";
import {
  getfeeddate, queryLineType,userroleadd,userrolelist,userroleremove,userroleupdate
} from "@/api/branchCompany/index";
import {uploadProcessFiles} from "@/api/public";

export default {
  data() {
    return {
      title: "",
      showDialog: false,
      curComponent: "",
      pageLabel: "taskInfo",
      table: {
        modulName: "周报推送管理-周报推送", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: false, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: true, // 是否有查询条件
        queryForm: {
          inline: true,
          labelPosition: 'right',
          labelWidth: "120px",
          formItemList: [
            {label: "反馈周期", key: "latitudeId", type: "select", class: 'c4', options: []},
          ],
        },
        tr: [
          {id: "latitudeId", label: "反馈周期", prop: 'latitudeId', show: 'template', template: 'latitudeId', width: 320},
          {id: "situation", label: "周报内容", prop: 'situation'},
          // {id: "lineTypeId", label: "所属条线", prop: 'lineTypeId', show: 'template', template: 'lineTypeId', width: 100},
          {id: "isIndex", label: "是否默认展示看板", prop: 'isIndex', show: 'template', template: 'isIndex', width: 140},
          {id: "trueNames", label: "查看人员范围", prop: 'trueNames'},
          {id: "modifiedTime", label: "最后修改时间", prop: 'modifiedTime', width: 160}
        ],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "1000px",
          labelWidth: "150px",
          inline: true,
          formItemList: [
            {class: "c12", label: "反馈周期", key: "latitudeId", type: "select", options: [], rule: { required: true}},
            {class: "c12", label: "周报内容", key: "situation", type: "input", inputType: 'textarea', autosize: {minRows: 2}, rule: { required: true }},
            {class: "c12", label: "是否默认展示看板", key: "isIndex", type: "radio", options: [{name: '是', value: '1'}, {name: '否', value: '0'}], rule: { required: true}},
            {
              class: 'c12', label: '查看人员姓名', key: 'trueNames', type: 'user',
              readonly: true, mulitple: true, stepLoad: true, appendShow: true, rule: { required: true },
              relevancy: "trueNames-name,userNames-id", defaultProps: {
                children: "children",
                label: "name",
                isLeaf: 'leaf',
              }
            },
            {class: "c12", label: "查看人员OA账号", key: "userNames", type: "input", disabled: true},
            {
              class: "c12",
              label: "查看附件",
              key: "file",
              type: "sbUpload",
              btnText: "上传",
              fun: "uploadFileList",
              listType: "text",
              multiple: false,
              rule: {required: true},
              show: true,
              isfile: true,
              // accept: '.doc, .docx, .pdf'
            }
          ],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: "200",
          fixed: "right",
          data: [
            // {id: "read", name: "【查看】", fun: "lookHandle"},
            {id: "update", name: "【编辑】", fun: "editHandle"},
            {id: "delete", name: "【删除】", fun: "removeHandle"},
            {id: "add", type: "danger", name: "新增", fun: "handleAdd"},
          ],
        },
        hasOtherQueryBtn: false, //是否有其他操作
        otherQueryBtn: {
          data: [
            // {id: "export", type: "success", name: "导出", fun: "handleExport"}
          ]
        },
        hasPagination: true,
        listQuery: {latitudeId: null},
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
      optionsList: []
    }
  },
  computed: {
    latitudeList() {
      return this.table.queryForm.formItemList[0].options
    },
    lineTypeList() {
      return this.table.form.formItemList[2].options
    }
  },
  created() {
    this.getFeedDateList()
    // this.getLineTypeList()
  },
  methods: {
    uploadFileList(obj) {
      uploadProcessFiles(obj.formData).then(async res => {
        obj.content.onSuccess(res, obj.content.file, []);
      }).catch(error => {
        obj.content.onError();
      });
    },
    async getLineTypeList() {
      let res = await queryLineType()
      this.table.form.formItemList[2].options = res.data.map(item => {
        return {
          name: item.line_type || item.LINE_TYPE,
          value: item.LINE_TYPE_ID
        }
      })
    },
    async getFeedDateList() {
      let res = await getfeeddate('',new Date().getFullYear())
      this.table.queryForm.formItemList[0].options = res.data.map(item => {
        return {
          name: `第${item.cycle}期（${this.dateToText(item.startDate)} - ${this.dateToText(item.endDate)}）`,
          value: item.id
        }
      })
      this.table.form.formItemList[0].options = this.table.queryForm.formItemList[0].options
      if (res.data.length) {
        this.table.listQuery.latitudeId = res.data[0].id
        await this.getList()
      }
    },
    dateToText(date) {
      if (date) {
        let arr = date.split('-')
        return arr[0] + '年' + arr[1] + '月' + arr[2] + '日'
      }
      return ''
    },
    lookHandle(row) {
      Object.assign(this.table.listFormModul, row)
    },
    editHandleGetRow(row) {
      Object.assign(this.table.listFormModul, row)
    },
    async removeHandle(row, index) {
      await userroleremove(row.id)
      // this.$message.success('删除成功')
      this.table.data.splice(index, 1)
    },
    async editHandle() {
      await userroleupdate(this.table.listFormModul)
      // this.$message.success('修改管理员成功')
      this.table.dialogVisible = false
      this.getList()
    },
    async handleAdd() {
      await userroleadd(this.table.listFormModul)
      // this.$message.success('新增管理员成功')
      this.table.dialogVisible = false
      this.getList()
    },
    // 查询列表
    getList(listQuery) {
      this.table.loading = true;
      userrolelist(listQuery || this.table.listQuery).then((res) => {
        this.table.loading = false;
        // res.data.forEach(item => {
        //   item.modifiedTime = this.util.getNow('yyyy-MM-dd hh:mm:ss', item.modifiedTime)
        // })
        this.table.data = res.data;
        this.table.total = res.data.length;
      }).catch((err) => {
        this.table.loading = false;
      });
    },
    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    },
  }
}
</script>

<style scoped>

::v-deep .inlineB .el-form-item__content {
  flex: 1;
}

.title {
  width: 99%;
  height: 30px;
  line-height: 30px;
  padding-left: 10px;
  border-left: #39aef5 4px solid;
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.titleName {
  font-size: 15px;
  color: #333;
  font-weight: 700;
}

::v-deep .table-container {
  /* 去除项目经历左右边距 */
  padding: 0;
}
</style>