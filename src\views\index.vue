<template>
  <div class="screen-container">
    <!-- 顶部图片区域 -->
    <div class="top-banner">
      <div class="nav-item" @click="lookto('1')"><span class="bg"></span><div class="nav-item-text">豫起奋发</div></div>
      <div class="nav-item" @click="lookto('2')"><span class="bg"></span><div class="nav-item-text">荣誉时刻</div></div>
      <div class="nav-item" @click="lookto('3')"><span class="bg"></span><div class="nav-item-text">你来吐槽 我来落实</div></div>
      <div class="nav-item" @click="lookto('4')"><span class="bg"></span><div class="nav-item-text">2025更加出彩项目</div></div>
      <img src="@/assets/images/screen/logo.png" style="display: block;" alt="">
      <div class="nav-item" @click="lookto('5')"><span class="bg"></span><div class="nav-item-text">你讲经验 我来推广</div></div>
      <div class="nav-item" @click="lookto('6')"><span class="bg"></span><div class="nav-item-text">经验视频</div></div>
      <div class="nav-item" @click="lookto('7')"><span class="bg"></span><div class="nav-item-text">学习教育</div></div>
      <div class="nav-item" @click="goto()"><span class="bg"></span><div class="nav-item-text">数智党建</div></div>
    </div>

    <!-- 标题导航栏 -->

    <!-- 主体内容区域 -->
    <div class="main-content">
        <div class="left-panel">
            <div class="top-section">
              <img src="@/assets/images/screen/yuqifenfa.jpg" class="box-img" alt="">
                <ul class="ul-box">
                <div class="ul-top">
                    <div class="ul-text">
                        省公司
                    </div>
                    <div class="look-more" @click="gotoMore('pro')">
                        更多>>
                    </div>
                </div>
                <div  v-if="provinceList.length>0">
                  <li class="item-text"  v-for="(item,index) in provinceList" :key="index" @click="gotomenhu(item)">
                      <div class="cycle"></div>
                      <div class="text-info">{{item.ART_TITLE}}</div>
                  </li>
                   <!-- <li class="item-text">
                    <div class="cycle"></div>
                    <div class="text-info">牢固廉洁防线!中国移动XX公司党委获评“全省清廉国企示范单位"</div>
                </li>
                <li class="item-text">
                    <div class="cycle"></div>
                    <div class="text-info">牢固廉洁防线!中国移动XX公司党委获评“全省清廉国企示范单位"</div>
                </li>
                 <li class="item-text">
                    <div class="cycle"></div>
                    <div class="text-info">牢固廉洁防线!中国移动XX公司党委获评“全省清廉国企示范单位"</div>
                </li> -->
                 
                </div>
               
                <div class="nodata" v-else>暂无数据</div>
               
                
                
            </ul>
            <ul class="ul-box noborder">
                <div class="ul-top">
                    <div class="ul-text">
                        地市公司
                    </div>
                    <div class="look-more" @click="gotoMore('city')">
                        更多>>
                    </div>
                </div>
                <div  v-if="fenCompanyList.length>0">
                  <li class="item-text"  v-for="(item,index) in fenCompanyList" :key="index" @click="gotomenhu(item)">
                      <div class="cycle"></div>
                      <div class="text-info">{{item.ART_TITLE}}</div>
                  </li>
                
                 
                </div>
               
                <div class="nodata" v-else>暂无数据</div>
                
                
            </ul>
            </div>
            <div class="bottom-section">
              <img src="@/assets/images/screen/rongyushike.jpg" class="box-img" alt="">
                <div class="swiper">
                    <el-carousel  @change="handleCarouselChange" indicator-position="none" :interval="4000" type="card"  arrow="never">
                        <el-carousel-item v-for="(item, index) in carouselItems" :key="index">
                        <img :src="item.image"  @load="onImageLoad" class="carousel-image" />
                        </el-carousel-item>
                    </el-carousel>
                    <div class="caption-container">
                        <transition name="fade" mode="out-in">
                        <div :key="currentCarouselIndex">
                            <h3 class="title">{{ carouselItems[currentCarouselIndex].title }}</h3>
                            <p class="description">{{ carouselItems[currentCarouselIndex].description }}</p>
                        </div>
                        </transition>
                    </div>
                </div>
              
            </div>
        </div>
      <div class="center-panel" >
        <div class="center-top">
          <img class="top-img" src="@/assets/images/screen/2025.png" alt="">
          <div class="top-main">
            <div class="center-top-header">
              <div class="top-one">
                <div style="position: relative;">
                    <img src="@/assets/images/screen/top1new1.png" class="top-step" alt="">
                    <div class="top-one-text" v-if="pingjun1">{{pingjun1}}<span>%</span></div>
                    
                </div>
              <div class="top-desc">组长单位:市场经营部</div>
              
            </div>
            <div class="top-one">
              <div style="position: relative;">
                   <img src="@/assets/images/screen/top2new2.png" class="top-step" alt="">
                    <div class="top-one-text" v-if="pingjun2">{{pingjun2}}<span>%</span></div>

              </div>
              <div class="top-desc">组长单位:科技创新部</div>
             
              
            </div>
            <div class="top-one">
              <div style="position: relative;">
                  <img src="@/assets/images/screen/top3new3.png" class="top-step" alt="">
                    <div class="top-one-text" v-if="pingjun3">{{pingjun3}}<span>%</span></div>

              </div>
              <div class="top-desc">组长单位:客户服务部</div>
             
            </div>
            <div class="top-one">
              <div style="position: relative;">
                  <img src="@/assets/images/screen/top4new4.png" class="top-step" alt="">
                    <div class="top-one-text" v-if="pingjun4">{{pingjun4}}<span>%</span></div>

              </div>
              <div class="top-desc">组长单位:财务部</div>
              
            </div>

            </div>
            <img src="@/assets/images/screen/title.png" class="xiangmu" alt="">
            <div class="center-list">
              <div class="top-item" v-for="(item,index) in centerDataList" :key="index">
                <div class="top-item-text1">{{item.id}}</div>
                <div class="top-item-text2">
                  <div class="top-item-text2-one">{{item.projectName}}</div>
                  <div class="top-item-text2-two">{{item.deptName}}</div>
                </div>
                  <div class="top-item-text3" v-if="item.currentProgress">{{item.currentProgress}}<span>%</span></div>

              </div>
               <!-- <div class="top-item">
                <div class="top-item-text1">2</div>
                <div class="top-item-text2">
                  <div class="top-item-text2-one">产品权益能力增收</div>
                  <div class="top-item-text2-two">主办单位--市场经营部</div>
                </div>
                  <div class="top-item-text3">23<span>%</span></div>

              </div>
               <div class="top-item">
                <div class="top-item-text1">3</div>
                <div class="top-item-text2">
                 <div class="top-item-text2-one">商客市场拓展</div>
                  <div class="top-item-text2-two">主办单位--商客运营中心</div>
                </div>
                  <div class="top-item-text3">22<span>%</span></div>

              </div>
               <div class="top-item">
                <div class="top-item-text1">4</div>
                <div class="top-item-text2">
                 <div class="top-item-text2-one">政企三大能力提升</div>
                  <div class="top-item-text2-two">主办单位--政企客户部</div>
                </div>
                  <div class="top-item-text3">21<span>%</span></div>

              </div>
               <div class="top-item">
                <div class="top-item-text1">5</div>
                <div class="top-item-text2">
                  <div class="top-item-text2-one">专线份额价值双提升</div>
                  <div class="top-item-text2-two">主办单位--政企客户部</div>
                </div>
                  <div class="top-item-text3">20<span>%</span></div>

              </div>
              <div class="top-item">
                <div class="top-item-text1">6</div>
                <div class="top-item-text2">
                  <div class="top-item-text2-one">领航级政企项目打造</div>
                  <div class="top-item-text2-two">主办单位--战略客户拓展中心</div>
                </div>
                  <div class="top-item-text3">24<span>%</span></div>

              </div>
              <div class="top-item">
                <div class="top-item-text1">7</div>
                <div class="top-item-text2">
                 <div class="top-item-text2-one">自有产品植入率提升</div>
                  <div class="top-item-text2-two">主办单位--信息服务能力中心</div>
                </div>
                  <div class="top-item-text3">23<span>%</span></div>

              </div>
              <div class="top-item">
                <div class="top-item-text1">8</div>
                <div class="top-item-text2">
                  <div class="top-item-text2-one">新质生产力布局</div>
                  <div class="top-item-text2-two">主办单位--科技创新部</div>
                </div>
                  <div class="top-item-text3">22<span>%</span></div>

              </div>
              <div class="top-item">
                  <div class="top-item-text1">9</div>
                  <div class="top-item-text2">
                    <div class="top-item-text2-one">移动爱家品牌打造</div>
                    <div class="top-item-text2-two">主办单位--市场经营部</div>
                  </div>
                  <div class="top-item-text3">25<span>%</span></div>

                </div>
                <div class="top-item">
                  <div class="top-item-text1">10</div>
                  <div class="top-item-text2">
                    <div class="top-item-text2-one">营销行为规范</div>
                    <div class="top-item-text2-two">主办单位--客户服务部</div>
                  </div>
                  <div class="top-item-text3">24<span>%</span></div>

                </div>
                <div class="top-item">
                  <div class="top-item-text1">11</div>
                  <div class="top-item-text2">
                    <div class="top-item-text2-one">网格产品化运营</div>
                    <div class="top-item-text2-two">主办单位--网络部</div>
                  </div>
                  <div class="top-item-text3">23<span>%</span></div>

                </div>
                 <div class="top-item">
                  <div class="top-item-text1">12</div>
                  <div class="top-item-text2">
                    <div class="top-item-text2-one">新增净增比改善</div>
                    <div class="top-item-text2-two">主办单位--市场经营部</div>
                  </div>
                  <div class="top-item-text3">23<span>%</span></div>

                </div>
                <div class="top-item">
                  <div class="top-item-text1">13</div>
                  <div class="top-item-text2">
                    <div class="top-item-text2-one">政企欠费管控</div>
                    <div class="top-item-text2-two">主办单位--政企客户部</div>
                  </div>
                  <div class="top-item-text3">22<span>%</span></div>

                </div>
                <div class="top-item">
                  <div class="top-item-text1">14</div>
                  <div class="top-item-text2">
                   <div class="top-item-text2-one">做优成本管控</div>
                    <div class="top-item-text2-two">主办单位--财务部</div>
                  </div>
                  <div class="top-item-text3">22<span>%</span></div>

                </div>
                <div class="top-item">
                  <div class="top-item-text1">15</div>
                  <div class="top-item-text2">
                    <div class="top-item-text2-one">漫游客户运营</div>
                    <div class="top-item-text2-two">主办单位--市场经营部</div>
                  </div>
                  <div class="top-item-text3">21<span>%</span></div>
                </div> -->


            </div>

            
            
          </div>
        </div>
        <div class="center-bottom" >
            <div class="center-left">
                <img class="title-img" src="@/assets/images/screen/niliatucao.png" alt="">
                <ul class="ul-box noborder" v-if="tucaoList.length > 0">
                  <li class="item-text" @click="lookTuCao(item)" v-for="(item,index) in tucaoList" :key="index">
                      <div class="huo" v-if="index == 0"></div>
                      <div class="huo"></div>
                      <div class="text-info">{{item.TITLE}}</div>
                  </li>
                  <!-- <li class="item-text" @click="lookNO()">
                      <div class="nohuo"></div>

                      <div class="huo"></div>
                      <div class="text-info">探索政企欠费管控新方法</div>
                  </li>
                  <li class="item-text" @click="lookNO()">
                      <div class="nohuo"></div>

                      <div class="huo"></div>
                      <div class="text-info">通过技术手段及时控制对手外呼行为</div>
                  </li> -->
              </ul>
                <div class="nodata" v-else>暂无数据</div>

            </div>
            <div class="center-right">
                <img class="title-img" src="@/assets/images/screen/nishuowojiang.png" alt="">
                <ul class="ul-box noborder" v-if="jingyanList.length > 0">
                   <li class="item-text" @click="lookjingyan(item)" v-for="(item,index) in jingyanList" :key="index">
                      <div class="huo" v-if="index == 0"></div>
                      <div class="huo"></div>
                      <div class="text-info">{{item.TITLE}}</div>
                  </li>
                    <!-- <li class="item-text" @click="lookNO()">
                        <div class="nohuo"></div>
                        <div class="huo"></div>
                        <div class="text-info">安阳“智慧文旅”惊艳文旅部!中国移动项目入选国家级.</div>
                    </li>
                    <li class="item-text" @click="lookNO()">
                      <div class="nohuo"></div>
                      <div class="huo"></div>
                      <div class="text-info">许昌市“智慧曹魏古城”项目出彩全省!荣获“数字文旅</div>
                  </li> -->
                </ul>
                <div class="nodata" v-else>暂无数据</div>
            </div>
        </div>
       
      </div>
      <div class="right-panel">
        <div class="top-area">
          <img src="@/assets/images/screen/jingyanshipin.jpg" class="box-img" alt="">
          <div class="video-box">
            <div class="video-main">
              <video :src="videoUrl" controls></video>
              <div class="look-more-video" style="margin-right: 0px;" @click="tomoreVideo()">
                        更多>>
                    </div>
            </div>
            <div class="video-text">
              <div class="video-title"></div>
              <div class="video-title1"></div>
              <div class="video-desc">周口沈丘分公司槐店网格长徐建峰分享了价值产品营销案例，介绍了槐店网格通过解决客户吐槽问题成功推动价值产品营销，并通过“一复得、二复失、三复受益、四复启发”的科学复盘将案例进行了固化，并在全区进行成功复制推广。</div>
            </div>
          </div>
        </div>
        <div class="bottom-area">
          <img src="@/assets/images/screen/xuexijiaoyu.jpg" class="box-img" alt="">

            <ul class="ul-box">
                <div class="ul-top">
                    <div class="ul-text">
                        清廉移动
                    </div>
                    <div class="look-more" @click="gotoMore('ql')">
                        更多>>
                    </div>
                </div>
                <div  v-if="qinglianList.length>0">
                  <li class="item-text"  v-for="(item,index) in qinglianList" :key="index" @click="gotoql(item)">
                      <div class="cycle"></div>
                      <div class="text-info">{{item.TITLE}}</div>
                  </li>
                  
                </div>
               
                <div class="nodata" v-else>暂无数据</div>
                
            </ul>
            <ul class="ul-box noborder">
                <div class="ul-top">
                    <div class="ul-text">
                        职场新风尚
                    </div>
                    <div class="look-more" @click="gotoMore('zhi')">
                        更多>>
                    </div>
                </div>
                <div  v-if="zhixinList.length>0">
                  <li class="item-text"  v-for="(item,index) in zhixinList" :key="index" @click="gotomenhu(item)">
                      <div class="cycle"></div>
                      <div class="text-info">{{item.ART_TITLE}}</div>
                  </li>
                  
                 
                </div>
               
                <div class="nodata" v-else>暂无数据</div>
                
            </ul>
        </div>
      </div>
    </div>
  <div class="znwd" @click="openAi"><img src="@/assets/images/screen/ai.gif" alt="" ></div>
  <div class="myplant"  v-if="aiShow">
      <iframe id="iframePlant" :src="aiUrl" frameborder="0" scrolling="no"  class="myIframe"></iframe>
  </div>
   <el-dialog
      :visible.sync="viewD"
      v-dialogDrag
      :close-on-click-modal="false"
      append-to-body
      width="20%"
    >
     <div class="qidai">正在建设中，敬请期待！</div>
    </el-dialog>

  </div>
</template>

<script>
import store from "@/store";
import {mapState} from "vuex";
import {mzlRequest,findArchivedReportsByPage,findKnowledgeBase,rongyuList,centerList,getLineInfo} from '@/api/process'
import '@/assets/js/rem.js'
let iportalUrl = window.location.href.indexOf('*************:8088') !=-1 ? 'http://iportal.ha.cmcc' : 'http://************:8088/portalweb/#'
let qLUrl = window.location.href.indexOf('*************:8088') !=-1 ? 'http://*************:8088/hnjjwz' : 'http://************:8088/hnjjwz'
let gjccUrl = window.location.href.indexOf('*************:8088') !=-1 ? 'http://*************:8088/oaWeb/gjccWeb/#/' : 'http://************:8088/gjccWeb/#/'
let yqffUrl = window.location.href.indexOf('*************:8088') !=-1 ? 'http://*************:8088/oaWeb/yqffWeb/#/' : 'http://************:8088/yqffWeb/#/'

export default {
  name: 'IndexPage',
  data() {
    return {
      viewD:false,
      aiUrl:'',
      aiShow: false,
      currentHeight: 0,
      loadedImages: 0,
      currentCarouselIndex: 0,
      videoUrl:'',
      carouselItems: [
        // {
        //   image: require('@/assets/images/screen/tu1.jpg'),
        //   title: '数字化转型',
        //   description: '2024年度，党建工作部在"豫起奋发 担当作为"主题实践活动中，获得“担当作为部门”荣誉称号。'
        // },
        // {
        //   image: require('@/assets/images/screen/tu1.jpg'),
        //   title: '数字化转型',
        //   description: '2024年度，党建工作部在"豫起奋发 担当作为"主题实践活动中，获得“担当作为部门”荣誉称号。'
        // },
        // {
        //   image: require('@/assets/images/screen/tu1.jpg'),
        //   title: '服务升级',
        //   description: '2024年度，党建工作部在"豫起奋发 担当作为"主题实践活动中，获得“担当作为部门”荣誉称号。'
        // }
      ],
      provinceList:[],
      fenCompanyList:[],
      qinglianList:[],
      zhixinList:[],
      tucaoList:[],
      jingyanList:[],
      centerDataList:[],
      pingjun1:'',
      pingjun2:'',
      pingjun3:'',
      pingjun4:'',


    }
  },
  computed: {
    ...mapState(['user'])
  },
  created() {
    
  },
  mounted() {
    document.title = '豫起奋发 更加出彩'
    this.videoUrl = this.util.getApiUrl() + "/sys/file/download?id=F855050359054872576"
    console.log(this.user.user.username)
     if (window.location.href.indexOf('http://*************:8088') === 0) {
        this.aiUrl = 'http://*************:8088/oaWeb/szdjAiWeb/#/?username='+ this.util.getRsa(this.user.user.username) +'&myFrom=OA'

     }else{
        this.aiUrl = 'http://************:8088/szdjAiWeb/#/?username='+ this.util.getRsa(this.user.user.username) +'&myFrom=OA'

     }
    window.addEventListener('resize', this.adjustCarouselHeight);
    window.closeMyplant = () => {
      this.aiShow = false;
    }
     // 预期奋发-省公司：100100204011        
      // 预期奋发-分公司：100100204012
      // 学习教育-职场新风尚 ：100100204013
    this.getNewList('100100204011')
    this.getNewList('100100204012')
    this.getNewList('100100204013')
    this.getQLNewList()
    this.findArchivedReportsByPage();
    this.findKnowledgeBase();
    this.getRongyu();
    this.getcenterList()
    this.getpingjunshu()

  },
  beforeDestroy() {
    window.removeEventListener('resize', this.adjustCarouselHeight);
     window.closeMyplant = null; // 清理全局方法
  },
  methods: {
    getpingjunshu(){
      getLineInfo({}).then(res=>{
        console.log(res)
        if(res.data && res.data.length > 0){
          res.data.forEach(el=>{
            if(el.LINE_NAME == '经营发展 更加出彩'){
              this.pingjun1 = el.SCORE;
            }else if(el.LINE_NAME == '科创能力 更加出彩'){
              this.pingjun2 = el.SCORE;
            }else if(el.LINE_NAME == '服务感知 更加出彩'){
              this.pingjun3 = el.SCORE;
            }else if(el.LINE_NAME == '精益管理 更加出彩'){
              this.pingjun4 = el.SCORE;
            }
          })
        }
      }) 
    },
    // 获取中间数据
    getcenterList(){
      centerList({}).then(res=>{
        console.log(res)
        if(res.data && res.data.length > 0){
          this.centerDataList = res.data;
         
        }
      }) 
    },
    // 获取轮播图
    getRongyu(){
      rongyuList({}).then(res=>{
        console.log(res)
        if(res.data && res.data.length > 0){
          this.carouselItems = res.data?.splice(0,3).map(item => ({
            image: item.formFileList[0]?.mobileFilePath,
            title: this.util.htmlDecode(item.name),
            description: this.util.htmlDecode(item.describe)
          }));
          setTimeout(()=>{
            this.adjustCarouselHeight()
          },500)

        }
        
      })
    },

    findKnowledgeBase(){
      findKnowledgeBase({
      page:1,
      size:3
    }).then(res=>{
      console.log(res)
        this.tucaoList = res.data?res.data.content?res.data.content.splice(0,3):[]:[];
      })
    },
    findArchivedReportsByPage(){
      findArchivedReportsByPage({
      page:1,
      size:3
    }).then(res=>{
      console.log(res)

        this.jingyanList = res.data?res.data.content?res.data.content.splice(0,3):[]:[];

      })
    },
    lookNO(){
      this.viewD = true;

    },
    lookto(type){
      var url = ''
      if(type == 1){
       var url = yqffUrl;

      }else if(type == 2){
        var url=  `${yqffUrl}systemManagement/deptHonor`
      } else if(type == 3){
         var url = `${gjccUrl}support/supportForm?appCode=yqff&username=${this.user.user.username}&loginuser=${this.util.encrypt(this.user.user.username)}&from=OA`

      }else if(type == 4){
         var url = `${gjccUrl}?appCode=yqff&username=${this.user.user.username}&loginuser=${this.util.encrypt(this.user.user.username)}&from=OA`


      }else if(type == 5){
        var url = `${gjccUrl}brilliant/report?appCode=yqff&username=${this.user.user.username}&loginuser=${this.util.encrypt(this.user.user.username)}&from=OA`

      }else if(type == 6){
        var url = `${gjccUrl}brilliant/look?appCode=yqff&username=${this.user.user.username}&loginuser=${this.util.encrypt(this.user.user.username)}&from=OA`


      }else if(type == 7){
        var url = qLUrl + '/html/webSite/index.html?appcode=hnjjwz&loginuser='+ this.util.getRsa(this.user.user.username) +'&myFrom=OA'

      }
      window.open(url,'_blank')
    },
    lookjingyan(item){
      var url = `${gjccUrl}workOrder?type=case&pmInsType=M&pmInsId=${item.PMINSID}&ID=${item.ID}&REPORTER=${item.REPORTER}&REPORTORG=${item.REPORTORG}&REPORTTIME=${item.REPORTTIME}&appCode=yqff&username=${this.user.user.username}&loginuser=${this.util.encrypt(this.user.user.username)}&from=OA`
      window.open(url,'_blank')

    },
    lookTuCao(item){
      var url = `${gjccUrl}workOrder?type=join&pmInsType=A&location=${item.ACTIVITY_DEF_ID}&pmInsId=${item.PM_INS_ID}&processDefKey=Process_1741771440303&iskonwledge=true&appCode=yqff&username=${this.user.user.username}&loginuser=${this.util.encrypt(this.user.user.username)}&from=OA`
      window.open(url,'_blank')
    },
    tomoreVideo(){
       var url = `${gjccUrl}brilliant/look?appCode=yqff&username=${this.user.user.username}&loginuser=${this.util.encrypt(this.user.user.username)}&from=OA`
      window.open(url,'_blank')


    },
    async getNewList(code){
     
      let res = await mzlRequest({
        url: `/ncms/findByCatSid?page=1&rows=3&catSid=${code}`
      });
      if(code == '100100204011'){
        // 预期奋发-省公司
        this.provinceList = res.data?res.data.content?res.data.content.splice(0,3):[]:[];
        
      }else if(code == '100100204012'){
        // 预期奋发-分公司
        this.fenCompanyList = res.data?res.data.content?res.data.content.splice(0,3):[]:[];

        
      }else if(code == '100100204013'){
        //  学习教育-职场新风尚
        this.zhixinList = res.data?res.data.content?res.data.content.splice(0,3):[]:[];

        
      }
      console.log(res)

    },
     async getQLNewList(){
      let res = await mzlRequest({
        url: `/ncms/findDataDetailList?page=1&rows=3`
      });
      this.qinglianList = res.data?res.data.contentDetial?res.data.contentDetial.splice(0,3):[]:[];

    },
    // 查看更多
    gotoMore(type){
      if(type == 'ql'){
         var url = qLUrl + '/html/webSite/index.html?appcode=hnjjwz&loginuser='+ this.util.getRsa(this.user.user.username) +'&myFrom=OA'
        window.open(url,'_blank')

        
      }else if(type == 'pro'){
        // 预期奋发-省公司
        var url = iportalUrl + '/newsList?catSid=100100204011&path=%2Fiportal%2Fncms%2Fcategory%2FinfoTwo&loginuser='+this.util.getRsa(this.user.user.username)+'&name='+encodeURIComponent('省公司')
        window.open(url,'_blank')
      }else if(type == 'city'){
       // 预期奋发-分公司
        var url = iportalUrl + '/newsList?catSid=100100204012&path=%2Fiportal%2Fncms%2Fcategory%2FinfoTwo&loginuser='+this.util.getRsa(this.user.user.username)+'&name='+encodeURIComponent('地市公司')
        window.open(url,'_blank')
      }else if(type == 'zhi'){
         //  学习教育-职场新风尚
        var url = iportalUrl + '/newsList?catSid=100100204013&path=%2Fiportal%2Fncms%2Fcategory%2FinfoTwo&loginuser='+this.util.getRsa(this.user.user.username)+'&name='+encodeURIComponent('职场新风尚')
        window.open(url,'_blank')
      }
      
    },
    gotomenhu(item){
       var url = `${iportalUrl}/news?id=${item.ID}&loginuser=${this.util.getRsa(this.user.user.username)}`
        window.open(url,'_blank')
    },
    gotoql(item){
      console.log(item)
       var url = qLUrl + '/html/webSite/listDetails.html?appcode=hnjjwz&loginuser='+ this.util.getRsa(this.user.user.username) +'&myFrom=OA&pmInsId='+item.PM_INS_ID
        window.open(url,'_blank')

    },
    openAi(){
      this.aiShow = true;

    },
    // closeMyplant(){
    //   this.aiShow = false;

    // },
    goto(){
       if (window.location.href.indexOf('http://*************:8088') === 0) {
        var url=`http://*************:8088/djfupt/html/dataScreen/partyBuildingScreen.html?appcode=djfupt&from=OA&uid=${this.util.getRsa(this.user.user.username)}`
          window.open(url,'_blank')

       }else{
        var url=`http://************:8088/djfupt/html/dataScreen/partyBuildingScreen.html?appcode=djfupt&from=OA&uid=${this.util.getRsa(this.user.user.username)}`

          window.open(url,'_blank')

       }

    },
    onImageLoad(event) {
      const img = event.target;
      this.loadedImages++;
      if (this.loadedImages === this.carouselItems.length) {
        this.currentHeight = img.height;
        this.adjustCarouselHeight();
      }
    },
    adjustCarouselHeight() {
      const carousel = this.$el.querySelector('.el-carousel__container');
      if (carousel) {
        carousel.style.height = `${this.currentHeight}px`;
      }
    },
    handleCarouselChange(currentIndex) {
      this.currentCarouselIndex = currentIndex;
      const carousel = this.$el.querySelector('.el-carousel__container');
      if (carousel) {
        this.$nextTick(()=>{
          carousel.style.height = `${this.currentHeight}px`;
        })
        
      }
    }
  }
}
</script>

<style scoped>
.screen-container {
  height: 100vh;
  background: #fff;
}

.top-banner {
  height: 56px;
  background: #fff;
  display: flex;
  justify-content: center;
  align-items: normal;

}


.nav-item {
    width: 130px;
    padding:6px 10px;
    color: #d31805;
    position: relative;
    line-height: 38px;
    cursor: pointer;
    margin-right: 50px;
}

.bg{
  content: "";
    position: absolute;
    width: 150px;
    height: 38px;
    display: inline-block;
    background: #fbe2e0;

}
.nav-item:nth-child(-n+4) .bg{
    transform: skew(30deg);
  
}
.nav-item:nth-last-child(-n+4) .bg{
    transform: skew(-30deg);
  
}
.nav-item-text{
    color: #d31805;
    font-size: 14px;
    width: 160px;
    display: block;
    padding-right: 12px;
    font-weight: 700;
    position: absolute;
    left: 6px;
    top: 5px;
    z-index: 2;
    text-align: center;
}


.nav-item:hover .bg{
  background: rgba(191,0,0,1);
}
.nav-item:hover .nav-item-text{
  color: #fff;
}

.main-content {
  /* overflow-y: hidden; */
  background-color: #fff;
  height: calc(100vh - 56px);
  display: grid;
  grid-template-columns: 26% 48% 26%;
  padding: 0 10px;
  /* gap: 10px; */
}

.left-panel,
.center-panel,
.right-panel {
  background: #fff;
  /* border-radius: 8px; */
  /* box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1); */
}

.left-panel {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  
}

.left-panel .top-section {
  position: relative;

  height: 51%;
  background: url(../assets/images/screen/bg12.png) no-repeat center;
  background-size: 100% 100%;
  padding: 40px 30px 30px;
  display: flex;
  flex-direction: column;
}

.left-panel .bottom-section {
  position: relative;
  height: 48%;
  background: url(../assets/images/screen/bg12.png) no-repeat center;
  background-size: 100% 100%;
  margin-top: 5px;

}
.box-img{
     position: absolute;
    top: 30px;
    left: 25px;
    width: 100px;
    height: auto;

}
.right-panel {
  display: flex;
  flex-direction: column;
  justify-content: space-between;

   
}

.right-panel .top-area {
  position: relative;

  height: 48%;
  background: url(../assets/images/screen/bg12.png) no-repeat center;
  background-size: 100% 100%;
  padding: 40px 30px 0px 30px ;


}

.right-panel .bottom-area {
  position: relative;

  height: 51%;
  background: url(../assets/images/screen/bg12.png) no-repeat center;
  background-size: 100% 100%;
  padding: 40px 30px 30px ;
  margin-top: 5px;
  display: flex;
  flex-direction: column;




}
/* .right-panel { background:  } */
.ul-box{
    height: 50%;
    padding: 30px 0 0;
    display: flex;
    flex-direction: column;
    /* justify-content: space-between; */
    color: #000;
    border-bottom: 1px dashed red;


}
.noborder{
    border-bottom: none;
}
.ul-top{
    display: flex;
    justify-content: space-between;
    align-items: center;

}

.ul-text{
    font-weight: bold;
    font-size: 16px;
    margin-left: 10px;
}
.look-more{
    color: #000;
    cursor: pointer;
    font-size: 12px;

}
.item-text{
    display: flex;
    align-items: center;
    margin: 10px 0 0 10px;
    cursor: pointer;

}
.cycle{
    width: 10px;
    height: 10px;
    border-radius: 100%;
    background-color: red;
    margin-right: 5px;
}
.nohuo{
    width: 13px;
    height: 20px;
}
.huo{
   width: 13px;
    height: 20px;
    background: url(../assets/images/screen/huo.png) no-repeat center;
    background-size: 100% 100%;

}
.text-info{
  flex: 1;
    font-size: 14px;
    color: #000;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left:5px;
}
.bottom-section{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.swiper{
    width: 92%;
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.carousel-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  display: flex;
  justify-content: center;
  align-items: center;
}

.caption-container {
  max-width: 95%;
  margin: 10px auto;
  overflow-wrap: break-word;
}

.title {
  font-size: 14px;
  line-height: 1.2;
  text-align: center;
  padding: 0;
}

.description {
  font-size: 12px;
  line-height: 1.4;
  text-indent: 2em;
}

.el-carousel {
  width: 90%;
  overflow: hidden;
}

.el-carousel__container {
  display: flex;
  align-items: center;
  height: 100%;
}
::v-deep .el-carousel__mask{
    background-color: transparent !important;
}
.center-panel{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: url(../assets/images/screen/bgcenter.png) no-repeat center;
    background-size: 100% 100%;
    margin:  0 5px;
    box-sizing: content-box;
    padding: 40px 20px 0;

}
.center-top{
  width: 100%;
  height: 70%;
  display: flex;
  flex-direction: column;
}
.top-img{
  height: 70px;
  width: auto;
  display: block;
}
.top-main{
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: 20px;
  margin-right: 20px;


}
.center-top-header{
  display: flex;
  justify-content: space-between;

}
.top-one{
  position: relative;
  width: 24%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}
.cycle-img{
    position: absolute;
    top: 15px;
    left: -18px;
    width: 15px;
    height: calc(100% - 50px);
    display: block;

}

.top-step{
   display: block;
   width: 100%;
}
.top-desc{
  text-align: center;
  font-size: 12px;
  line-height: 1.2;
  margin: -10px 0 10px;
}
.top-one-text{
  position: absolute;
  right:20px;
  top:50%;
  transform: translateY(-50%);
  color: #fff;
  margin-top: -3px;
  font-size: 16px;
}
.top-one-text span{
  color: #fff;
  font-size: 16px;
  font-weight: normal;
  margin-left: 3px;
}
.xiangmu{
  display: block;
  width: 130px;
  margin-bottom: 10px;

}
.center-list{
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.top-item{
  width: 28%;
  height: auto;
  background-image: url(../assets/images/screen/bgNew.png) ;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
  padding: 10px 15px;
  display: flex;
  margin-bottom: 15px;
  position: relative;
  justify-content: space-between;
  box-sizing: content-box;

}

.top-item-text1{
  color: #CB1313;
  font-size: 20px;
  margin-right: 10px;
  margin-top: -5px;
  font-weight: bold;

}
.top-item-text2{
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  font-size: 12px;
}
.top-item-text2-one{
  color: #000;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;

}
.top-item-text2-two{
  font-size: 14px;
  color:#000 ;
   overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-top: 3px;
}
.top-item-text3{
  color: #F83B28;
  font-size: 20px;
  margin-top: -5px;
  font-weight: bold;


}
.top-item-text3 span{
  color: #000;
  font-size: 12px;
  font-weight: normal;



}
.center-bottom{
  margin-top: 10px;
  width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    overflow-y: auto;
    flex: 1;


}
.center-left,.center-right{
    width: 50%;
    height: 100%;
    display: flex;
    flex-direction: column;


}
.center-left ul,.center-right ul{
    padding:0 10px 10px;


}
.center-left ul .item-text,.center-right ul .item-text{
    margin-top: 20px;
}
.title-img{
    display: block;
    width: 90%;
    height: 60px;
    margin: 0 auto;
}
.video-box{
  padding: 20px 0;
}
.video-main{
  position: relative;
  display: flex;
  justify-content: center;
  margin-top: 20px;

}
.look-more-video{
  margin-right: 0px px;
  position: absolute;
  right: -10px;
  top: 0;
  color: #000;
  cursor: pointer;
  font-size: 12px;

}

video{
  width: 80%;

}
.video-text{
  font-size: 12px;
  color: #000;
  text-align: left;
  line-height: 20px;
  margin-top: 15px;
}
.video-title1{
  text-align: right;
  margin-right: 15%;
}
.znwd {
    position: absolute;
    bottom: 10px;
    right: 0;
}
.znwd img{
  width: 120px;
}
.myplant{
        width: 35vw;
        height: 100vh;
        position: absolute;
        top: 0px;
        right: 0px;
        background: #fff;
        display: flex;
        z-index: 50;

    }
.myIframe{
        flex: 18;
  }
  .nodata{
    font-size: 14px;
    color: rgb(110, 112, 121);
    text-align: center;
    margin-top: 20px;
  }
  .qidai{
    height:100px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 18px;
    font-weight: bold;
  }
  ::v-deep .el-dialog__body{
    padding: 0;
  }
</style>