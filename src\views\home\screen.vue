<template>
  <div class="flex column w100 s-container">
    <img src="@/assets/screenImg/8.png" class="w100 top-img">
    <div class="to-menu" @click="toMenu">返回</div>
    <div class="s-box" :style="{'height': 'calc(100vh - ' + topImgHeight + 'px)'}">
      <el-row :gutter="20">
        <el-col :span="5">
          <div class="s-1">
            <img src="@/assets/screenImg/3.png" class="s-2" style="right: 0;" alt="">
            <img src="@/assets/screenImg/4.png" class="s-2" style="left: 0;">
            <div class="s-3">
              <img src="@/assets/screenImg/7.png" style="width: 94px;">
              <div class="flex j-s a-c s-4">
                <div class="flex a-c">
                  <div class="f18" style="width: 55px;">已结项</div>
                  <div class="s-5 center">{{ taskTotalItem.completedItems }}项</div>
                </div>
                <div class="flex a-c">
                  <div class="f18" style="width: 55px;">未结项</div>
                  <div class="s-5 center" style="background: #4FAEE5">{{ taskTotalItem.unfinishedItems }}项</div>
                </div>
              </div>
              <div class="s-6">
                <div id="huan-pic"></div>
                <div class="s-7">{{ taskTotalItem.rate }}%</div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="9">
          <div class="s-1">
            <img src="@/assets/screenImg/3.png" class="s-2" style="right: 0;" alt="">
            <img src="@/assets/screenImg/4.png" class="s-2" style="left: 0;">
            <div class="s-3 flex column" style="height: 272px;">
              <img src="@/assets/screenImg/17.png" style="width: 141px;">
              <div class="flex j-s a-c flex1 pt20">
                <div v-for="(item, index) in waterEchartsList" :key="index" class="flex column">
                  <img :src="item.img" style="width: 90px;"></img>
                  <div class="flex">
                    <span class="f16" style="line-height: 42px;">{{ item.title }}</span>
                    <div :style="{'color': item.color}">
                      <span style="font-size: 26px" class="fbold">{{ item.x_num }}</span>
                      <span class="f18">项</span>
                    </div>
                  </div>
                  <div :id="item.id" class="water-pic"></div>
                  <div class="flex a-c mt10">
                    <div class="s-8 mr5"></div>
                    <span class="f14">已完成 {{ item.completedNum }}</span>
                  </div>
                  <div class="flex a-c">
                    <div class="s-9 mr5"></div>
                    <span class="f14">未完成 {{ item.noCompletedNum }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="5">
          <div class="s-1">
            <img src="@/assets/screenImg/3.png" class="s-2" style="right: 0;" alt="">
            <img src="@/assets/screenImg/4.png" class="s-2" style="left: 0;">
            <div class="s-3 flex column" style="height: 272px;">
              <div class="flex column">
                <div class="flex a-c j-s">
                  <img src="@/assets/screenImg/18.png" style="width: 100px;">
                  <div class="f12 look-tip" @click="moreHandle(4)">查看更多></div>
                </div>
                <ul class="f14 lh26 h120 mt10" v-if="activityList.length > 0">
                  <li class="flex j-s" v-for="(item, index) in activityList" :key="index" @click="viewHandle(item.file)">
                    <span>{{ item.title }}</span>
                    <span>{{ item.createdTime }}</span>
                  </li>
                </ul>
                <div v-else class="center f14 h120 mt10" style="color: #6e7079">暂无数据</div>
              </div>
              <div class="flex column">
                <div class="flex a-c j-s">
                  <img src="@/assets/screenImg/19.png" style="width: 105px;">
                  <div class="f12 look-tip" @click="moreHandle(3)">查看更多></div>
                </div>
                <ul class="f14 lh26 mt10" v-if="rongheList.length > 0">
                  <li class="flex j-s" v-for="(item, index) in rongheList" :key="item.ID" @click="toDangYe(item.ID)">
                    <span>{{ item.ART_TITLE }}</span>
                    <span>{{ item.SHOW_DATE }}</span>
                  </li>
                </ul>
                <div v-else class="center f14 mt10" style="height: 50px;color: #6e7079">暂无数据</div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="5">
          <div class="s-1">
            <img src="@/assets/screenImg/3.png" class="s-2" style="right: 0;" alt="">
            <img src="@/assets/screenImg/4.png" class="s-2" style="left: 0;">
            <div class="s-3 flex column" style="height: 272px;">
              <div class="flex column">
                <div class="flex a-c j-s">
                  <img src="@/assets/screenImg/12.png" style="width: 90px;">
                  <div class="f12 look-tip" @click="moreHandle(1)">查看更多></div>
                </div>
                <ul class="f14 lh26 h120 mt10" v-if="AIList.length > 0">
                  <li class="flex j-s" v-for="(item, index) in AIList" :key="index" @click="viewHandle(item.file)">
                    <span>{{ item.title }}</span>
                    <span>{{ item.createdTime }}</span>
                  </li>
                </ul>
                <div v-else class="center f14 h120 mt10" style="color: #6e7079">暂无数据</div>
              </div>
              <div class="flex column">
                <div class="flex a-c j-s">
                  <img src="@/assets/screenImg/11.png" style="width: 105px;">
                  <div class="f12 look-tip" @click="moreHandle(2)">查看更多></div>
                </div>
                <ul class="f14 lh26 mt10" v-if="monthList.length > 0">
                  <li class="flex j-s" v-for="(item, index) in monthList" :key="index"
                      @click="viewHandle(item.file[0])">
                    <span>{{ item.title }}</span>
                    <span>{{ item.createdTime }}</span>
                  </li>
                </ul>
                <div v-else class="center f14 mt10" style="height: 50px;color: #6e7079">暂无数据</div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20" style="height: calc(100% - 325px);margin-top: 20px;">
        <el-col :span="7" class="h100">
          <div class="s-1 h100">
            <img src="@/assets/screenImg/3.png" class="s-2" style="right: 0;" alt="">
            <img src="@/assets/screenImg/4.png" class="s-2" style="left: 0;">
            <div class="s-3 h100" style="overflow: auto;">
              <img src="@/assets/screenImg/6.png" style="width: 206px;margin-bottom: 10px;">
              <div class="flex column mt10" v-for="(item, index) in list" :key="item.index">
                <div class="flex a-c">
                  <div class="s-11" style="display: inline-block;">{{ index + 1 }}</div>
                  <span class="f16 fbold" style="color:#F83523;">【{{ item.rate }}%】</span>
                  <span class="fbold f14">{{ item.targetIntor }}</span>
                </div>
                <div class="flex a-c j-s mt10 mb10">
                  <div class="center s-12"
                       :style="{'background': waterEchartsList.find(a => a.title == item.lineType).color}">
                    {{ item.lineType }}
                  </div>
                  <span class="f14" style="color: #909090">已完成任务数:{{
                      item.completedNum
                    }} 进行中任务数:{{ item.noCompletedNum }}</span>
                </div>
                <el-progress :percentage="item.rate" :stroke-width="6" color="#f73624" :show-text="false"></el-progress>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="17" class="h100">
          <div class="s-1 h100">
            <img src="@/assets/screenImg/3.png" class="s-2" style="right: 0;" alt="">
            <img src="@/assets/screenImg/4.png" class="s-2" style="left: 0;">
            <img src="@/assets/screenImg/9.png" class="s-13">
            <div class="s-3 flex column a-c h100" style="padding-bottom: 0;padding-top: 40px;">
              <img src="@/assets/screenImg/5.png" style="width: 180px;">
              <div id="zhu-pic"></div>
            </div>
            <p class="deadTime">{{ deadTime }}</p>
            <div class="f14" style="position: absolute;left: 30px;bottom: 27px;line-height: 24px;">
              第{{ newDateNum }}期
              <br/>
              第{{ oldDateNum }}期
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!-- 查看更多 -->
    <el-dialog :title="moreType==1 ? '往期AI周报' : moreType==2 ? '往期月度通报' : '往期活动安排'" :visible.sync="moreD" v-dialogDrag
               :close-on-click-modal="false" append-to-body width="40%">
      <div class="w100" style="padding-bottom: 70px;">
        <more-list v-if="moreD" :moreType="moreType"></more-list>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import 'echarts-liquidfill';
import {monthreportlist, mzlRequest} from "@/api/process";
import {mapState} from "vuex";
import moreList from "./moreList.vue";

let iportalUrl = process.env.NODE_ENV == 'production' ? 'http://iportal.ha.cmcc' : 'http://************:8088/portalweb/#'
export default {
  components: {moreList},
  data() {
    return {
      topImgHeight: 0,
      huanEcharts: null,
      zhuEcharts: null,
      waterEchartsList: [
        {
          id: 'water-1',
          number: 0,
          color: '#EB5E00',
          waterEcharts: null,
          title: '经营线',
          x_num: 0,
          completedNum: 0,
          noCompletedNum: 0,
          img: require('@/assets/screenImg/fazhan.png')
        },
        {
          id: 'water-2',
          number: 0,
          color: '#017FC8',
          waterEcharts: null,
          title: '网络线',
          x_num: 0,
          completedNum: 0,
          noCompletedNum: 0,
          img: require('@/assets/screenImg/zhuji.png')
        },
        {
          id: 'water-3',
          number: 0,
          color: '#F38F00',
          waterEcharts: null,
          title: 'IT线',
          x_num: 0,
          completedNum: 0,
          noCompletedNum: 0,
          img: require('@/assets/screenImg/funeng.png')
        },
        {
          id: 'water-4',
          number: 0,
          color: '#A33484',
          waterEcharts: null,
          title: '综合线',
          x_num: 0,
          completedNum: 0,
          noCompletedNum: 0,
          img: require('@/assets/screenImg/baozhang.png')
        },
        {
          id: 'water-5',
          number: 0,
          color: '#31A342',
          waterEcharts: null,
          title: '监督线',
          x_num: 0,
          completedNum: 0,
          noCompletedNum: 0,
          img: require('@/assets/screenImg/huhang.png')
        }
      ],
      taskTotalItem: {
        completedItems: 0,
        unfinishedItems: 0,
        rate: 0
      },
      list: [],
      deadTime: '',
      newDateNum: '',
      oldDateNum: '',
      rongheList: [],
      monthList: [],  // 通过state区分 state 0
      activityList: [], // 通过state区分 state 1
      AIList: [],
      moreType: '', // 1 AI周报 2 月度通报 3 无 4 活动安排
      moreD: false
    }
  },
  mounted() {
    this.getHuanPic()
    this.getWaterPic()
    this.getZhuPic()
    this.getList()
    this.getMonthReportList()
    this.getAIReportList()
    this.getDangYeList()
    setTimeout(() => {
      this.topImgHeight = document.querySelector('.top-img').offsetHeight
    }, 500)

    window.addEventListener('resize', this.handleScreenAuto);
  },
  destroyed() {
    window.removeEventListener('resize', this.handleScreenAuto);
  },
  computed: {
    ...mapState(['user'])
  },
  methods: {
    toDangYe(ID) {
      let url = `${iportalUrl}/news?id=${ID}&iv-user=${this.util.getRsa(this.user.user.username)}`
      window.open(url, '_blank')
    },
    moreHandle(type) {
      if (type == 3) {
        let url = `${iportalUrl}/newsList?catSid=100100205000&path=/iportal/ncms/category/infoTwo&name=党业融合专区`
        window.open(url, '_blank')
        return;
      }
      this.moreType = type
      this.moreD = true
    },
    async getDangYeList() {
      let res = await mzlRequest({
        url: `/action/largeScreen/ncmsInfo?page=1&rows=2&source=PC&cacheFlag=true&currentUserCode=${this.user.user.username}&catSid=100100205000&appcode=iportal&loginuser=${this.util.getRsa(this.user.user.username)}`
      });
      this.rongheList = res.data.content
    },
    viewHandle(file) {
      this.util.fileOpen(file.id)
    },
    async getMonthReportList() {
      let res = await monthreportlist({page: 1, size: 2}, {state: '0'});
      this.monthList = res.data.content.map(item => {
        item.createdTime = item.createdTime?.slice(5, 10)
        return {...item}
      })
      let res1 = await monthreportlist({page: 1, size: 4}, {state: '1'});
      this.activityList = res1.data.content.map(item => {
        item.createdTime = item.createdTime?.slice(5, 10)
        return {...item}
      })
    },
    async getAIReportList() {
      let res = await mzlRequest({
        url: '/action/largeScreen/queryAi?page=1&size=4'
      });
      this.AIList = res.data.content.map(item => {
        item.createdTime = item.file?.createdTime?.slice(5, 10)
        return {...item}
      })
    },
    toMenu() {
      this.$router.push('/mywork/processTask')
    },
    handleScreenAuto() {
      this.topImgHeight = document.querySelector('.top-img').offsetHeight
      this.huanEcharts.resize()
      setTimeout(() => {
        var width = document.getElementById('zhu-pic').clientWidth;
        var height = document.getElementById('zhu-pic').clientHeight;
        this.zhuEcharts.resize({
          width,
          height
        })
      }, 300)
    },
    async getList() {
      let res = await mzlRequest({
        url: '/action/largeScreen/queryTargetIntor?type=0'
      });

      this.list = res.data.map(item => {
        item.lineType = item.lineType.replace('条', '')
        item.rate = parseFloat(item.rate)
        return {
          ...item
        }
      })
    },
    async getZhuPic() {
      let res = await mzlRequest({
        url: '/action/largeScreen/queryTargetIntorNew?type=1'
      });

      let colorList = [
        {lineType: '经营条线', finishColor: '#e83204', oldColor: '#f2d1ca'},
        {lineType: '网络条线', finishColor: '#017fc8', oldColor: '#b8dbef'},
        {lineType: 'IT条线', finishColor: '#f39500', oldColor: '#f6d683'},
        {lineType: '综合条线', finishColor: '#a53583', oldColor: '#f6d9f7'},
        {lineType: '监督条线', finishColor: '#3da742', oldColor: '#cdf3ce'}
      ]
      let xAxisData = [], finishData = [], oldData = [];
      res.data.forEach(item => {
        xAxisData.push(item.targetIntor + '\n' + item.rate + '\n' + item.rateOld)
        finishData.push({
          value: item.rate,
          itemStyle: {color: colorList.find(i => i.lineType == item.lineType).finishColor}
        })
        oldData.push({
          value: item.rateOld,
          itemStyle: {color: colorList.find(i => i.lineType == item.lineType).oldColor}
        })
      })

      if (res.data?.length > 0) {
        let dataTime = res.data[0].deadline
        this.deadTime = '截止时间：' + new Date(dataTime).getFullYear() + '年' + (Number(new Date(dataTime).getMonth()) + 1) + '月' + new Date(dataTime).getDate() + '日'
        this.newDateNum = res.data[0].cycle
        this.oldDateNum = res.data[0].cycleOld
      }

      if (this.zhuEcharts != null && this.zhuEcharts != "" && this.zhuEcharts != undefined) {
        this.zhuEcharts.dispose() //销毁
      }

      var chartDom = document.getElementById('zhu-pic');
      this.zhuEcharts = echarts.init(chartDom);

      var option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        grid: {
          bottom: "114",
          top: '10%',
          left: '5%',
          right: '1%'
        },
        // toolbox: {
        //   feature: {
        //     dataView: {show: true, readOnly: false},
        //     magicType: {show: true, type: ['line', 'bar']},
        //     restore: {show: true},
        //     saveAsImage: {show: true}
        //   }
        // },
        legend: {
          data: ['Evaporation', 'Precipitation', 'Temperature']
        },
        xAxis: [
          {
            type: 'category',
            data: xAxisData,
            axisPointer: {
              type: 'shadow'
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              rotate: 0,
              interval: 0,
              fontSize: 13,
              formatter: function (value) {
                var arr = value.split('\n')
                var ret = ''
                var maxLength = 5
                var valLength = arr[0].length
                var rowN = Math.ceil(valLength / maxLength)
                if (rowN > 1) {
                  for (var i = 0; i < rowN; i++) {
                    var temp = ''
                    var start = i * maxLength
                    var end = start + maxLength
                    temp = arr[0].substring(start, end) + '\n'
                    ret += temp
                  }
                  if (ret.length < 13) {
                    ret += '\n'
                  }
                  return ret + '\n' + arr[1] + '\n\n' + arr[2]
                } else {
                  return value
                }
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            // name: 'Precipitation',
            min: 0,
            max: 100,
            axisLabel: {
              // formatter: '{value} ml'
            }
          }
        ],
        series: [
          {
            name: `第${this.oldDateNum}期完成进度`,
            type: 'bar',
            label: {
              show: true, // 显示标签
              position: 'top', // 标签位置
              // 可以通过 formatter 自定义显示格式
              formatter: '{c}', // {c} 表示数据值
              fontSize: 10
            },
            tooltip: {
              valueFormatter: function (value) {
                return value + ' %';
              }
            },
            data: oldData,
            barWidth: '30%',
          },
          {
            name: `第${this.newDateNum}期完成进度`,
            type: 'bar',
            label: {
              show: true, // 显示标签
              position: 'top', // 标签位置
              // 可以通过 formatter 自定义显示格式
              formatter: '{c}', // {c} 表示数据值
              fontSize: 10
            },
            tooltip: {
              valueFormatter: function (value) {
                return value + ' %';
              }
            },
            data: finishData,
            barWidth: '30%',
          }
        ]
      };

      setTimeout(() => {
        var width = chartDom.clientWidth;
        var height = chartDom.clientHeight;
        this.zhuEcharts.resize({
          width,
          height
        })
        option && this.zhuEcharts.setOption(option);
      }, 300)
    },
    async getWaterPic() {
      let res = await mzlRequest({
        url: '/action/largeScreen/schedule'
      });

      this.waterEchartsList.forEach((item, index) => {
        item.number = parseFloat(res.data[index].rate)
        item.x_num = parseFloat(res.data[index].tasks)
        item.title = res.data[index].lineType
        item.completedNum = res.data[index].completedNum
        item.noCompletedNum = res.data[index].noCompletedNum
      })

      for (let i = 0; i < this.waterEchartsList.length; i++) {
        let item = this.waterEchartsList[i]
        if (item.waterEcharts != null && item.waterEcharts != "" && item.waterEcharts != undefined) {
          item.waterEcharts.dispose() //销毁
        }

        var chartDom = document.getElementById(item.id);
        item.waterEcharts = echarts.init(chartDom);

        var option = {
          series: [{
            name: '任务攻坚进度',
            type: 'liquidFill',
            radius: '96%',
            center: ['50%', '50%'],
            data: [item.number / 100],
            label: {
              normal: {
                textStyle: {
                  color: item.number > 70 ? '#ffffff' : item.color,
                  fontSize: 18,
                }
              }
            },
            color: [item.color],
            backgroundStyle: {
              color: '#FFFFFF'
            },
            outline: {
              borderDistance: 3,
              itemStyle: {
                borderWidth: 1,
                borderColor: item.color,
              }
            },
            amplitude: 2,
          }]
        };

        option && item.waterEcharts.setOption(option);
      }
    },
    async getHuanPic() {
      let res = await mzlRequest({
        url: '/action/largeScreen/getDetailForm'
      })

      Object.assign(this.taskTotalItem, res.data)
      this.taskTotalItem.rate = ((this.taskTotalItem.completedItems / (this.taskTotalItem.completedItems + this.taskTotalItem.unfinishedItems)) * 100).toFixed(2)

      if (this.huanEcharts != null && this.huanEcharts != "" && this.huanEcharts != undefined) {
        this.huanEcharts.dispose() //销毁
      }

      var chartDom = document.getElementById('huan-pic');
      this.huanEcharts = echarts.init(chartDom);

      var finishNum = this.taskTotalItem.completedItems; //已完成
      var nofinishNum = this.taskTotalItem.unfinishedItems; //未完成
      var option

      option = {
        tooltip: {
          show: false,
        },
        series: [
          {
            name: '任务总览',
            type: 'pie',
            silent: true,
            radius: ['150%', '190%'],
            startAngle: 180,
            center: ['50%', '100%'],
            roseType: false,
            labelLine: {
              show: false,
            },
            data: [
              {
                value: finishNum,
                // name: "男生",
                itemStyle: {
                  normal: {
                    color: '#ED7A7A'
                  },
                },
              },
              {
                value: nofinishNum,
                // name: "女生",
                itemStyle: {
                  normal: {
                    color: '#3E9FD6'
                  },
                },
              },
              {
                value: finishNum + nofinishNum,
                name: '',
                label: {
                  show: false,
                },
                labelLine: {
                  show: false,
                },
                itemStyle: {
                  normal: {
                    color: 'transparent',
                    borderWidth: 0,
                    shadowBlur: 0,
                  },
                },
              },
            ],
          }
        ],
      };

      option && this.huanEcharts.setOption(option);
    }
  }
}
</script>

<style scoped>
.h120 {
  height: 120px;
}

ul li {
  cursor: pointer;
}

ul li span:first-child {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

ul li:hover {
  color: #ef2f1d;
}

.look-tip {
  color: #6e7079;
  cursor: pointer;
}

.s-container {
  width: 100vw;
  height: 100vh;
  background: #fff1f0;
}

.s-box {
  padding: 20px;
}

#zhu-pic {
  width: 100%;
  height: calc(100% - 20px);
}

.s-13 {
  position: absolute;
  left: 50%;
  top: 15px;
  transform: translateX(-50%);
  z-index: 5;
  width: 466px;
}

.s-12 {
  width: 41px;
  height: 15px;
  color: #FFFFFF;
  border-radius: 2px;
  font-size: 11px;
  line-height: 1;
}

.s-11 {
  width: 20px;
  height: 20px;
  line-height: 20px;
  border-radius: 50%;
  background: #F83523;
  font-size: 12px;
  color: #FFFFFF;
  text-align: center;
}

.s-10 {
  width: 112px;
}

.s-8 {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #fe6b63;
  margin-left: 5px;
}

.s-9 {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #4faee4;
  margin-left: 5px;
}

.water-pic {
  width: 86px;
  height: 86px;
}

.s-7 {
  position: absolute;
  left: 50%;
  bottom: -104px;
  transform: translateX(-50%);
  z-index: 9;
  width: 210px;
  height: 210px;
  background: #FFF1F0;
  border-radius: 50%;
  font-size: 30px;
  color: #ED7A7A;
  font-weight: bold;
  line-height: 120px;
  text-align: center;
}

.s-6 {
  width: 100%;
  height: 160px;
  position: relative;
  overflow: hidden;
}

#huan-pic {
  width: 100%;
  height: 160px;
}

.s-5 {
  width: 80px;
  height: 28px;
  background: #ED7A7A;
  border-radius: 2px;
  font-size: 18px;
  font-weight: bold;
  color: #FFFFFF;
  margin-left: 10px;
  border-radius: 6px;
}

.s-4 {
  padding: 20px 0 10px;
  justify-content: space-between;
}

.s-3 {
  background: #FFFFFF;
  width: 100%;
  padding: 16px 13px;
  border-radius: 10px;
  overflow-x: auto;
}

.s-2 {
  width: 11px;
  height: 11px;
  position: absolute;
  bottom: 0;
  z-index: 10;
}

.s-1 {
  padding: 15px 10px;
  border: 1px solid #F5D3D3;
  position: relative;
}

.s-1:before {
  content: '';
  position: absolute;
  width: 11px;
  height: 11px;
  background-image: url("../../assets/screenImg/1.png");
  background-size: 100% 100%;
  left: 0;
  top: 0;
  z-index: 10;
}

.s-1:after {
  content: '';
  position: absolute;
  width: 11px;
  height: 11px;
  background-image: url("../../assets/screenImg/2.png");
  background-size: 100% 100%;
  right: 0;
  top: 0;
  z-index: 10;
}

.h100 {
  height: 100%;
}

.to-menu {
  position: fixed;
  right: 20px;
  top: 3.8%;
  z-index: 99;
  cursor: pointer;
  color: #FFFFFF;
  width: 96px;
  height: 36px;
  text-align: center;
  line-height: 36px;
  box-shadow: inset 0 0 0 2px #ec2c1b;
  transition: color 1s;
  overflow: hidden;
  background: #ec2c1b;
}

.to-menu::before,
.to-menu::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  top: 0;
  left: 0;
  box-sizing: border-box;
  border: 2px solid transparent;
}

.to-menu:hover {
  color: #FFFFFF;
}

.to-menu:hover::before {
  transition: width .5s, height .5s, border-bottom-color 0s;
  transition-delay: .5s, 0s, .5s;
  width: 96px;
  height: 36px;
  border-left: 2px solid #FFFFFF;
  border-bottom: 2px solid #FFFFFF;
}

.to-menu:hover::after {
  transition: width .5s, height .5s, border-right-color .5s;
  transition-delay: 0s, .5s, .5s;
  width: 96px;
  height: 36px;
  border-top: 2px solid #FFFFFF;
  border-right: 2px solid #FFFFFF;
}

.deadTime {
  position: absolute;
  right: 4%;
  top: 8%;
  color: #fc6f66;
  font-weight: 700;

}
</style>