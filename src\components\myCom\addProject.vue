<template>
  <div class="content">
    <!-- 业务表单 -->
    <div class="tableForm">
      <sb-el-form
        ref="appForm"
        :form="appForm"
        v-model="appFormValue"
        :disabled="appForm.formDisabled"
        @chooseFun="chooseFun"
        @uploadFileList="uploadFileList"
        :on-ok="handleDoFun"
      >
      </sb-el-form>
      <div style="padding: 20px 0; margin-left: 80%">
        <el-button @click="handleUp('no')" type="primary" size="small"
          >关闭</el-button
        >
        <el-button type="primary" @click="handleUp('yes')" size="small"
          >确认</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
let defaultAppFormValue = {
  pmInsId: "",
  id: "",
  blank: "blank",
};
import { uploadProcessFiles } from "@/api/public";
import { findProjectById } from "@/api/zjMessage/zjMessage";

export default {
  name: "addProject",
  props: {
    gps: {
      type: Object,
      default() {
        return this.$route.query;
      },
    },
    editValue: { type: Object },
    types: { type: String },
    isAdmin: {  
      type: [String, Boolean], // 现在可以接受 String 或 Boolean  
      default: false // 提供一个默认值  
    } ,
    rowData: { type: Object },
    rowId: { type: String },
    currentMan: { type: String },
    currentuserMan: { type: String },
  },
  computed: {},
  data() {
    return {
      // 业务表单
      initValue: {},
      appFormValue: Object.assign({}, defaultAppFormValue),
      appForm: {
        formDisabled: false,
        labelWidth: "200px",
        inline: true,
        formItemList: [
          {
            class: "c12",
            label: "人员姓名",
            key: "truename",
            type: "user",
            rule: { required: true },
          },
          {
            class: "c12",
            label: "开始时间",
            key: "startDate",
            type: "date",
            subtype: "date",
            valueFormat: "yyyy-MM-dd",
            disabled: false,
            rule: { required: true },
          },
          {
            class: "c12",
            label: "结束时间",
            key: "endDate",
            type: "date",
            subtype: "date",
            valueFormat: "yyyy-MM-dd",
            disabled: false,
            rule: { required: true },
          },
          {
            class: "c12",
            label: "项目名称",
            key: "name",
            type: "input",
            placeholder: "",
            rule: { required: true },
          },
          {
            class: "c12",
            label: "项目成果内容",
            key: "achievement",
            type: "input",
            placeholder: "",
            rule: { required: true },
          },
          {
            class: "c12",
            label: "附件",
            key: "annex",
            type: "sbUpload",
            btnText: "+",
            fun: "uploadFileList",
            listType: "text",
            multiple: true,
            rule: { required: true },
          },
        ],
      },
    };
  },
  created() {
    console.log(11111, this.types);
    this.initValue = {
      username: this.$store.getters.user.username,
      truename: this.$store.getters.user.truename,
    };
    this.manageinitValue = {
      username: this.currentuserMan,
      truename: this.currentMan,
    };

    if (this.types == "edit") {
      this.findProjectByIds();
    } else if (this.types == "add" && !this.isAdmin) {
      // 专家信息上报自己上传信息
      this.appFormValue = Object.assign(this.initValue);
    }else{
      // 管理员选人上报信息
      this.appFormValue = Object.assign(this.manageinitValue);
    }
  },
  methods: {
    handleUp(op) {
      if (this.types == "add") {
        if (op == "yes") {
        let formName = "appForm";
        this.$refs[formName].$children[0].validate((valid) => {
          console.log(valid, "9999");
          console.log(this.appFormValue, "this.appFormValue");
          if (valid) {
            this.$emit("event", this.appFormValue);
            this.$emit("dialogClose");
          } else {
            this.$message({
								message: "数据必填项校验不通过",
								type: "warning",
								duration: 1500
							});
            console.log("error submit!!");
            return false;
          }
        });
      } else {
        console.log('关闭');
        this.$emit("dialogClose");
      }
      }else if (this.types == "edit") {
        // 如果是编辑页面的点击确定取消
        if (op == "yes") {
        let formName = "appForm";
        this.$refs[formName].$children[0].validate((valid) => {
          console.log(valid, "9999");
          console.log(valid, "编辑页面");
          if (valid) {
            this.$emit("update", this.appFormValue);
            this.$emit("dialogClose");
          } else {
            this.$message({
								message: "数据必填项校验不通过",
								type: "warning",
								duration: 1500
							});
            console.log("error submit!!");
            return false;
          }
        });
      } else {
        this.$emit("dialogClose");
      }
      }

    },
    // 获取编辑数据
    findProjectByIds() {
      findProjectById(this.rowId).then((res) => {
        this.appFormValue = Object.assign(res.data, this.initValue);
      });
    },
    handleDoFun(obj, fun, data) {
      //单独处理上传附件
      for (var i = 0; i < this.appForm.formItemList.length; i++) {
        if (this.appForm.formItemList[i].type == "sbUpload") {
          this.appForm.formItemList[i].disabled = false;
        }
      }
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    },
    fetchRowData() {},
    chooseFun(obj, data) {
      // console.log(obj, data,'999');
    },
    uploadFileList(obj) {
      uploadProcessFiles(obj.formData)
        .then((res) => {
          obj.content.onSuccess(res, obj.content.file, []);
        })
        .catch((error) => {
          obj.content.onError();
        });
    },
  },
};
</script>

<style scoped>
::v-deep .el-form-item .el-form-item__label[for="applyUser"] {
  line-height: 15px !important;
}

.tableForm {
  border-left: none;
}

::v-deep .tableForm .el-input__inner,
.tableForm .el-textarea__inner {
  border-right: 1px solid #e8e8e8;
  border-bottom: 1px solid #e8e8e8;
}
::v-deep .tableForm .upload_D {
  border-right: 1px solid #e8e8e8;
  border-bottom: 1px solid #e8e8e8;
}
</style>