<template>
  <div class="app-container">
    <ul class="content-wrap" v-if="isfetch">
      <template v-if="employeeList.length > 0">
        <li v-for="(item, index) in employeeList" :key="item.id">
          <div class="img-wrap">
            <img :src="employeeImg" alt="">
          </div>
          <div class="info">
            <div class="title">{{ item.title }}</div>
            <div style="flex: 1;">
              <div class="text">{{ item.content }}</div>
            </div>
            <div class="opts">
              <div class="observe" @click.stop="subHandle(item)">执行</div>
            </div>
          </div>
        </li>
      </template>
      <div class="center w100" style="color: #aaaaaa;padding-top: 40px;" v-else>
        暂无数据
      </div>
    </ul>
    <input type="file" ref="importRef" accept=".xlsx, .xls" @change="importChange" style="display: none">
  </div>
</template>
<script>
import employeeImg from "@/assets/images/robot1.png"
import {mzlRequest} from "@/api/process";
import {uploadProcessFiles} from "@/api/public";
import conclusionSend from "@/views/expertTalents/conclusionSend.vue";

export default {
  name: "application",
  components: {conclusionSend},
  data() {
    return {
      nowTime: this.util.getNow("yyyy-MM-dd hh:mm:ss"),
      roleType: '', // 0：上传附件 1上传content内容框
      employeeList: [],
      employeeImg: employeeImg,
      mdelId: '',
      isfetch: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async importChange(event) {
      const files = event.target.files
      if (files.length > 0) {
        const loading = this.$loading({
          lock: true,
          text: '执行中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        const data = new FormData()
        data.append('file', files[0])
        try {
          let res = await uploadProcessFiles(data)
          await mzlRequest({
            url: '/action/numerical/addIntelligenceExcel',
            data: {
              modelId: this.mdelId,
              applyUserName: this.$store.getters.user.username,
              applyTrueName: this.$store.getters.user.truename,
              contentFile: res.data.sysFiles
            }
          })
          setTimeout(() => {
            this.$router.push('/expertTalents/executionRecord')
          }, 300)
        } finally {
          this.$refs.importRef.value = ''
          loading.close()
        }
      }
    },
    subHandle(item) {
      if (item.type == 0) {
        this.$confirm('请您上传相关文件后自动生成周报PPT', '温馨提示', {
          confirmButtonText: '点击上传'
        }).then(() => {
          this.mdelId = item.id
          this.$refs.importRef.click()
        })
      } else {
        this.$prompt('请输入工单编号', '提示', {
          confirmButtonText: '执行',
          cancelButtonText: '取消',
          inputPlaceholder: '格式：20240920-07342970',
          inputValidator: value => {
            if (!value) {
              return '请输入工单编号'
            }
          }
          // inputPattern: /[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/,
          // inputErrorMessage: '邮箱格式不正确'
        }).then(async ({ value }) => {
          const loading = this.$loading({
            lock: true,
            text: '执行中',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          await mzlRequest({
            url: '/action/numerical/addContentExcel',
            data: {
              modelId: item.id,
              applyUserName: this.$store.getters.user.username,
              applyTrueName: this.$store.getters.user.truename,
              content: value
            }
          })
          loading.close()
          setTimeout(() => {
            this.$router.push('/expertTalents/executionRecord')
          }, 300)
        }).catch(() => {});
      }
    },
    async getList() {
      let res = await mzlRequest({
        url: `/action/numerical/queryModel?source=PC&page=1&size=999`
      })
      this.isfetch = true
      this.employeeList = res.data;
    }
  }

};
</script>
<style scoped>
.app-container {
  padding: 20px;
}

.content-wrap {
  display: flex;
  flex-wrap: wrap;
}

.content-wrap li {
  width: 400px;
  height: 160px;
  display: flex;
  background-color: #fff;
  padding: 8px;
  border-radius: 6px;
  overflow: hidden;
  margin: 0 10px 16px;
  border: 1px solid #E6E6E6;
}

.content-wrap li:nth-child(4n) {
  margin-right: 0;
}

.content-wrap li .img-wrap {
  width: 120px;
  height: 100%;
  border-radius: 6px;
  overflow: hidden;
  padding: 12px;
}

.content-wrap li .img-wrap img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.content-wrap li:nth-child(3n+1) .img-wrap {
  background: linear-gradient(to bottom, #eeedfd, #dad8ff);
}

.content-wrap li:nth-child(3n+2) .img-wrap {
  background: linear-gradient(to bottom, #f3fdfe, #dff2f6);
}

.content-wrap li:nth-child(3n) .img-wrap {
  background: linear-gradient(to bottom, #fff8f2, #fdeddd);
}

.content-wrap li .info {
  width: calc(100% - 120px);
  padding: 8px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.content-wrap li .info .title {
  font-size: 16px;
  font-weight: bold;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.content-wrap li .info .text {
  margin-top: 4px;
  font-size: 14px;
  display: -webkit-box;
  /* 使用弹性盒子布局模型*/
  -webkit-line-clamp: 3;
  /* 限制在3行*/
  -webkit-box-orient: vertical;
  /* 垂直方向的子元素排列*/
  overflow: hidden;
  /* 隐藏溢出的内容*/
  text-overflow: ellipsis;
  /* 多行时显示省略号*/

}

.content-wrap li .info .opts {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.content-wrap li .info .opts .observe {
  font-size: 12px;
  background-color: rgba(192, 0, 0, .8);
  padding: 2px 12px;
  border-radius: 4px;
  color: #FFFFFF;
  cursor: pointer;
}

.content-wrap li .info .opts .observe-num {
  font-size: 12px;
  margin-left: 8px;
}

</style>