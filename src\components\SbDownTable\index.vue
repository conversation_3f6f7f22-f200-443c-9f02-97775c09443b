<template>
  <el-select v-model="value" v-bind="$attrs" v-on="$listeners" :size="item.size || 'small'" :placeholder="item.placeholder || item.label || '请输入'" :disabled="item.disabled || false" :readonly="item.readonly || false" :autosize="item.autosize || false" style="width:100%;" @visible-change="visibleChange">
    <template #empty>
      <div style="padding:10px">
        <sb-el-table :table="table" @getList="getList" @updateTableData="updateTableData" :on-ok="handleDoFun"></sb-el-table>
        <div style="display: flex;
    justify-content: flex-end;
    padding: 10px 0px 20px 0px;">
          <el-button type="primary" @click="handleSubmitGroup">确 定</el-button>
        </div>
      </div>
    </template>
  </el-select>
</template>
<script>
import { getDataByApi } from "@/api/apply/application";
import util from "@/assets/js/public";
export default {
  props: {
    item: {
      type: Object,
      required: true
    },
    dialogData: {
      type: Object,
      required: true
    },
    appFormValue: {
      type: Object,
      required: true
    },
    value: {
      type: undefined,
      default: null,
    },
  },
  data() {
    return {
      values: "",
      table: {
        modulName: "dialogs-选择数据", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: true, // 是否有复选框
        showIndex: false, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: true, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "90px",
          formItemList: [
            { label: "关键词", key: "appName", type: "input" },
            // {label: "流程类型",key: "pmInstType",type: "select",dictType: "processType"}
          ],
        },
        tr: [],
        // hasSetup:true,
        // setup:[],
        processType: [],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "600px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: false, //是否有操作列表
        operation: {
          width: "100",
          fixed: "right",
          data: [
          ],
        },
        hasPagination: true,
        listQuery: {},
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
    };
  },
  created() {
    if (this.dialogData.options.length > 0) {
      this.dialogData.options.forEach((item) => {
        item.id = item.value
        item.label = item.name
        item.prop = item.value
      })
      this.table.tr = this.dialogData.options
    } else {
      this.table.tr = []
    }

    if (this.dialogData.paramsArr.length > 0) {
      this.dialogData.paramsArr.forEach((item) => {
        item.type = "input"
        item.label = item.CHINESE_NAME ? item.CHINESE_NAME : item.API_PARAM_NAME
        item.key = item.API_PARAM_NAME
      })
      this.table.queryForm.formItemList = this.dialogData.paramsArr
    } else {
      this.table.queryForm.formItemList = []
    }
  },
  methods: {
    handleSubmitGroup() {
      let revealss = util.getallValue(this.table.multipleSelection, this.dialogData.reveals)
      let storess = util.getallValue(this.table.multipleSelection, this.dialogData.stores)
      this.$emit("chooseData", storess);
    },
    visibleChange() {
      this.getList();
    },
    // 查询列表
    getList() {
      this.dialogData.parameter.forEach((item) => {
        item.DEFAULT_VALUE = item.formsId ? (this.appFormValue[item.formsId] || this.table.listQuery[item.API_PARAM_NAME]) : this.table.listQuery[item.API_PARAM_NAME]
      })
      this.table.loading = true;
      getDataByApi(this.dialogData.dialogData, this.dialogData.parameter).then((res) => {
        this.table.loading = false;
        this.table.data = res.data;
      }).catch((err) => {
        this.table.loading = false;
      });
    },
    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }
};
</script>
<style scoped>
.selecTree {
  max-height: 50vh;
  overflow: auto;
  padding: 5px;
}
.el-select {
  width: 100%;
}
.slotSpan {
  font-size: 14px;
}
.slotSpan b {
  font-weight: normal;
  font-size: 12px;
  color: #999;
}
.selecTree ::v-deep .el-tree-node__content {
  font-size: 14px;
}
</style>