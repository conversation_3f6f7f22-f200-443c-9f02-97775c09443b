import request from "@/assets/js/request";
import util from "@/assets/js/public";
import store from "@/store";

// 获取Lie表相关数据
export function getUserList(params) {
    let url = util.toUrl(`/${process.env.VUE_APP_APPCODE}/uums/sys/userinfo/findRoleNameIsARoleDim`, params || {})+`&appcode=${process.env.VUE_APP_APPCODE}`;
    return request({
        url: url,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 获取组织树数据
export function findRootAndNextRoot(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/org/findRootAndNextRoot?appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
        // data: params
    });
}
// 获取组织树关数据
export function findSonByParentOrgId(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/org/findSonByParentOrgId?appcode=${process.env.VUE_APP_APPCODE}&orgCode=${params.orgCode}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 获取职务树关数据
export function findAllToTree(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/position/findAllToTree`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
export function addUser(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/userinfo/create?appcode=${process.env.VUE_APP_APPCODE}&keyword=${store.state.user.user.username}&keyType=username`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
export function updateUserCustom(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/userinfo/updateUser?appcode=${process.env.VUE_APP_APPCODE}&keyword=${store.state.user.user.username}&keyType=username`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
export function deleteUserCustom(id) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/userinfo/deleteUserInOrgNormal?id=${id}&appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
    });
}
export function findByIdUser(id) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/userinfo/getRsa?id=${id}&appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
    });
}