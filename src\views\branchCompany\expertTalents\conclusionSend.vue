<template>
  <div :class="gps.location ? 'w99' : 'p10'">
    <div class="pageInfo" v-if="!gps.action">
      <template v-if="gps.type == 'task' || types == 'send'">
        <span class="btn nextBtn" @click="handleNextBtn(1)">
          <svg-icon icon-class="random"></svg-icon>
          <font>{{gps.location == 'leader' ? '确认归档' : '提交'}}</font>
        </span>
        <span v-if="gps.location && gps.location != 'yqff.start'" class="btn abolish" @click="handleNextBtn(0)">
          <svg-icon icon-class="shanchu"></svg-icon>
          <font>退回修改</font>
        </span>
      </template>
      <span class="btn flowTrack" @click="handleFlowTrack()">
        <svg-icon icon-class="liuchengtu"></svg-icon>
        <font>流程跟踪</font>
      </span>
      <span class="btn optClose" @click="handleOptClose()">
        <svg-icon icon-class="close"></svg-icon>
				<font>关闭</font>
			</span>
    </div>

    <!-- 业务表单 -->
    <div class="message tableForm">
      <div class="orderTitle" style="">豫起奋发 更加出彩</div>
      <sb-el-form ref="appForm" :form="appForm" v-model="appFormValue" :disabled="appForm.formDisabled" :on-ok="handleDoFun">
        <template v-slot:taskInfo>
          <div class="plf15 f12">
            <!-- <b>{{ appFormValue.targetIntor }}</b> -->
            <span class="ml10">{{ appFormValue.taskInfo }}</span>
          </div>
        </template>
        <template v-slot:taskJcInfo>
          <span class="f12 lh20" style="padding: 6px 15px;">{{ appFormValue.taskJcInfo }}</span>
        </template>
      </sb-el-form>
      <div class="m-title">
        <span>结项说明</span>
<!--        <span style="font-weight: normal;">（完成率大于或等于100%时方可发起结项申请）</span>-->
      </div>
      <sb-el-form ref="changeForm" :form="changeForm" v-model="changeFormValue" :disabled="changeForm.formDisabled" :on-ok="handleDoFun"
                  @uploadFileList="uploadFileList">
        <template v-slot:progress>
          <div class="flex column w100 f-1">
            <el-input type="textarea" v-model="changeFormValue.progress" placeholder="请输入进展情况"></el-input>
            <span class="f-2">请输入 <i class="red">20</i> - <i class="red"> 80</i> 字，您已输入 <i class="red">{{ changeFormValue.progress?.length || 0 }}</i> 字。</span>
          </div>
        </template>
        <template v-slot:branchCompanySituation>
          <div class="flex column w100 f-1">
            <el-input type="textarea" v-model="changeFormValue.branchCompanySituation" placeholder="请输入地市分公司情况"></el-input>
            <span class="f-2">请输入 <i class="red">0</i> - <i class="red"> 40</i> 字，您已输入 <i class="red">{{changeFormValue.branchCompanySituation?.length || 0}}</i> 字。</span>
          </div>
        </template>
        <template v-slot:completionRate>
          <el-input type="text" v-model="changeFormValue.completionRate" placeholder="请输入正整数" maxlength="3" @input="numberCheck">
            <template slot="append">%</template>
          </el-input>
        </template>
      </sb-el-form>
    </div>

    <!-- 流程跟踪 -->
    <el-dialog title="流程跟踪" :visible.sync="trackD" v-dialogDrag :close-on-click-modal="false" append-to-body width="1200px">
      <process-track :key="trackKey" :gps="gps"></process-track>
    </el-dialog>

    <!-- 选择人员、部门 -->
    <el-dialog title="选择人员" :visible.sync="showUserDialog" v-dialogDrag :close-on-click-modal="false" append-to-body top="10vh" width="50%">
      <conclusionChooseUser v-if="showUserDialog" :gps="gps" :appFormValue="appFormValue" :item="itemUser" :key="itemUser.key"
                            @closeshowDialogFun="showUserDialog = false" :cd="cd"
                            @flowdata="flowdata"/>
    </el-dialog>
  </div>
</template>
<script>
import {
  uploadProcessFiles,
} from "@/api/public";
import {
  conclusionDetail,conclusionSubmit,startProcess
  
} from "@/api/branchCompany";
let defaultAppFormValue = {
  pmInsId: "",
  id: "",
  blank: "blank",
  applyNumber: '',
  title: ''
};

import ProcessNext from '@/components/Process/ProcessNext.vue'
import conclusionChooseUser from "@/components/conclusionChooseUserFen.vue";
import ProcessTrack from "@/components/Process/ProcessTrack.vue";


export default {
  name: "application",
  props: {
    href: {
      type: Object,
      default() {
        return {};
      }
    },
    showBtn: {
      type: Object,
      // default(){
      // 	return {};
      // }
    },
    // 流程跟踪
    doFlowTrack: {
      type: Function
    },
    // 查看意见
    doViewComments: {
      type: Function
    },
    // 关闭
    dialogClose: {
      type: Function
    },
    types: {
      type: String,
      default: ''
    },
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  components: {
    ProcessTrack,
    ProcessNext,
    conclusionChooseUser
  },
  data() {
    return {
      gps: this.href,
      pmInsId: '',
      orderId: '', // 保存草稿 重置功能用

      processD: false,
      pnKey: 0,
      clickFlag: true, //防止多次点击

      nowTime: this.util.getNow("yyyy-MM-dd hh:mm:ss"),

      showUserDialog: false,
      itemUser: {key: 0, nodeKey: 'id', mulitple: false, type: ''}, // type 1 提交 2 退回修改
      cd: [],

      // 业务表单
      initValue: {},
      appFormValue: Object.assign({}, defaultAppFormValue),
      appForm: {
        formDisabled: true,
        labelWidth: "200px",
        inline: true,
        formItemList: [
          {class: "c8", label: "工单标题", key: "title", type: "input", show: false},
          {class: "c4", label: "工单编号", key: "applyNumber", type: "input", show: false},
          {class: "c4", label: "责任部门", key: "taskHostUnitName", type: "input"},
          {class: "c4", label: "部门接口人", key: "interfaceAdminName", type: "input"},
          {class: "c4", label: "时间要求", key: "finishDataStr", type: "input"},
          {class: "c12", label: "目标", key: "actionInfo", type: "input"},
          {class: "c12", label: "任务", key: "taskInfo", type: "template", template: 'taskInfo'},
          {class: "c12", label: "举措", key: "taskJcInfo", type: "template", template: 'taskJcInfo'}
        ],
      },

      formData: {},

      // 流程图
      trackKey: 0,
      trackD: false,

      changeFormValue: Object.assign({}),
      changeForm: {
        formDisabled: false,
        labelWidth: "200px",
        inline: true,
        formItemList: [
          {class: "c4", label: "任务负责人", key: "feedbackPersonTrueName", type: "input", rule: {required: true}, disabled: true},
          {
            class: "c4",
            label: "是否存在延期风险",
            key: "isExtension",
            type: "select",
            rule: {required: true},
            options: [{label: '是', value: '是'}, {label: '否', value: '否'}]
          },
          {
            class: "c4",
            label: "完成率（指标）",
            key: "completionRate",
            type: "template",
            template: 'completionRate',
            rule: {required: true}
          },
          {class: "c12", label: "进展情况", key: "progress", type: "template", template: "progress", rule: {required: true}},
          // {class: "c12", label: "地市分公司情况", key: "branchCompanySituation", type: "template", template: "branchCompanySituation", rule: {required: true}},
          {
            class: "c12",
            label: "附件",
            key: "sysFiles",
            type: "sbUpload",
            btnText: "",
            fun: "uploadFileList",
            listType: "text",
            multiple: true,
            rule: {required: false}
          }
        ],
      },
    }
  },
  created() {
    var query = this.util.getQueryString();
    this.gps = Object.assign(this.gps, query);
    console.log('gps', JSON.parse(JSON.stringify(this.gps)));

    this.initValue = {
      applyUser: this.$store.getters.user.truename,
      applyUserName: this.$store.getters.user.username,
      belongCompanyName: this.$store.getters.user.belongCompanyName,
      belongDepartmentName: this.$store.getters.user.belongDepartmentName,
      applyPhone: this.$store.getters.user.preferredMobile,
      applyTime: this.nowTime
    };
    this.appFormValue = Object.assign({}, defaultAppFormValue, this.initValue);

    this.initFun(); //初始化
  },
  methods: {
    numberCheck(val) {
      setTimeout(() => {
        this.changeFormValue['completionRate'] = this.util.checkInput(val)
      }, 0)
    },
    async flowdata(arr) {
      if (arr.length == 0) {
        this.$message.error('请选择人员')
        return
      }

      let username
      if (arr.length == 1) {
        username = arr[0].id
      } else {
        username = arr.map(item => item.id)?.join(',')
      }


      let params = {
        location: this.gps.location || 'yqff.start',
        pmInsId: this.gps.pmInsId || '',
        taskId: this.gps.taskId || '',
        type: this.itemUser.type,
        nextUser: username
      }
      let data = {
        pmInsId: this.gps.location ? this.appFormValue.pmInsId : '',
        id: this.gps.location ? this.appFormValue.id : '',
        pmInsType: 'I',
        title: this.gps.location ? this.appFormValue.title : '',
        checkContentId: this.rowData.id,
        checkInfo: Object.assign({}, this.changeFormValue),
        interfaceAdminCode: this.appFormValue.interfaceAdminCode,
        interfaceAdminName: this.appFormValue.interfaceAdminName
      }
      await conclusionSubmit(params, data)
      this.afterClick()
    },
    // 流程图
    handleProcessImg() {
      this.diagramKey++;
      this.diagramD = true;
    },
    uploadFileList(obj) {
      uploadProcessFiles(obj.formData).then(async res => {
        obj.content.onSuccess(res, obj.content.file, []);
      }).catch(error => {
        obj.content.onError();
      });
    },
    // 初始化
    async initFun() {
      if (!this.gps.location && this.types == 'send') {
        Object.assign(this.appFormValue, this.rowData)
        Object.assign(this.appFormValue, {interfaceAdminName:this.rowData.companyTrueName,taskHostUnitName:this.rowData.companyName})
        Object.assign(this.changeFormValue, {feedbackPersonTrueName: this.rowData.taskInfoTrueName})
        console.log(this.appFormValue)
      } else {
        this.loadForm();
      }
    },
    // 获取工单详情
    loadForm(data) {
      var data = {
        pmInsId: this.gps.pmInsId || this.rowData.pmInsId,
        id: this.types == 'look' ? this.rowData.id : '',
      };
      conclusionDetail(data).then((res) => {
        this.appForm.formItemList[0].show = true
        this.appForm.formItemList[1].show = true
        Object.assign(this.appFormValue, res.data)
        this.changeFormValue.isExtension = res.data.isExtension?res.data.isExtension:'否'
        if (res.data.checkInfo) {
          Object.assign(this.changeFormValue, res.data.checkInfo)
        }
        if (this.gps.type == 'task' && this.gps.location == 'yqff.start') {

        } else {
          this.changeForm.formDisabled = true
          //单独处理上传附件
          for (var i = 0; i < this.changeForm.formItemList.length; i++) {
            if (this.changeForm.formItemList[i].type == "sbUpload") {
              this.changeForm.formItemList[i].disabled = true;
            }
          }
        }
        if (this.types == 'look') {
          Object.assign(this.gps, {pmInsId: res.data.pmInsId})
        }
      });
    },
    //封装验证函数
    submitForm(formUser) {
      return new Promise((resolve, reject) => {
        if (this.$refs[formUser]) {
          this.$refs[formUser].$children[0].validate((valid) => {
            if (valid) {
              resolve()
            } else {
              reject(new Error('错误'))
            }
          })
        } else {
          resolve()
        }
      })
    },
    // 流转下一步
    handleNextBtn(type) {
      Promise.all([this.submitForm('changeForm')])
          .then(() => {
            if (this.changeFormValue.progress?.length > 80 || this.changeFormValue.progress?.length < 20) {
              this.$message.warning('表单数据校验不通过')
              return
            }
            // if (this.changeFormValue.completionRate < 100) {
            //   this.$message.warning('任务未完成，无法结项。')
            //   return
            // }
            if (this.gps.location == 'leader' && type == 1) {
              this.$confirm('请确认是否归档？', '提示', {
                cancelButtonText: '关闭'
              }).then(async () => {
                let params = {
                  location: this.gps.location,
                  pmInsId: this.gps.pmInsId,
                  taskId: this.gps.taskId,
                  type: type,
                  nextUser: ''
                }
                let data = {
                  pmInsId: this.appFormValue.pmInsId,
                  id: this.appFormValue.id,
                  pmInsType: 'G',
                  title: this.appFormValue.title,
                  checkContentId: this.rowData.id,
                  checkInfo: Object.assign({}, this.changeFormValue),
                  interfaceAdminCode: this.appFormValue.interfaceAdminCode,
                  interfaceAdminName: this.appFormValue.interfaceAdminName
                }
                await conclusionSubmit(params, data)
                this.afterClick()
              }).catch(async () => {})

              return;
            }

            this.itemUser.type = type
            this.showUserDialog = true
          })
          .catch(() => {
            this.$message.warning('表单数据校验不通过')
          })
    },
    afterClick() {
      if (this.gps.myFrom && this.$router.currentRoute.path == "/workOrder") { //单点
        if(this.gps.flushPortalUrl){
            // 集团单点流转
            var flushPortalUrl = decodeURIComponent(this.gps.flushPortalUrl);
            var params = {
                "appcode": this.gps.appcode,
                "uniqueId": this.gps.uniqueId,
                "itemId": this.gps.itemId,
            }
            var pageUrlNew = this.util.toUrl(flushPortalUrl,params);
            window.location.replace(pageUrlNew)
          }else{
             window.opener = null;
            window.open("", "_self");
            window.close();

          }
      } else {
        if (!this.gps.location) {
          this.$router.push({
            name: "processTask"
          });
        } else {
          this.dialogClose();
        }
      }
    },
    // 确认
    handleConfirm() {
      // 判断流程下一步页面数据是否加载完
      var isLoad = this.$refs["processNext"].isLoad;
      // console.log(isLoad);

      if (!isLoad) {
        return false;
      }

      var decisionUser = this.$refs["processNext"].decisionUser;
      var decisionData = this.$refs["processNext"].decisionData;
      var choosedUser = this.$refs["processNext"].choosedUser;
      // console.log('已选决策项',JSON.parse(JSON.stringify(decisionData)))
      // console.log('人员',JSON.parse(JSON.stringify(decisionUser)))
      // console.log('已选人员',JSON.parse(JSON.stringify(choosedUser)))

      var flag = true;
      var gName = "";
      for (var i in decisionUser) {
        for (var j in decisionUser[i]) {
          if ((decisionUser[i][j].requSel === true || decisionUser[i][j].requSel === "true") && choosedUser[i][j].length == 0) {
            flag = false;
            if (i == "copy" && decisionUser[i][j].group != "normalGrouping") {
              gName = decisionData[i].decisionName.split("#")[1] + decisionUser[i][j].group;
            }
            break;
          }
        }
      }

      if ((!flag && decisionData["main"].decisionId.indexOf("_end") == -1) || !decisionUser.main.length) {
        this.$message({
          message: "清选择" + gName + "审批人",
          type: "warning",
          duration: 1500
        });
        return false;
      }

      var nextUser = [],
          nextUserName = [],
          nextUserOrgCode = [],
          nextUserPostId = [];
      for (var i in choosedUser["main"]) {
        for (var j in choosedUser["main"][i]) {
          nextUser.push(choosedUser["main"][i][j].id);
          nextUserName.push(choosedUser["main"][i][j].name);
          nextUserOrgCode.push(choosedUser["main"][i][j].parentId);
          nextUserPostId.push("123");
        }
      }

      var data = {
        appCode: process.env.VUE_APP_APPCODE,
        type: !this.gps.location || (this.gps.type == "draft" && this.gps.location == process.env.VUE_APP_APPCODE + ".start") ? "START" : "FLOW",
        title: this.appFormValue.title || "",
        processDefKey: this.gps.processDefKey || "",
        processDefId: this.gps.processDefKey || "",
        pmInsType: this.gps.pmInsType || "",
        outcome: decisionData["main"].decisionId,
        taskDefinitionKey: decisionData["main"].targetActivityDefId,
        message: this.$refs["processNext"].opinion,
        nextUser: nextUser.join(","),
        nextUserName: nextUserName.join(","),
        nextUserOrgCode: nextUserOrgCode.join(","),
        nextUserPostId: nextUserPostId.join(","),
        nextActivityParam: decisionData["main"].nextActivityParam,
        formData: this.formData
      };
      data.activityDefId = this.gps.location ? this.gps.location : process.env.VUE_APP_APPCODE + ".start";
      if (this.gps.taskId) data.taskId = this.gps.taskId;
      if (this.gps.processInstId) data.processInstId = this.gps.processInstId;
      // if(this.gps.taskDefinitionKey) data.taskDefinitionKey = this.gps.taskDefinitionKey;
      if (this.gps.processDefinitionId) data.processDefinitionId = this.gps.processDefinitionId;

      this.processD = false;
      startProcess(data).then((res) => {
        if (this.gps.myFrom && this.$router.currentRoute.path == "/workOrder") { //单点
         if(this.gps.flushPortalUrl){
            // 集团单点流转
            var flushPortalUrl = decodeURIComponent(this.gps.flushPortalUrl);
            var params = {
                "appcode": this.gps.appcode,
                "uniqueId": this.gps.uniqueId,
                "itemId": this.gps.itemId,
            }
            var pageUrlNew = this.util.toUrl(flushPortalUrl,params);
            window.location.replace(pageUrlNew)
          }else{
             window.opener = null;
            window.open("", "_self");
            window.close();

          }
        } else {
          if (!this.gps.location) {
            this.$router.push({
              name: "processTask"
            });
          } else {
            this.dialogClose();
          }
        }
      });
    },
    // 关闭
    handleOptClose() {
      if (this.dialogClose) {
        //待办打开
        this.dialogClose();
      } else if (this.gps.myFrom && this.$router.currentRoute.path == "/workOrder") {//单点
        //单点
        window.opener = null;
        window.open("", "_self");
        window.close();
      } else {
        // let item = this.tabnav.find(item => item.path === this.$route.path);
        // this.$store.dispatch("CloseTabnav", item).then(res => {
        //   if (item.path === this.$route.path) {
        //     const lastTag = res.slice(-1)[0];
        //     // 前一个 tab-view 页面存在，就跳；不存在就到首页
        //     if (lastTag) {
        //       this.$router.push({ path: lastTag.path });
        //     } else {
        //       this.$router.push({ path: "/mywork/processTask" });
        //     }
        //   }
        // });
      }
    },
    // 流程跟踪
    handleFlowTrack() {
      this.trackKey++;
      this.trackD = true;
    },
    // 查看意见
    handleViewComments() {
      this.doViewComments();
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }

};
</script>
<style scoped>
.f-1 {
  position: relative;
}

.f-2 {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 99;
  font-size: 12px;
  line-height: 26px;
  background: #fff;
  width: 100%;
  padding-left: 15px;
}

::v-deep .el-input__count {
  background: rgba(247, 234, 233, 0);
}

.red {
  color: rgba(192, 0, 0, 1);
  cursor: pointer;
}

::v-deep .tableCustom1 {
  position: relative;
}

.look-more {
  position: absolute;
  right: 5px;
  top: 0px;
  z-index: 999;
  font-size: 13px;
  cursor: pointer;
}


::v-deep .el-textarea__inner {
  padding-bottom: 25px;
}

::v-deep .tableCustom .el-textarea {
  //border: 1px solid #ebebeb;
  margin: 5px 0;
}

::v-deep .tableCustom .cell {
  white-space: normal;
  overflow: visible;
  text-overflow: inherit;
}

::v-deep .el-table .cell > .inlineC > .el-button {
  background: transparent;
  border: none;
  margin: 0;
  font-size: 14px;
  color: rgba(192, 0, 0, 1);
  padding: 7px 0px;
  white-space: normal;
}

::v-deep .el-input-group__append {
  background-color: #fff3f3;
  border-color: #fff3f3;
}

::v-deep .upload_D {
  min-width: 100%;
}

::v-deep .upload_Btn .uploadB {
  right: 0;
  left: auto;
}

.m-title {
  width: 100%;
  padding-left: 10px;
  border-left: 4px solid rgba(192, 0, 0, 1);
  padding: 10px;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  border-bottom: 1px solid #ebebeb;
  border-right: 1px solid #ebebeb;
}

.m-4 {
  height: 32px;
  display: flex;
  align-items: center;
  padding: 0;
  border-right: 1px solid #ebebeb;
  border-bottom: 1px solid #ebebeb;
}

.m-4:first-child {
  border-top: 1px solid #ebebeb;
}

::v-deep .m-4 .el-input__inner {
  height: 30px;
  line-height: 30px;
  border: none;
}

::v-deep .m-4 .el-input__icon {
  line-height: 30px;
}

.m-2 {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100px;
  height: 100%;
  background: #DDF1FE;
  padding: 0 10px;
  font-size: 14px;
  color: #606266;
  font-weight: bold;
}

.m-5 {
  flex: 1;
}
</style>