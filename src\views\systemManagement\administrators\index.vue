<template>
  <div class="app-container" style="padding-bottom: 80px;">
    <sb-el-table :table="table" @getList="getList" @updateTableData="updateTableData" :on-ok="handleDoFun" @handleAdd="handleAdd" @editHandle="editHandle"
                 @editHandleGetRow="editHandleGetRow" @handleExport="handleExport" @lookHandle="lookHandle" @removeHandle="removeHandle">
    </sb-el-table>
  </div>
</template>

<script>
import {adminadd, adminexport, adminlist, adminremove, adminupdate} from "@/api/process";

export default {
  data() {
    return {
      title: "",
      showDialog: false,
      curComponent: "",
      pageLabel: "taskInfo",
      table: {
        modulName: "管理员查询-管理员", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: true, // 是否有查询条件
        queryForm: {
          inline: true,
          labelPosition: 'right',
          labelWidth: "120px",
          formItemList: [
            {label: "单位名称", key: "belongCompanyName", type: "input", class: 'c3'},
            {label: "管理员姓名", key: "trueName", type: "input", class: 'c3'},
            {label: "管理员OA账号", key: "userName", type: "input", class: 'c3'},
          ],
        },
        tr: [
          {id: "belongCompanyName", label: "单位名称", prop: 'belongCompanyName'},
          {id: "belongDepartmentName", label: "部门名称", prop: 'belongDepartmentName'},
          {id: "trueName", label: "管理员姓名", prop: 'trueName'},
          {id: "userName", label: "管理员OA账号", prop: 'userName'},
          {id: "modifiedTime", label: "最后修改时间", prop: 'modifiedTime'}
        ],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "700px",
          labelWidth: "120px",
          inline: true,
          formItemList: [
            {
              class: 'c12', label: '管理员姓名', key: 'trueName', type: 'user',
              readonly: true, mulitple: false, stepLoad: true, appendShow: true, rule: { required: true },
              relevancy: "trueName-name,userName-id", defaultProps: {
                children: "children",
                label: "name",
                isLeaf: 'leaf',
              }, handleUser: 'chooseUser'
            },
            {class: "c12", label: "管理员OA账号", key: "userName", type: "input", disabled: true},
            {class: "c12", label: "单位名称", key: "belongCompanyName", type: "input", disabled: true},
            {class: "c12", label: "部门名称", key: "belongDepartmentName", type: "input", disabled: true}
          ],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: "200",
          fixed: "right",
          data: [
            // {id: "read", name: "【查看】", fun: "lookHandle"},
            {id: "update", name: "【编辑】", fun: "editHandle"},
            {id: "delete", name: "【删除】", fun: "removeHandle"},
            {id: "add", type: "danger", name: "新增", fun: "handleAdd"},
          ],
        },
        hasOtherQueryBtn: false, //是否有其他操作
        otherQueryBtn: {
          data: [
            {id: "export", type: "success", name: "导出", fun: "handleExport"}
          ]
        },
        hasPagination: true,
        listQuery: {page: 1, size: 10},
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
      optionsList: []
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async handleExport() {
      let res = await adminexport(this.table.listQuery)

      if (res.data) {
        this.util.blobDownload(res.data, res.filename);
      } else {
        this.$message({
          message: '导出失败',
          type: 'warning',
          duration: 1500
        });
      }
    },
    lookHandle(row) {
      Object.assign(this.table.listFormModul, row)
    },
    editHandleGetRow(row) {
      Object.assign(this.table.listFormModul, row)
    },
    async removeHandle(row, index) {
      await adminremove(row.id)
      // this.$message.success('删除成功')
      this.table.data.splice(index, 1)
    },
    async editHandle() {
      await adminupdate(this.table.listFormModul)
      // this.$message.success('修改管理员成功')
      this.table.dialogVisible = false
      this.getList()
    },
    async handleAdd() {
      await adminadd(this.table.listFormModul)
      // this.$message.success('新增管理员成功')
      this.table.dialogVisible = false
      this.getList()
    },
    chooseUser(item, arr) {
      let orgDisplayName = arr[0].orgDisplayName
      let list = orgDisplayName.split('\\')
      let obj = {
        belongCompanyName: list[0],
        belongDepartmentName: list.slice(1).join('/')
      }
      Object.assign(this.table.listFormModul, obj)
    },
    // 查询列表
    getList(listQuery) {
      this.table.loading = true;
      adminlist(listQuery || this.table.listQuery, listQuery).then((res) => {
        this.table.loading = false;
        res.data.content.forEach(item => {
          item.modifiedTime = this.util.getNow('yyyy-MM-dd hh:mm:ss', item.modifiedTime)
        })
        this.table.data = res.data.content;
        this.table.total = res.data.totalElements;
      }).catch((err) => {
        this.table.loading = false;
      });
    },
    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    },
  }
}
</script>

<style scoped>

::v-deep .inlineB .el-form-item__content {
  flex: 1;
}

.title {
  width: 99%;
  height: 30px;
  line-height: 30px;
  padding-left: 10px;
  border-left: #39aef5 4px solid;
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.titleName {
  font-size: 15px;
  color: #333;
  font-weight: 700;
}

::v-deep .table-container {
  /* 去除项目经历左右边距 */
  padding: 0;
}
</style>