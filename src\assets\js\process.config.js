// ABC代表的是流程类型,后边的是对应的需要跳转的页面路径
const appHtml = {
    "A":"views/expertTalents/invocation.vue",
	"C":"views/expertTalents/feedBack.vue",
    "D":"views/expertTalents/sendReport.vue",
    "E":"views/expertTalents/AiDateReport.vue",
    "F":"views/expertTalents/AiDateReport.vue",
    "G":"views/expertTalents/conclusionSend.vue",
    "H":"views/branchCompany/expertTalents/feedBack.vue",
    "I":"views/branchCompany/expertTalents/conclusionSend.vue",
    "J":"views/branchCompany/expertTalents/AiDateReport.vue",
    "K":"views/branchCompany/expertTalents/AiDateReport.vue",
    "L":"views/expertTalents/AiDateReport.vue",
}

// ABC代表的是流程类型,后边的是对应的是流程名，会显示在详情页面的头部
const appName = {
    "A":"派发流程",
	"C":"反馈流程",
    "D": "通报流程",
    "E": "AI周报流程",
    "F": "AI周报流程",
    "G": "结项流程",
	"H":"分公司反馈流程",
	"I":"分公司结项流程",
    "J": "AI周报流程",
    "K": "AI周报流程",
    "L": "",



}

export { appHtml,appName }
