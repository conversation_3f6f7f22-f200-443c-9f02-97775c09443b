import request from "@/assets/js/request";

// 获取用户列表
export function getUserAll(params) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/role/findAllInfo`,
    contentType: "application/json; charset=utf-8",
    data: params
  });
}
//新增用户
export function addUser(params) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/role/saveInfo`,
    contentType: "application/json; charset=utf-8",
    data: params
  });
}
//删除用户
export function deleteUserById(id) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/role/deleteInfo?id=${id}`,
    contentType: "application/json; charset=utf-8",
    // data: params
  });
}
//查询公司列表
export function findPOrgAndCityOrg() {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/uums/sys/org/findPOrgAndCityOrg?appcode=${process.env.VUE_APP_APPCODE}`
  });
}
export function findOrgByUserMap() {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/uums/sys/org/findOrgByUserMap?appcode=${process.env.VUE_APP_APPCODE}`
  });
}

// 导出
export function exportParameter(params) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/action/common/exportMCExcel`,
    contentType: "application/json; charset=utf-8",
    data: params,
    responseType: "blob"
  });
}







