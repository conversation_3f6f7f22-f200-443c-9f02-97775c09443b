<template>
  <div class="app-container" style="padding-bottom: 80px;">
    <sb-el-table :table="table" @getList="getList" @updateTableData="updateTableData" :on-ok="handleDoFun">
      <template #taskHostUnitCode>
        <el-select v-model="table.listQuery.taskHostUnitCode" placeholder="请选择" @change="changeTr">
          <el-option v-for="(item, index) in optionsList" :key="index" :label="item.name" :value="item.value"></el-option>
        </el-select>
      </template>
    </sb-el-table>
  </div>
</template>

<script>
import {hisoptions, historylist} from "@/api/process";

export default {
  props: {
    gps: Object,
    targetId: [String, Number],
    type: [String, Number],
    taskHostUnitCode: {}
  },
  data() {
    return {
      title: "",
      showDialog: false,
      curComponent: "",
      pageLabel: "taskInfo",
      table: {
        modulName: "列表查询", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: (this.gps.location == 'fillIn' || this.gps.location == 'host') && this.type == 1, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "110px",
          formItemList: [
            {label: "填报内容筛选", key: "taskHostUnitCode", type: "template", template: 'taskHostUnitCode'},
          ],
        },
        tr: [
          {
            id: "time",
            label: "填报时间",
            prop: '',
            width: 160
          },
          {
            id: "name",
            label: "填报人",
            prop: '',
            width: 140
          },
          {
            id: "projectType",
            label: "填报内容",
            prop: ''
          }
        ],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "400px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: false, //是否有操作列表
        operation: {
          width: "160",
          fixed: "right",
          data: [
            {id: "lookHandle", name: "【选择】", fun: "lookHandle"},
          ],
        },
        hasOtherQueryBtn: false, //是否有其他操作
        otherQueryBtn: {
          data: [
            {id: "export", type: "success", name: "导出", fun: "handleExport"}
          ]
        },
        hasPagination: false,
        listQuery: {taskHostUnitCode: this.taskHostUnitCode, page: 1, size: 10},
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
      optionsList: []
    }
  },
  created() {
    this.getList()
    if ((this.gps.location == 'fillIn' || this.gps.location == 'host') && this.type == 1) {
      this.getOptions()
    }
    this.changeTr()
  },
  methods: {
    changeTr() {
      if (this.table.listQuery.taskHostUnitCode) {
        ['taskReportTime', 'taskReportTrueName', 'taskReportContent'].forEach((item, index) => {
          this.table.tr[index].prop = item
        })
      } else {
        ['reportTime', 'reportTrueName', 'reportContent'].forEach((item, index) => {
          this.table.tr[index].prop = item
        })
      }
      this.$forceUpdate()
    },
    async getOptions() {
      let res = await hisoptions({pmInsId: this.gps.pmInsId, targetId: this.targetId})
      this.optionsList = res.data.map(item => {
        return {
          name: item.taskHostUnitName + '填报内容',
          value: item.taskHostUnitCode
        }
      })
      this.optionsList.unshift({
        name: '牵头部门汇总内容',
        value: 0
      })
    },
    lookHandle(data) {
      this.$emit('getDetail', data.row)
    },
    // 查询列表
    getList() {
      this.table.loading = true;
      let params = Object.assign({taskHostUnitCode: 0}, this.table.listQuery, {pmInsId: this.gps.pmInsId, targetId: this.targetId})
      historylist(params).then((res) => {
        this.table.loading = false;
        this.table.data = res.data;
      }).catch((err) => {
        this.table.loading = false;
      });
    },
    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    },
  }
}
</script>

<style scoped>
.title {
  width: 99%;
  height: 30px;
  line-height: 30px;
  padding-left: 10px;
  border-left: #39aef5 4px solid;
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.titleName {
  font-size: 15px;
  color: #333;
  font-weight: 700;
}

::v-deep .table-container {
  /* 去除项目经历左右边距 */
  padding: 0;
}
</style>